<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:clipChildren="false"
    android:clipToPadding="false">

    <TextView
        android:id="@+id/tv_tab_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        tools:text="12345"
        android:singleLine="true"/>

    <com.flyco.tablayout.widget.MsgView
        android:id="@+id/rtv_msg_tip"
        xmlns:mv="http://schemas.android.com/apk/res-auto"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:gravity="center"
        android:textColor="#ffffff"
        android:textSize="10dp"
        android:visibility="gone"
        mv:mv_backgroundColor="#FF2121"
        mv:mv_isRadiusHalfHeight="true"
        mv:mv_isWidthHeightEqual="true"/>

</RelativeLayout>