<?xml version="1.0" encoding="UTF-8"?>
<issues format="5" by="lint 3.6.3" client="gradle" variant="release" version="3.6.3">

    <issue
        id="ObsoleteLintCustomCheck"
        message="<PERSON><PERSON> found an issue registry (`androidx.appcompat.AppCompatIssueRegistry`) which requires a newer API level. That means that the custom lint checks are intended for a newer lint version; please upgrade">
        <location
            file="../../../../../.gradle/caches/transforms-2/files-2.1/4bcbea224e1f5a97ce3a89a5d02de9cd/appcompat-1.2.0/jars/lint.jar"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (!TextUtils.isEmpty(ver) &amp;&amp; ver.toLowerCase().contains(EMUI_HOST)) {"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="88"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (!TextUtils.isEmpty(ver) &amp;&amp; ver.toLowerCase().contains(EMUI_HOST)) {"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="100"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (!TextUtils.isEmpty(ver) &amp;&amp; ver.toLowerCase().contains(COLOR_HOST)) {"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="114"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (!TextUtils.isEmpty(ver) &amp;&amp; ver.toLowerCase().contains(COLOR_HOST)) {"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="126"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (!TextUtils.isEmpty(ver) &amp;&amp; ver.toLowerCase().contains(FUNTOUCH_HOST)) {"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="139"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (!TextUtils.isEmpty(ver) &amp;&amp; ver.toLowerCase().contains(FUNTOUCH_HOST)) {"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="151"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (!TextUtils.isEmpty(ver) &amp;&amp; ver.toLowerCase().contains(FLYME_HOST)) {"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="165"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (!TextUtils.isEmpty(ver) &amp;&amp; ver.toLowerCase().contains(FLYME_HOST)) {"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="177"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (SAMSUNG_HOST.equals(Build.BRAND.toLowerCase()) &amp;&amp; Build.BRAND.toLowerCase().equals(getSystemProperty(SAMSUNG_VER))) {"
        errorLine2="                                            ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="190"
            column="45"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (SAMSUNG_HOST.equals(Build.BRAND.toLowerCase()) &amp;&amp; Build.BRAND.toLowerCase().equals(getSystemProperty(SAMSUNG_VER))) {"
        errorLine2="                                                                          ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="190"
            column="75"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (!TextUtils.isEmpty(ver) &amp;&amp; ver.toLowerCase().contains(H2_HOST)) {"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="239"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (Build.HOST.toLowerCase().contains(CM_HOST) &amp;&amp; !TextUtils.isEmpty(getSystemProperty(CM_VER))) {"
        errorLine2="                       ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="251"
            column="24"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        String host = Build.HOST.toLowerCase();"
        errorLine2="                                 ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="275"
            column="34"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        if (host.contains(GOOGLE_HOST) &amp;&amp; !TextUtils.isEmpty(ver) &amp;&amp; ver.toLowerCase().contains(GOOGLE_HOST)) {"
        errorLine2="                                                                         ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/RomUtils.java"
            line="277"
            column="74"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="                        if (old.length() > 1024 &amp;&amp; old.getName().toLowerCase().endsWith(SUFFIX_PATCH)) {//文件大于1024b"
        errorLine2="                                                                 ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/common/UpdateManager.java"
            line="536"
            column="66"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="                        if (old.length() > 3 * 1024 * 1024 &amp;&amp; old.getName().toLowerCase().endsWith(SUFFIX)) {//文件大于3MB"
        errorLine2="                                                                            ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/common/UpdateManager.java"
            line="540"
            column="77"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="             extensionName = fileName.substring(fileName.lastIndexOf(&quot;.&quot;) + 1).toLowerCase();"
        errorLine2="                                                                               ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/common/UrlCacheManager.java"
            line="165"
            column="80"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            extensionName = fileName.substring(fileName.lastIndexOf(&quot;.&quot;) + 1).toLowerCase();"
        errorLine2="                                                                              ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/common/UrlCacheManager.java"
            line="179"
            column="79"/>
    </issue>

    <issue
        id="ShowToast"
        message="Toast created but not shown: did you forget to call `show()` ?"
        errorLine1="            toast = Toast.makeText(getContext(), text, isLong ? Toast.LENGTH_LONG : Toast.LENGTH_SHORT);"
        errorLine2="                    ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/UiUtils.java"
            line="287"
            column="21"/>
    </issue>

    <issue
        id="SimpleDateFormat"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates."
        errorLine1="        SimpleDateFormat format = new SimpleDateFormat("
        errorLine2="                                  ^">
        <location
            file="src/main/java/com/ybm/app/common/NtpTrustedTime.java"
            line="136"
            column="35"/>
    </issue>

    <issue
        id="SimpleDateFormat"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates."
        errorLine1="        SimpleDateFormat format = new SimpleDateFormat(&quot;yyyy-MM-dd HH:mm:ss&quot;);"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/bean/TimeLog.java"
            line="83"
            column="35"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="        root = inflater.inflate(R.layout.dialog, null);"
        errorLine2="                                                 ~~~~">
        <location
            file="src/main/java/com/ybm/app/common/BaseDialog.java"
            line="38"
            column="50"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="        View view = inflater.inflate(R.layout.window_toast,null);"
        errorLine2="                                                           ~~~~">
        <location
            file="src/main/java/com/ybm/app/common/WindowToast/ToastTips.java"
            line="170"
            column="60"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="            View v = inflater.inflate(R.layout.progress, null);"
        errorLine2="                                                         ~~~~">
        <location
            file="src/main/java/com/ybm/app/common/UpdateManager.java"
            line="145"
            column="58"/>
    </issue>

    <issue
        id="GradleOverrides"
        message="This `versionCode` value (`1`) is not used; it is always overridden by the value specified in the Gradle build script (`1`)"
        errorLine1="    android:versionCode=&quot;1&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="4"
            column="5"/>
    </issue>

    <issue
        id="GradleOverrides"
        message="This `versionName` value (`1.0.0`) is not used; it is always overridden by the value specified in the Gradle build script (`1.0.1`)"
        errorLine1="    android:versionName=&quot;1.0.0&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="SpUsage"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        errorLine1="        android:textSize=&quot;14dp&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/view_loadmore.xml"
            line="16"
            column="9"/>
    </issue>

    <issue
        id="SwitchIntDef"
        message="Switch statement on an `int` with known associated constant missing case `TelephonyManager.NETWORK_TYPE_GSM`, `TelephonyManager.NETWORK_TYPE_IWLAN`, `TelephonyManager.NETWORK_TYPE_TD_SCDMA`"
        errorLine1="            switch (telephonyManager.getNetworkType()) {"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/NetUtil.java"
            line="136"
            column="13"/>
    </issue>

    <issue
        id="PackageManagerGetSignatures"
        message="Reading app signatures from getPackageInfo: The app signatures could be exploited if not validated properly; see issue explanation for details."
        errorLine1="            Signature appSig = BaseYBMApp.getAppContext().getPackageManager().getPackageInfo(BaseYBMApp.getAppContext().getPackageName(), PackageManager.GET_SIGNATURES).signatures[0];"
        errorLine2="                                                                                                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/common/UpdateManager.java"
            line="775"
            column="139"/>
    </issue>

    <issue
        id="PackageManagerGetSignatures"
        message="Reading app signatures from getPackageInfo: The app signatures could be exploited if not validated properly; see issue explanation for details."
        errorLine1="                context.getPackageName(), PackageManager.GET_SIGNATURES).signatures[0];"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/Utils.java"
            line="119"
            column="43"/>
    </issue>

    <issue
        id="BadHostnameVerifier"
        message="`verify` always returns `true`, which could cause insecure network traffic due to trusting TLS/SSL server certificates for wrong hostnames"
        errorLine1="                    public boolean verify(String hostname, SSLSession session) {"
        errorLine2="                                   ~~~~~~">
        <location
            file="src/main/java/com/ybm/app/common/download/DownloadManager.java"
            line="55"
            column="36"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        message="`checkClientTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers"
        errorLine1="                public void checkClientTrusted("
        errorLine2="                            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/common/download/TrustManager.java"
            line="26"
            column="29"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        message="`checkServerTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers"
        errorLine1="                public void checkServerTrusted("
        errorLine2="                            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/common/download/TrustManager.java"
            line="32"
            column="29"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/common/BaseDialog.java"
            line="113"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/common/BaseDialog.java"
            line="118"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/common/BaseDialog.java"
            line="126"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is never &lt; 21"
        errorLine1="        if (Build.VERSION.SDK_INT &lt;= Build.VERSION_CODES.GINGERBREAD_MR1) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/bean/DeviceEntity.java"
            line="89"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/bean/DeviceEntity.java"
            line="124"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is never &lt; 21"
        errorLine1="        if ((android.os.Build.VERSION.SDK_INT &lt; 21 &amp;&amp; mTarget instanceof AbsListView)"
        errorLine2="             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/view/refresh/RecyclerRefreshLayout.java"
            line="409"
            column="14"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is never &lt; 21"
        errorLine1="        if (android.os.Build.VERSION.SDK_INT &lt; 14 &amp;&amp; mTarget instanceof AbsListView) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/view/refresh/RecyclerRefreshLayout.java"
            line="1222"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="            String methodName = Build.VERSION.SDK_INT >= 18 ? &quot;currentProcessName&quot; : &quot;currentPackageName&quot;;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/utils/Utils.java"
            line="65"
            column="33"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `BaseYBMApp` which has field `currActivity` pointing to `Activity`); this is a memory leak"
        errorLine1="    private static BaseYBMApp appLike = null;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/ybm/app/common/BaseYBMApp.java"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="HandlerLeak"
        message="This Handler class should be static or leaks might occur (anonymous android.os.Handler)"
        errorLine1="    private Handler mHandler = new Handler() {"
        errorLine2="                               ^">
        <location
            file="src/main/java/com/ybm/app/common/UpdateManager.java"
            line="227"
            column="32"/>
    </issue>

    <issue
        id="InefficientWeight"
        message="Use a `layout_width` of `0dp` instead of `50dp` for better performance"
        errorLine1="        android:layout_width=&quot;50dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/window_toast.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="        &lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/line_bg_top_radius.xml"
            line="4"
            column="16"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;loading_more&quot;>正在加载中...&lt;/string>"
        errorLine2="                                ^">
        <location
            file="src/main/res/values/strings.xml"
            line="3"
            column="33"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_00.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_00.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_01.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_01.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_02.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_02.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_03.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_03.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_04.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_04.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_05.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_05.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_06.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_06.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_07.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_07.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_08.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_08.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_09.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_09.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_10.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_10.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_11.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_11.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_12.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_12.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_13.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_13.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_14.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_14.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_15.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_15.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_16.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_16.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_17.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_17.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_18.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_18.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_19.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_19.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_20.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_20.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_21.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_21.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_22.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_22.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_23.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_23.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_24.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_24.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_25.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_25.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_26.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_26.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_27.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_27.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_28.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_28.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_29.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_29.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_30.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_30.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_31.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_31.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_32.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_32.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_33.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_33.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_34.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_34.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_35.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_35.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_36.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_36.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_37.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_37.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_38.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_38.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_39.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_39.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_40.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_40.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_41.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_41.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_42.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_42.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_43.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_43.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_44.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_44.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_45.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_45.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_46.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_46.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_47.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_47.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_48.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_48.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_49.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_49.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_50.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_50.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_51.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_51.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_52.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_52.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_53.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_53.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_54.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_54.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/sx_55.png` in densityless folder">
        <location
            file="src/main/res/drawable/sx_55.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        message="The following unrelated icon files have identical contents: sx_00.png, sx_01.png">
        <location
            file="src/main/res/drawable/sx_01.png"/>
        <location
            file="src/main/res/drawable/sx_00.png"/>
    </issue>

    <issue
        id="IconMissingDensityFolder"
        message="Missing density variation folders in `src/main/res`: drawable-hdpi, drawable-mdpi, drawable-xhdpi">
        <location
            file="src/main/res"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="Custom view `RecyclerRefreshLayout` overrides `onTouchEvent` but not `performClick`"
        errorLine1="    public boolean onTouchEvent(MotionEvent ev) {"
        errorLine2="                   ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybm/app/view/refresh/RecyclerRefreshLayout.java"
            line="779"
            column="20"/>
    </issue>

</issues>
