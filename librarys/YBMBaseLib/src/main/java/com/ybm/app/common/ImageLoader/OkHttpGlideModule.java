package com.ybm.app.common.ImageLoader;

import android.content.Context;
import android.os.Build;

import com.bumptech.glide.Glide;
import com.bumptech.glide.GlideBuilder;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.bitmap_recycle.LruBitmapPool;
import com.bumptech.glide.load.engine.cache.InternalCacheDiskCacheFactory;
import com.bumptech.glide.load.engine.cache.LruResourceCache;
import com.bumptech.glide.load.engine.cache.MemorySizeCalculator;
import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.module.GlideModule;

import java.io.InputStream;

/**
 * Created by Administrator on 2016/8/2.
 */
public class OkHttpGlideModule implements GlideModule {
    private int DEFAULT_DISK_CACHE_SIZE = 400 * 1024 * 1024; //500MB 缓存
    private float con = 1.2f;
    @Override
    public void applyOptions(Context context, GlideBuilder builder) {
        if(Build.VERSION.SDK_INT>=Build.VERSION_CODES.N){
            builder.setDecodeFormat(DecodeFormat.PREFER_ARGB_8888);
        }else {
            builder.setDecodeFormat(DecodeFormat.DEFAULT);
        }
        MemorySizeCalculator calculator = new MemorySizeCalculator(context);
        int defaultMemoryCacheSize = calculator.getMemoryCacheSize();
        int defaultBitmapPoolSize = calculator.getBitmapPoolSize();
        builder.setMemoryCache( new LruResourceCache((int) (defaultMemoryCacheSize*con)));
        builder.setBitmapPool( new LruBitmapPool((int) (defaultBitmapPoolSize*con)));
        builder.setDiskCache(new InternalCacheDiskCacheFactory(context,DEFAULT_DISK_CACHE_SIZE));
    }

    @Override
    public void registerComponents(Context context, Glide glide) {
        glide.register(GlideUrl.class, InputStream.class, new OkHttpUrlLoader.Factory());
    }
}