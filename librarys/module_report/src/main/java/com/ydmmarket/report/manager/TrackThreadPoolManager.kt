package com.ydmmarket.report.manager

import java.util.concurrent.Callable
import java.util.concurrent.ExecutorService
import java.util.concurrent.Future
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor

object TrackThreadPoolManager {

    val mThreadPool: ExecutorService by lazy {
        ThreadPoolExecutor(10, 20, 60, java.util.concurrent.TimeUnit.SECONDS, LinkedBlockingQueue())
    }


    fun submit(task:Callable<String>): Future<String> {
        return mThreadPool.submit(task)
    }

}