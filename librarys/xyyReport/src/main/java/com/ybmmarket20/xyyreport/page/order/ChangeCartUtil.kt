package com.ybmmarket20.xyyreport.page.order

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ybmmarket20.xyyreport.page.cart.CartReportConstant
import com.ybmmarket20.xyyreport.page.common.addCart.AddCartPopupWindowConstant
import com.ybmmarket20.xyyreport.paramsInfo.IRowsBeanInfo
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmExtensionConstant
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.TrackData
import com.ybmmarket20.xyyreport.spm.XyyReportActivity

object ChangeCartUtil {

    @JvmStatic
    fun getChangeCartParams(context: Context, rowsBeanInfo: IRowsBeanInfo?): String? {
        return try {
            if (context !is XyyReportActivity) return null
            if (rowsBeanInfo?.onOpGoods() == true) return null
            val spm = (context.getExtensionValue(AddCartPopupWindowConstant.EXTENSION_ADD_CART_ACTION_ITEM_SPM) as? SpmBean)?.newInstance()
            val scm = (context.getExtensionValue(AddCartPopupWindowConstant.EXTENSION_ADD_CART_ACTION_ITEM_SCM) as? ScmBean)?.newInstance()
            if (spm == null || scm == null) return null
            //spmC(组件是“0”)则不需要买点
            if (spm.spmC == "0") return null
            val qtListData = rowsBeanInfo?.getQtListData()
            val qtSkuData = rowsBeanInfo?.getSpmQtSkuData()
            val map = mutableMapOf<String, Any>(
                "spm_cnt" to spm.concat(),
                "scm_cnt" to scm.concat(),
            )
            if (qtListData != null && qtSkuData != null) {
                val qtListDataMap = Gson().fromJson<Map<String, Any?>>(qtListData, object: TypeToken<Map<String, Any?>>() {}.type)
                map["qt_list_data"] = qtListDataMap
                val qtSkuDataMap = Gson().fromJson<Map<String, Any?>>(qtSkuData, object: TypeToken<Map<String, Any?>>() {}.type)
                map["qt_sku_data"] = qtSkuDataMap
            }
            Gson().toJson(map)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 获取再次购买Spm
     */
    private fun getBuyAgainSpmParams(context: Context): TrackData? {
        return SpmUtil.checkAnalysisContextReturn(context) {
            val spm = it.getExtensionValue(OrderConstant.ORDER_ACTION_BUY_AGAIN_SPM_CNT)
            val scm = it.getExtensionValue(OrderConstant.ORDER_ACTION_BUY_AGAIN_SCM_CNT)
            if (spm != null && scm != null && spm is SpmBean && scm is ScmBean) {
                TrackData(spm, scm)
            } else null
        }
    }

    @JvmStatic
    fun addQtDataToParams(context: Context, p: Map<String, String>?): Map<String, String>? {
        p?: return p
        val spmP = getBuyAgainSpmParams(context)
        val map = mutableMapOf<String, Any>(
            "spm_cnt" to (spmP?.spmEntity?.concat()?: ""),
            "scm_cnt" to (spmP?.scmEntity?.concat()?: ""),
        )
        val qtData = Gson().toJson(map)
        return p.toMutableMap().apply {
            put("qtdata", qtData)
        }
    }

}