package com.ybmmarket20.xyyreport.page.orderDetail

import android.content.Context
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.order.OrderConstant
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

object OrderDetailReport {

    @JvmStatic
    fun pvTrack(context: Context, orderNo: String?) {
        val spm = SpmUtil.getSpmPv("orderDetail_${orderNo}-0_0")
        SpmLogUtil.print("订单详情-PV")
        ReportUtil.pvTrack(context, ReportPageExposureBean(), spm)
        SpmUtil.checkAnalysisContext(context) {
            it.putExtension(OrderConstant.ORDER_ACTION_PAGE_IS_LIST, false)
        }
    }

    @JvmStatic
    fun trackBuyAgainClick(context: Context) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "ftorderDetail@Z"
                spmD = "btn@2"
            }
            val scm = ScmBean(
                "order",
                "0",
                "all_0",
                "text-再次购买",
                null
            )
            ReportUtil.clickTrack(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }
}