<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingEnd="@dimen/dimen_dp_15"
    android:paddingBottom="@dimen/dimen_dp_14"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RadioButton
        android:id="@+id/tv_refund_reason_child_child"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="false"
        android:paddingTop="@dimen/dimen_dp_2"
        android:paddingBottom="@dimen/dimen_dp_2"
        android:paddingStart="@dimen/dimen_dp_9"
        android:paddingEnd="@dimen/dimen_dp_9"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/selector_refund_reason_child_child_bg"
        android:text="价格高"
        android:textColor="@color/selector_refund_reason_child_child_color"
        android:textSize="@dimen/dimen_dp_12"
        android:button="@null" />
</androidx.constraintlayout.widget.ConstraintLayout>