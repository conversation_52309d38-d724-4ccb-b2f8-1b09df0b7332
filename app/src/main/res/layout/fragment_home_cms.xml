<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    android:id="@+id/fl_home_cms"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <View
        android:id="@+id/home_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include layout="@layout/home_search_cms" />

        <com.ybm.app.view.refresh.RecyclerRefreshLayout
            android:id="@+id/rfl_refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.ybmmarket20.view.homeview.SnapNestedSCrollView
                android:id="@+id/home_scrollview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fadingEdge="none"
                android:fillViewport="true"
                android:overScrollMode="never"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:descendantFocusability="blocksDescendants"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:orientation="vertical">

                    <com.ybmmarket20.view.cms.DynamicHomeLayoutCms
                        android:id="@+id/home"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_scrollFlags="scroll" />

                    <com.flyco.tablayout.SlidingTabLayout
                        android:id="@+id/tab_layout"
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        app:tl_indicator_color="@color/home_back_selected"
                        app:tl_indicator_corner_radius="2dp"
                        app:tl_indicator_height="4dp"
                        app:tl_indicator_width="30dp"
                        app:tl_tab_space_equal="true"
                        app:tl_textAllCaps="true"
                        app:tl_textBold="SELECT"
                        app:tl_textSelectColor="@color/text_292933"
                        app:tl_textSelectSize="17sp"
                        app:tl_textUnselectColor="@color/text_676773"
                        app:tl_textsize="15sp" />

                    <LinearLayout
                        android:id="@+id/view_shadow"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_tab_shadow" />

                    </LinearLayout>


                    <androidx.viewpager.widget.ViewPager
                        android:id="@+id/vp_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </com.ybmmarket20.view.homeview.SnapNestedSCrollView>
        </com.ybm.app.view.refresh.RecyclerRefreshLayout>

    </LinearLayout>


    <ImageView
        android:id="@+id/iv_fastscroll"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_gravity="bottom|right"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="60dp"
        android:src="@drawable/fastscroll"
        android:visibility="invisible" />

    <ImageView
        android:id="@+id/iv_ad_suspension"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_gravity="bottom|right"
        android:layout_marginBottom="110dp"
        android:src="@drawable/icon_ad_suspension"
        android:visibility="invisible" />

    <com.ybmmarket20.view.DialImageView
        android:id="@+id/iv_dial_suspension"
        android:layout_width="114dp"
        android:layout_height="76dp"
        android:layout_gravity="bottom|right"
        android:layout_marginBottom="90dp"
        android:src="@drawable/transparent"
        android:visibility="gone" />

</FrameLayout>
