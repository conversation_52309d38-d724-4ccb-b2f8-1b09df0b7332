<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:background="@color/white"
    android:layout_height="wrap_content">

    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:id="@+id/ll_item_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:paddingHorizontal="0.5dp"
        android:paddingBottom="1dp"
        app:rv_backgroundColor="@color/color_ff2121"
        app:rv_cornerRadius="10dp">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="15dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/icon_goods_details_limited_time_premium"
            app:layout_constraintBottom_toTopOf="@id/cl_good_root"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_time_hundred_ms"
            android:layout_width="wrap_content"
            android:layout_height="19dp"
            android:layout_marginEnd="6dp"
            android:gravity="center"
            android:minWidth="19dp"
            android:paddingHorizontal="1dp"
            android:textColor="@color/color_ff2121"
            android:textSize="13dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_end"
            app:layout_constraintEnd_toStartOf="@id/tv_end"
            app:layout_constraintTop_toTopOf="@id/tv_end"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="2dp"
            tools:text="9" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_time_s"
            android:layout_width="wrap_content"
            android:layout_height="19dp"
            android:layout_marginEnd="9dp"
            android:gravity="center"
            android:minWidth="19dp"
            android:paddingHorizontal="1dp"
            android:textColor="@color/color_ff2121"
            android:textSize="13dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_hundred_ms"
            app:layout_constraintEnd_toStartOf="@id/tv_time_hundred_ms"
            app:layout_constraintTop_toTopOf="@id/tv_time_hundred_ms"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="2dp"
            tools:text="49" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_time_min"
            android:layout_width="wrap_content"
            android:layout_height="19dp"
            android:layout_marginEnd="9dp"
            android:gravity="center"
            android:minWidth="19dp"
            android:paddingHorizontal="1dp"
            android:textColor="@color/color_ff2121"
            android:textSize="13dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_s"
            app:layout_constraintEnd_toStartOf="@id/tv_time_s"
            app:layout_constraintTop_toTopOf="@id/tv_time_s"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="2dp"
            tools:text="999" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_time_h"
            android:layout_width="wrap_content"
            android:layout_height="19dp"
            android:layout_marginEnd="9dp"
            android:gravity="center"
            android:minWidth="19dp"
            android:paddingHorizontal="1dp"
            android:textColor="@color/color_ff2121"
            android:textSize="13dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_min"
            app:layout_constraintEnd_toStartOf="@id/tv_time_min"
            app:layout_constraintTop_toTopOf="@id/tv_time_min"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="2dp"
            tools:text="9999" />

        <TextView
            android:id="@+id/tv_end"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:text="后结束"
            android:textColor="@color/white"
            android:textSize="12dp"
            app:layout_constraintBottom_toTopOf="@id/cl_good_root"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_mark_ms_s"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="."
            android:textColor="@color/white"
            android:textSize="13dp"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_time_hundred_ms"
            app:layout_constraintEnd_toStartOf="@id/tv_time_hundred_ms"
            app:layout_constraintStart_toEndOf="@id/tv_time_s" />

        <TextView
            android:id="@+id/tv_mark_min_s"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=":"
            android:textColor="@color/white"
            android:textSize="13dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_s"
            app:layout_constraintEnd_toStartOf="@id/tv_time_s"
            app:layout_constraintStart_toEndOf="@id/tv_time_min"
            app:layout_constraintTop_toTopOf="@id/tv_time_s" />

        <TextView
            android:id="@+id/tv_mark_h_min"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=":"
            android:textColor="@color/white"
            android:textSize="13dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_min"
            app:layout_constraintEnd_toStartOf="@id/tv_time_min"
            app:layout_constraintStart_toEndOf="@id/tv_time_h"
            app:layout_constraintTop_toTopOf="@id/tv_time_min" />


        <com.ybmmarket20.common.widget.RoundConstraintLayout
            android:id="@+id/cl_good_root"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="2dp"
            android:layout_marginTop="26dp"
            android:layout_marginBottom="2dp"
            android:background="@color/white"
            android:paddingLeft="@dimen/dimen_dp_3"
            android:paddingTop="@dimen/dimen_dp_9"
            android:paddingRight="@dimen/dimen_dp_8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="8dp">

            <ImageView
                android:id="@+id/icon"
                android:layout_width="@dimen/pic_goods_list_big"
                android:layout_height="@dimen/pic_goods_list_big"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_shop_mark"
                android:layout_width="@dimen/pic_goods_list_big"
                android:layout_height="@dimen/pic_goods_list_big"
                app:layout_constraintLeft_toLeftOf="@id/icon"
                app:layout_constraintTop_toTopOf="@id/icon" />

            <com.ybmmarket20.view.PromotionTagView
                android:id="@+id/view_ptv"
                android:layout_width="@dimen/pic_goods_list_big"
                android:layout_height="@dimen/pic_goods_list_big"
                app:contentTextSize="7dp"
                app:layout_constraintLeft_toLeftOf="@id/icon"
                app:layout_constraintTop_toTopOf="@id/icon"
                app:shopBottomTextSize="6dp"
                app:shopContentTextSize="9dp"
                app:shopTimeTextSize="6dp"
                app:shopTopTextSize="@dimen/dimen_dp_7"
                app:subTitleTextSize="4dp" />

            <TextView
                android:id="@+id/shop_no_limit_tv01"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:background="@drawable/bg_goods_sold_out"
                android:gravity="center"
                android:textColor="@color/color_fdfdfd"
                android:textSize="@dimen/dimen_dp_13"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/icon"
                app:layout_constraintLeft_toLeftOf="@id/icon"
                app:layout_constraintRight_toRightOf="@id/icon"
                app:layout_constraintTop_toTopOf="@id/icon"
                tools:text="售罄"
                tools:visibility="invisible" />

            <TextView
                android:id="@+id/rtv_buy_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dimen_dp_10"
                android:paddingStart="3dp"
                android:paddingEnd="3dp"
                android:textColor="@color/text_676773"
                android:textSize="@dimen/dimen_dp_10"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="@id/icon"
                app:layout_constraintRight_toRightOf="@id/icon"
                app:layout_constraintTop_toBottomOf="@id/icon"
                tools:background="@color/base_colors"
                tools:text="买过1次"
                tools:visibility="visible" />

            <!--  商品信息区 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_goods_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_dp_5"
                app:layout_constraintLeft_toRightOf="@id/icon"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <!--商品名字  如 阿莫西林胶囊-->
                <TextView
                    android:id="@+id/shop_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/color_292933"
                    android:textSize="@dimen/dimen_dp_14"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="【拼团】20盒包邮 念慈菴蜜炼川贝枇杷膏/12颗*1盒" />

                <!--商品规格-->
                <TextView
                    android:id="@+id/tv_goods_spec"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_dp_2"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/colors_676773"
                    android:textSize="@dimen/dimen_dp_12"
                    app:layout_constraintLeft_toLeftOf="@id/shop_name"
                    app:layout_constraintTop_toBottomOf="@id/shop_name"
                    tools:text="2.5mg*7s*2板"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/iv_divider_of_spec_name"
                    android:layout_width="1dp"
                    android:layout_height="0dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="3dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="3dp"
                    android:background="@drawable/divider_line_vertial_spec_goodsname"
                    app:layout_constraintBottom_toBottomOf="@id/tv_goods_spec"
                    app:layout_constraintLeft_toRightOf="@id/tv_goods_spec"
                    app:layout_constraintRight_toLeftOf="@id/tv_chang_name"
                    app:layout_constraintTop_toTopOf="@id/tv_goods_spec" />

                <!--商品出厂-->
                <TextView
                    android:id="@+id/tv_chang_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_dp_2"
                    android:drawablePadding="5dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/colors_676773"
                    android:textSize="@dimen/dimen_dp_12"
                    app:layout_constraintLeft_toRightOf="@id/iv_divider_of_spec_name"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/shop_name"
                    tools:text="施慧达药业集团(吉林)有限公..." />

                <!-- 有效期-->
                <TextView
                    android:id="@+id/tv_validity_period"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/colors_676773"
                    android:textSize="@dimen/dimen_dp_12"
                    android:visibility="visible"
                    app:layout_constraintLeft_toLeftOf="@id/shop_name"
                    app:layout_constraintTop_toBottomOf="@id/tv_chang_name"
                    tools:text="有效期2025.09.11/2029.11.22"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--  价格标签区域 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_goods_tag_price"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:layout_constraintLeft_toLeftOf="@id/cl_goods_info"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_goods_info">

                <!--标签 比上次购买时降   60天最低价   区域毛利榜  -->
                <com.ybmmarket20.view.ShopNameWithTagView
                    android:id="@+id/data_tag_list_view"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dimen_dp_14"
                    android:layout_marginTop="@dimen/dimen_dp_8"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="gone" />

                <!-- 价格 -->
                <RelativeLayout
                    android:id="@+id/ll_product_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="@id/barrier_el_edit">

                    <TextView
                        android:id="@+id/tv_audit_passed_visible"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="价格认证资质可见"
                        android:textColor="@color/detail_tv_FF982C"
                        android:textSize="@dimen/dimen_dp_14"
                        android:visibility="gone"
                        tools:text="价格认证资质可见"
                        tools:visibility="gone" />

                    <TextView
                        android:id="@+id/shop_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:ellipsize="end"
                        android:lines="1"
                        android:singleLine="true"
                        android:textColor="@color/record_red"
                        android:textSize="@dimen/dimen_dp_14"
                        android:textStyle="bold"
                        android:visibility="visible"
                        tools:text="¥55.00"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tv_original_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignBottom="@id/shop_price"
                        android:layout_marginLeft="@dimen/dimen_dp_5"
                        android:layout_marginBottom="2dp"
                        android:layout_toRightOf="@id/shop_price"
                        android:ellipsize="end"
                        android:lines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_ff2121"
                        android:textSize="@dimen/dimen_dp_11"
                        android:visibility="visible"
                        tools:text="折后约 ¥222.29"
                        tools:visibility="visible" />

                </RelativeLayout>

                <!-- 拼团相关组件       -->

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_groupbooking"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:visibility="gone"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ll_product_price"
                    tools:visibility="visible">

                    <!-- 拼团价 -->
                    <TextView
                        android:id="@+id/shop_price_spell_group"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/dimen_dp_5"
                        android:ellipsize="end"
                        android:lines="1"
                        android:singleLine="true"
                        android:textColor="@color/record_red"
                        android:textSize="@dimen/dimen_dp_14"
                        android:textStyle="bold"
                        android:visibility="visible"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="¥55.00"
                        tools:visibility="visible" />

                    <!-- 拼团原价 -->
                    <TextView
                        android:id="@+id/tv_original_price_spell_group"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="4dp"
                        android:layout_marginBottom="2dp"
                        android:ellipsize="end"
                        android:lines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_ff2121"
                        android:textSize="@dimen/dimen_dp_11"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="@+id/shop_price_spell_group"
                        app:layout_constraintStart_toEndOf="@+id/shop_price_spell_group"
                        tools:text="折后约 ¥222.29"
                        tools:visibility="visible" />

                    <!--拼团倒计时-->
                    <!--            <TextView-->
                    <!--                android:id="@+id/tv_countdown"-->
                    <!--                android:layout_width="wrap_content"-->
                    <!--                android:layout_height="wrap_content"-->
                    <!--                android:layout_marginStart="4dp"-->
                    <!--                android:layout_marginBottom="4dp"-->
                    <!--                android:drawableStart="@drawable/icon_countdown"-->
                    <!--                android:drawablePadding="@dimen/dimen_dp_5"-->
                    <!--                android:gravity="center_vertical"-->
                    <!--                android:textColor="@color/color_FE5427"-->
                    <!--                android:textSize="@dimen/dimen_dp_11"-->
                    <!--                android:visibility="gone"-->
                    <!--                app:layout_constraintBottom_toBottomOf="parent"-->
                    <!--                app:layout_constraintStart_toStartOf="parent"-->
                    <!--                tools:text="距离结束仅剩 6:12:30" />-->

                    <com.ybmmarket20.common.widget.RoundTextView
                        android:id="@+id/tv_join_groupbooking"
                        android:layout_width="wrap_content"
                        android:layout_height="24dp"
                        android:gravity="center"
                        android:minWidth="45dp"
                        android:paddingHorizontal="3dp"
                        android:text="参团"
                        android:textColor="@color/white"
                        android:textSize="@dimen/dimen_dp_14"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:rv_backgroundColor="@color/color_FF6204"
                        app:rv_cornerRadius="4dp" />

                    <TextView
                        android:id="@+id/tv_groupbooking"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="4dp"
                        android:layout_marginStart="@dimen/dimen_dp_5"
                        android:textColor="@color/color_676773"
                        android:textSize="@dimen/dimen_dp_12"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_original_price_spell_group"
                        tools:text="已拼3210盒" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- 秒杀 -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clSecKillTime"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dimen_dp_39"
                    android:layout_marginTop="@dimen/dimen_dp_7"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/cl_groupbooking"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/shop_price_sec_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/dimen_dp_5"
                        android:ellipsize="end"
                        android:lines="1"
                        android:singleLine="true"
                        android:textColor="@color/record_red"
                        android:textSize="@dimen/dimen_dp_14"
                        android:textStyle="bold"
                        android:visibility="visible"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="¥55.00"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tv_original_price_sec_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="4dp"
                        android:layout_marginBottom="2dp"
                        android:ellipsize="end"
                        android:lines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_ff2121"
                        android:textSize="11dp"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="@+id/shop_price_sec_time"
                        app:layout_constraintStart_toEndOf="@+id/shop_price_sec_time"
                        tools:text="折后约 ¥222.29"
                        tools:visibility="visible" />

                    <!-- 秒杀倒计时-->
                    <com.ybmmarketkotlin.views.SeckillTimeView
                        android:id="@+id/st_countdown"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dimen_dp_14"
                        android:layout_marginStart="@dimen/dimen_dp_5"
                        android:layout_marginTop="@dimen/dimen_dp_8"
                        android:layout_marginBottom="@dimen/dimen_dp_2"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        tools:visibility="visible" />

                    <!-- 秒杀进度条-->
                    <SeekBar
                        android:id="@+id/seckill_progress"
                        android:layout_width="25dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dimen_dp_8"
                        android:max="100"
                        android:maxHeight="5dp"
                        android:minHeight="5dp"
                        android:paddingStart="0dp"
                        android:paddingEnd="@dimen/dimen_dp_0"
                        android:progress="40"
                        android:progressDrawable="@drawable/bg_progressbar_seckill_listitem"
                        android:thumb="@drawable/shape_sec_kill_seekbar_thumb"
                        android:thumbOffset="0dp"
                        app:layout_constraintBottom_toBottomOf="@+id/st_countdown"
                        app:layout_constraintStart_toEndOf="@+id/seckill_desc"
                        app:layout_constraintTop_toTopOf="@+id/st_countdown"
                        tools:progress="50" />

                    <!-- 秒杀进度描述-->
                    <TextView
                        android:id="@+id/seckill_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dimen_dp_10"
                        android:layout_marginRight="@dimen/dimen_dp_10"
                        android:text="已售80%"
                        android:textColor="@color/color_676773"
                        android:textSize="@dimen/dimen_dp_9"
                        app:layout_constraintBottom_toBottomOf="@id/seckill_progress"
                        app:layout_constraintStart_toEndOf="@+id/st_countdown"
                        app:layout_constraintTop_toTopOf="@id/seckill_progress" />

                    <com.ybmmarket20.common.widget.RoundTextView
                        android:id="@+id/tv_sec_kill_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="24dp"
                        android:gravity="center"
                        android:minWidth="45dp"
                        android:paddingHorizontal="3dp"
                        android:text="抢购"
                        android:textColor="@color/white"
                        android:textSize="@dimen/dimen_dp_14"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:rv_backgroundColor="@color/color_FF6204"
                        app:rv_cornerRadius="4dp" />

                    <com.ybmmarket20.view.ProductEditLayoutNew
                        android:id="@+id/el_sec_kill_edit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dimen_dp_5"
                        android:background="@color/white"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:visibility="gone" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--控销价/零售价 && 毛利率-->
                <TextView
                    android:id="@+id/tv_retail_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/ll_product_price"
                    android:layout_marginTop="4dp"
                    android:ellipsize="end"
                    android:lines="1"
                    android:orientation="horizontal"
                    android:textColor="@color/color_9494A6"
                    android:textSize="@dimen/dimen_dp_11"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/clSecKillTime"
                    tools:text="零售价 ¥19.00（毛利率25%）"
                    tools:visibility="visible" />


                <com.ybmmarket20.view.ShopNameWithTagView
                    android:id="@+id/snwtg_spell_group_market_tag"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dimen_dp_14"
                    android:layout_marginTop="@dimen/dimen_dp_4"
                    android:layout_marginRight="@dimen/dimen_dp_20"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/rl_icon_type"
                    tools:visibility="visible" />

                <!--价格在data_tag_list_view和中包装下面  因为两者可能没有-->
                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/barrier_el_edit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="data_tag_list_view,shop_price_tv" />

                <!--加购按钮-->
                <com.ybmmarket20.view.ProductEditLayoutNew
                    android:id="@+id/el_edit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:visibility="visible"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/ll_product_price"
                    tools:visibility="visible" />

                <!--秒杀立即抢购-->
                <com.ybmmarket20.common.widget.RoundTextView
                    android:id="@+id/tv_seckill_commit"
                    android:layout_width="wrap_content"
                    android:layout_height="24dp"
                    android:layout_marginBottom="@dimen/dimen_dp_10"
                    android:gravity="center"
                    android:minWidth="45dp"
                    android:text="抢购"
                    android:textColor="@color/white"
                    android:textSize="14dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0"
                    app:rv_backgroundColor="@color/color_FF6204"
                    app:rv_cornerRadius="4dp"
                    tools:visibility="gone" />

                <!--   中包装-->
                <TextView
                    android:id="@+id/shop_price_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="2dp"
                    android:layout_marginBottom="3dp"
                    android:gravity="center_vertical"
                    android:singleLine="true"
                    android:textColor="@color/brand_description_tv1"
                    android:textSize="10dp"
                    android:visibility="invisible"
                    app:layout_constrainedHeight="true"
                    app:layout_constraintBottom_toTopOf="@id/el_edit"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/data_tag_list_view"
                    tools:text="中包装：10盒"
                    tools:visibility="visible" />

                <!--<editor-fold desc="营销标签">-->
                <!--  营销标签  标签 7-->
                <com.ybmmarket20.view.ShopNameWithTagView
                    android:id="@+id/rl_icon_type"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tv_retail_price"
                    android:layout_marginTop="@dimen/dimen_dp_4"
                    android:layout_marginRight="@dimen/dimen_dp_20"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_retail_price"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/iv_promotion_more"
                    android:layout_width="@dimen/dimen_dp_27"
                    android:layout_height="@dimen/dimen_dp_15"
                    android:background="@drawable/icon_more_dots"
                    android:padding="6dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/rl_icon_type"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@id/rl_icon_type"
                    tools:visibility="visible" />
                <!--</editor-fold>-->

                <!-- 店铺 -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_pop_company"
                    android:layout_width="@dimen/dimen_dp_0"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_dp_6"
                    app:layout_constraintEnd_toStartOf="@+id/ll_shop_same_goods"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/snwtg_spell_group_market_tag">

                    <TextView
                        android:id="@+id/tv_pop_company"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="@dimen/dimen_dp_2"
                        android:gravity="center_vertical"
                        android:singleLine="true"
                        android:textColor="@color/color_676773"
                        android:textSize="@dimen/dimen_dp_11"
                        android:visibility="visible"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintEnd_toStartOf="@id/tv_goto_shop"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tv_goto_shop"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="7dp"
                        android:text="进店"
                        android:textColor="@color/text_color_333333"
                        android:textSize="11dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/iv_goto_shop"
                        app:layout_constraintStart_toEndOf="@id/tv_pop_company"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/iv_goto_shop"
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:layout_marginStart="1dp"
                        android:background="@drawable/icon_goto_shop"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tv_goto_shop"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- 店铺同款商品 -->
                <LinearLayout
                    android:id="@+id/ll_shop_same_goods"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_pop_company"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/cl_pop_company"
                    app:layout_constraintTop_toTopOf="@+id/cl_pop_company">

                    <View
                        android:layout_width="@dimen/dimen_dp_1"
                        android:layout_height="@dimen/dimen_dp_7"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_dp_7"
                        android:background="@color/detail_tv_575766"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_shop_same_goods"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dimen_dp_15"
                        android:drawableStart="@drawable/icon_shop_same_goods"
                        android:drawableEnd="@drawable/icon_goto_shop"
                        android:drawablePadding="@dimen/dimen_dp_3"
                        android:gravity="center_vertical"
                        android:text="店铺同款"
                        android:textColor="@color/color_676773"
                        android:textSize="@dimen/dimen_dp_11" />
                </LinearLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/baseLine"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_dp_1"
                android:layout_marginTop="@dimen/dimen_dp_25"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/cl_goods_tag_price" />

            <!-- 订阅-->
            <LinearLayout
                android:id="@+id/ll_subscribe"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@id/baseLine"
                app:layout_constraintRight_toRightOf="parent"
                tools:visibility="gone">

                <ImageView
                    android:id="@+id/iv_goods_subscribe"
                    android:layout_width="@dimen/dimen_dp_22"
                    android:layout_height="@dimen/dimen_dp_22"
                    android:layout_gravity="center_horizontal"
                    android:visibility="visible"
                    tools:src="@drawable/icon_goods_subscribe"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_goods_subscribe"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_292933"
                    android:textSize="@dimen/dimen_dp_12"
                    android:visibility="visible"
                    tools:text="到货通知"
                    tools:visibility="visible" />

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/dimen_dp_7"
                android:background="@color/color_f7f7f8"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="@id/cl_goods_tag_price"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_goods_tag_price"
                app:layout_constraintVertical_bias="1" />
        </com.ybmmarket20.common.widget.RoundConstraintLayout>
    </com.ybmmarket20.common.widget.RoundConstraintLayout>

</LinearLayout>
