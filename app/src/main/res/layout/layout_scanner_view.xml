<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:app="http://schemas.android.com/apk/res-auto"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
    <!--预览-->
    <SurfaceView
        android:id="@+id/surface_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <!--遮盖层-->
    <com.zxing.view.MarkerView
        android:id="@+id/marker_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <!--提示文字-->
    <com.ybmmarket20.common.widget.RoundFrameLayout
        android:id="@+id/fl_mode_tip"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="30dp"
        android:clickable="false"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        app:rv_backgroundColor="#000000"
        app:rv_cornerRadius="20dp">

        <TextView
            android:id="@+id/tv_mode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="2dp"
            android:gravity="center"
            android:lineSpacingExtra="10dp"
            android:text="请将条形码/二维码对准扫描框"
            android:textColor="@android:color/white"
            android:textSize="17sp"/>
    </com.ybmmarket20.common.widget.RoundFrameLayout>
    <!--扫描线（宽度最好和框的宽适配好）-->
    <ImageView
        android:id="@+id/iv_laser"
        android:layout_width="250dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:background="@drawable/icon_scan_laser"
        android:visibility="gone"/>
    <!--输入框-->
    <EditText
        android:id="@+id/edit_text"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_centerHorizontal="true"
        android:background="@android:color/white"
        android:gravity="center_vertical"
        android:hint="请输入条形码"
        android:inputType="text"
        android:padding="4dp"
        android:textColor="@color/text_292933"
        android:textColorHint="#cccccc"
        android:textSize="16sp"
        android:visibility="gone"/>
    <!--下面的确认按钮-->
    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="80dp"
        android:background="@drawable/bg_circle_green_shape"
        android:gravity="center"
        android:text="确认"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:visibility="gone"/>
    <!--灯光按钮-->
    <LinearLayout
        android:id="@+id/ll_switch_light"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:orientation="vertical">

        <CheckBox
            android:id="@+id/cb_light"
            style="@style/captureCheckboxTheme"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"/>

        <TextView
            android:id="@+id/tv_light_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="点击开启闪光灯"
            android:textColor="#7fffffff"
            android:textSize="14sp"/>
    </LinearLayout>
</RelativeLayout>