<com.ybmmarket20.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/pabr_dimen12dp"
    app:rv_cornerRadius="@dimen/pabr_dimen4dp"
    app:rv_strokeColor="@color/colors_FFC97E"
    app:rv_strokeWidth="0.5dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="0.5dp"
        android:layout_marginTop="0.5dp"
        android:background="@drawable/shape_product_combinedbuy_top_bg"
        android:paddingHorizontal="@dimen/pabr_dimen10dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_combined_single_title"
            android:drawablePadding="@dimen/dimen_dp_4"
            android:gravity="center"
            android:paddingTop="@dimen/pabr_dimen10dp"
            android:paddingBottom="@dimen/pabr_dimen10dp"
            android:text="组合购更优惠"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/pax_core_sp_14"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvRefresh"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_combined_change"
            android:drawablePadding="@dimen/pabr_dimen1dp"
            android:gravity="center"
            android:paddingTop="@dimen/pabr_dimen10dp"
            android:paddingBottom="@dimen/pabr_dimen10dp"
            android:text="换一个"
            android:textColor="@color/text_color_666666"
            android:textSize="@dimen/pax_core_sp_13"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rlvGoods"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/pabr_dimen16dp"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintEnd_toStartOf="@id/cyOperation"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layoutTop" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cyOperation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/pabr_dimen8dp"
        app:layout_constraintBottom_toBottomOf="@id/rlvGoods"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/rlvGoods"
        app:layout_constraintTop_toBottomOf="@id/layoutTop">

        <TextView
            android:id="@+id/tvDiscount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:text="组合优惠:￥30"
            android:textColor="@color/text_color_666666"
            android:textSize="@dimen/pax_core_sp_12"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTotalPrice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:text="到手价:￥30"
            android:textColor="@color/color_FF2121"
            android:textSize="@dimen/pax_core_sp_12"
            app:layout_constraintTop_toBottomOf="@id/tvDiscount" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tvSettle"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_marginTop="@dimen/pabr_dimen5dp"
            android:gravity="center"
            android:text="去下单"
            android:textColor="@color/white"
            android:textSize="@dimen/pabr_dimen14sp"
            app:layout_constraintTop_toBottomOf="@id/tvTotalPrice"
            app:rv_backgroundColor="@color/color_00b955"
            app:rv_cornerRadius="@dimen/dimen_dp_4" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.ybmmarket20.common.widget.RoundConstraintLayout>