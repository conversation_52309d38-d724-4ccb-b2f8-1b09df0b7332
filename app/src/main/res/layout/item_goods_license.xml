<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_80"
    android:background="@color/white">

    <ImageView
        android:id="@+id/ivGoods"
        android:layout_width="@dimen/dimen_dp_48"
        android:layout_height="@dimen/dimen_dp_48"
        android:layout_marginStart="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/logo" />

    <TextView
        android:id="@+id/tvProductName"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_2"
        android:textSize="@dimen/dimen_dp_14"
        android:textColor="@color/color_292933"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivGoods"
        app:layout_constraintTop_toTopOf="@+id/ivGoods"
        tools:text="珍视明抗疲劳养护滴眼液/50ml/盒" />

    <LinearLayout
        android:id="@+id/llReport"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_18"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/dimen_dp_4"
        app:layout_constraintTop_toBottomOf="@+id/tvProductName"
        app:layout_constraintStart_toEndOf="@+id/ivGoods">

        <ImageView
            android:id="@+id/ivReport"
            android:layout_width="@dimen/dimen_dp_14"
            android:layout_height="18dp"
            android:layout_marginTop="@dimen/dimen_dp_1"
            android:layout_gravity="center_vertical"
            android:src="@drawable/icon_license_selected" />

        <TextView
            android:id="@+id/tvReport"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="药检报告"
            android:textColor="#01B377"
            android:textSize="@dimen/dimen_dp_12"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dimen_dp_5" />
    </LinearLayout>

    <Space
        android:id="@+id/space"
        android:layout_width="@dimen/dimen_dp_40"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@+id/llReport" />

    <LinearLayout
        android:id="@+id/llLicense"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_18"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginBottom="@dimen/dimen_dp_4"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/dimen_dp_4"
        app:layout_constraintTop_toBottomOf="@+id/tvProductName"
        app:layout_constraintStart_toEndOf="@+id/space" >

        <ImageView
            android:id="@+id/ivLicense"
            android:layout_width="@dimen/dimen_dp_14"
            android:layout_height="@dimen/dimen_dp_14"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="@dimen/dimen_dp_1"
            android:src="@drawable/icon_license_unselected" />

        <TextView
            android:id="@+id/tvLicense"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="首营资料"
            android:textColor="#676773"
            android:textSize="@dimen/dimen_dp_12"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dimen_dp_5" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>