<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="42dp"
        android:layout_height="34dp"
        android:layout_marginTop="5dp"
        android:paddingStart="15dp"
        android:paddingTop="7dp"
        android:paddingEnd="15dp"
        android:paddingBottom="7dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:id="@+id/ll_search_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:background="@drawable/search_round_corner_gray_bg_03"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/iv_back"
        app:layout_constraintLeft_toRightOf="@id/iv_back"
        app:layout_constraintRight_toLeftOf="@+id/iv_service"
        app:layout_constraintTop_toTopOf="@id/iv_back">

        <ImageView
            android:id="@+id/iv_scan_qrcode"
            android:layout_width="41dp"
            android:layout_height="match_parent"
            android:paddingStart="11dp"
            android:paddingTop="6dp"
            android:paddingEnd="8dp"
            android:paddingBottom="6dp"
            android:src="@drawable/icon_home_steady_scan" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="16dp"
            android:background="#d8d8d8" />

        <EditText
            android:id="@+id/et_search"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@null"
            android:inputType="none"
            android:focusableInTouchMode="false"
            android:focusable="false"
            android:hint="搜索此商家商品"
            android:imeOptions="actionSearch"
            android:paddingStart="5.5dp"
            android:paddingEnd="5.5dp"
            android:singleLine="true"
            android:textColor="@color/color_292933"
            android:textColorHint="@color/color_9494A6"
            android:textCursorDrawable="@drawable/color_cursor"
            android:textSize="13dp" />


        <ImageView
            android:id="@+id/iv_clear"
            android:layout_width="39dp"
            android:layout_height="match_parent"
            android:paddingStart="10.5dp"
            android:paddingTop="8dp"
            android:scaleType="centerCrop"
            android:paddingEnd="10.5dp"
            android:paddingBottom="8dp"
            android:src="@drawable/icon_home_steady_voice" />

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_service"
        android:layout_width="42dp"
        android:layout_height="0dp"
        android:paddingStart="10dp"
        android:paddingTop="6dp"
        android:paddingEnd="10dp"
        android:paddingBottom="6dp"
        android:src="@drawable/icon_customer_service"
        app:layout_constraintBottom_toBottomOf="@id/iv_back"
        app:layout_constraintLeft_toRightOf="@id/ll_search_container"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_back" />


</androidx.constraintlayout.widget.ConstraintLayout>