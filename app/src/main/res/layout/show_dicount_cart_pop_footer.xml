<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <TextView
        android:id="@+id/tv_total_discount_price_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dimen_dp_15"
        android:layout_marginTop="@dimen/dimen_dp_20"
        android:textColor="@color/color_30303c"
        android:textSize="@dimen/dimen_dp_16"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="共优惠 " />

    <TextView
        android:id="@+id/tv_total_discount_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dimen_dp_15"
        android:textColor="@color/color_ff2121"
        android:textSize="@dimen/dimen_dp_16"
        android:textStyle="bold"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_total_discount_price_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="￥24.5 " />


    <TextView
        android:id="@+id/tv_notice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_6"
        android:layout_marginBottom="@dimen/dimen_dp_20"
        tools:text="以上优惠不包含余额抵扣，请在结算页查看"
        android:textColor="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/tv_total_discount_price_title"
        app:layout_constraintTop_toBottomOf="@id/tv_total_discount_price_title" />
</androidx.constraintlayout.widget.ConstraintLayout>