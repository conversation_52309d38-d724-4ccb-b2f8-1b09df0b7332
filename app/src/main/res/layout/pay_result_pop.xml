<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_weight="3"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/rl_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipChildren="false"
        android:orientation="vertical">

        <com.ybmmarket20.view.ClipViewPager
            android:id="@+id/vp_arl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:clipChildren="false" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_clase"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:background="@color/transparent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_arl"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:gravity="center"
            android:orientation="horizontal" />

        <ImageView
            android:id="@+id/clase"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="10dp"
            android:src="@drawable/icon_operation_explain_close" />

    </LinearLayout>

</LinearLayout>