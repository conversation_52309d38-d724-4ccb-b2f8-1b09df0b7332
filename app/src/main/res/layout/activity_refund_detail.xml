<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/activity_bg"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <com.ybmmarket20.view.MyScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/ll_refund_status"
                android:layout_width="match_parent"
                android:layout_height="46dp"
                android:background="@color/white"
                android:orientation="horizontal"
                android:paddingLeft="20dp"
                android:paddingRight="11dp"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_status"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="1dp"
                    android:background="@color/white"
                    android:drawableLeft="@drawable/refund_drawable_selector"
                    android:drawablePadding="7dp"
                    android:gravity="center_vertical"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp"
                    android:text=""
                    android:textColor="@color/text_292933"
                    android:textSize="14sp" />

                <TextView
                    style="@style/payment_item_layout_text_left"
                    android:layout_marginRight="0dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical|right"
                    android:paddingRight="0dp"
                    android:text="查看退货商品"
                    android:textColor="@color/text_292933" />
            </LinearLayout>

            <com.ybmmarket20.view.RefundDetailsOptimizationLayout
                android:id="@+id/rdol"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:background="@color/white"
                android:minHeight="129dp"/>
            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:id="@+id/ll_card_refund_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="7dp"
                android:orientation="vertical"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_address_title"
                    style="@style/refund_detail_title"
                    android:text="退货信息" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/divider_line_base_1px" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/add_address"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:layout_marginTop="5dp"
                        android:src="@drawable/dqddd_adress" />

                    <LinearLayout
                        android:id="@+id/ll_refund_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_toRightOf="@+id/add_address"
                        android:orientation="vertical"
                        android:showDividers="middle">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tv_address_name"
                                style="@style/refund_detail_activiy_title"
                                android:layout_width="wrap_content"
                                android:text="收货人：" />

                            <TextView
                                android:id="@+id/tv_address_phone"
                                style="@style/refund_detail_activiy_title"
                                android:layout_marginLeft="10dp"
                                android:text="联系电话：" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_address"
                            style="@style/refund_detail_activiy_title"
                            android:layout_height="wrap_content"
                            android:maxLines="2"
                            android:text="收货地址：" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/divider_line_base_1px" />

                        <TextView
                            android:id="@+id/tv_delivery_instructions"
                            style="@style/refund_detail_activiy_title"
                            android:layout_height="wrap_content"
                            android:height="44dp"
                            android:maxLines="2"
                            android:minHeight="44dp"
                            android:text="快递说明："
                            android:textStyle="bold" />

                    </LinearLayout>
                </RelativeLayout>
            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:gravity="center"
                android:orientation="horizontal"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="7dp">

                <TextView
                    android:id="@+id/btn_kefu"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:drawableLeft="@drawable/icon_phone"
                    android:drawablePadding="20dp"
                    android:gravity="center"
                    android:minWidth="120dp"
                    android:visibility="gone"
                    android:text="客服电话：400-0505-111"
                    android:textColor="@color/text_292933" />

            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <Space
                android:id="@+id/space"
                android:layout_width="match_parent"
                android:layout_height="10dp" />

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:orientation="vertical"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="7dp">

                <TextView
                    android:id="@+id/tv_result_title"
                    style="@style/refund_detail_title"
                    android:text="退款详情" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/divider_line_base_1px" />

                <LinearLayout
                    android:id="@+id/ll_refund_result"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="10dp"
                    android:showDividers="middle">

                    <TextView
                        android:id="@+id/tv_money"
                        style="@style/refund_detail_activiy_title"
                        android:text="退款金额：" />

                    <TextView
                        android:id="@+id/tv_balance"
                        style="@style/refund_detail_activiy_title"
                        android:text="退回余额：" />

                    <TextView
                        android:id="@+id/tv_virtual_money"
                        style="@style/refund_detail_activiy_title"
                        android:text="退回购物金：" />

                    <TextView
                        android:id="@+id/tv_refund_route"
                        style="@style/refund_detail_activiy_title"
                        android:text="退款去向：" />

                    <TextView
                        android:id="@+id/tv_refund_channel"
                        style="@style/refund_detail_activiy_title"
                        android:text="退款渠道："
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_refund_time"
                        style="@style/refund_detail_activiy_title"
                        android:text="关闭时间：" />

                    <TextView
                        android:id="@+id/tv_close_reason"
                        style="@style/refund_detail_activiy_title"
                        android:layout_height="wrap_content"
                        android:maxLines="3"
                        android:text="关闭原因：" />

                    <LinearLayout
                        android:id="@+id/ll_invoice_express"
                        style="@style/refund_detail_activiy_title"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/dimen_dp_9"
                        android:visibility="visible">

                        <TextView
                            style="@style/refund_detail_activiy_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:minHeight="@dimen/dimen_dp_0"
                            android:layout_gravity="top"
                            android:text="发票寄出快递：" />

                        <TextView
                            android:id="@+id/tv_express_type"
                            style="@style/refund_detail_activiy_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:minHeight="@dimen/dimen_dp_0"
                            android:layout_gravity="top"
                            android:layout_marginEnd="@dimen/dimen_dp_50"
                            tools:text="京东JD394728319京ddddd" />

                        <TextView
                            android:id="@+id/tv_express_type_copy_btn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="top"
                            android:layout_marginStart="-40dp"
                            android:background="@drawable/shape_express_type_copy_btn"
                            android:paddingStart="@dimen/dimen_dp_7"
                            android:paddingEnd="@dimen/dimen_dp_7"
                            android:text="复制"
                            android:textColor="@color/color_00b377"
                            android:textSize="@dimen/dimen_dp_11" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_pay_credential"
                        style="@style/refund_detail_activiy_title"
                        android:layout_height="wrap_content"
                        android:gravity="top"
                        android:visibility="gone"
                        android:layout_marginBottom="@dimen/dimen_dp_5"
                        tools:visibility="visible">

                        <TextView
                            style="@style/refund_detail_activiy_title"
                            android:layout_width="wrap_content"
                            android:text="打款凭证：" />

                        <com.ybmmarket20.common.widget.RoundedImageView
                            android:id="@+id/iv_pay_credential"
                            android:layout_width="@dimen/dimen_dp_65"
                            android:layout_height="@dimen/dimen_dp_65"
                            app:riv_corner_radius="@dimen/dimen_dp_2" />

                        <TextView
                            android:id="@+id/iv_pay_credential_symbol"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-"
                            android:visibility="gone"
                            style="@style/refund_detail_activiy_title" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_indemnity_money"
                        style="@style/refund_detail_activiy_title"
                        android:layout_height="wrap_content"
                        android:textColor="@color/color_FF2121"
                        android:maxLines="1"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:text="额外赔偿：" />

                    <TextView
                        android:id="@+id/tv_small_detail"
                        style="@style/refund_detail_activiy_title"
                        android:lineSpacingExtra="2dp"
                        android:lineSpacingMultiplier="1.1"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:text="小额赔偿：" />

                </LinearLayout>

            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:orientation="vertical"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="7dp">

                <TextView
                    style="@style/refund_detail_title"
                    android:text="申请详情" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/divider_line_base_1px" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="10dp"
                    android:showDividers="middle">

                    <TextView
                        android:id="@+id/tv_apply_money"
                        style="@style/refund_detail_activiy_title"
                        android:lineSpacingExtra="2dp"
                        android:lineSpacingMultiplier="1.1"
                        android:text="退款金额：" />

                    <TextView
                        android:id="@+id/tv_small_payment"
                        style="@style/refund_detail_activiy_title"
                        android:lineSpacingExtra="2dp"
                        android:lineSpacingMultiplier="1.1"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:text="小额赔偿：" />

                    <TextView
                        android:id="@+id/tv_apply_time"
                        style="@style/refund_detail_activiy_title"
                        android:lineSpacingExtra="2dp"
                        android:lineSpacingMultiplier="1.1"
                        android:text="申请时间：" />

                    <TextView
                        android:id="@+id/tv_reason"
                        style="@style/refund_detail_activiy_title"
                        android:layout_height="wrap_content"
                        android:lineSpacingExtra="2dp"
                        android:lineSpacingMultiplier="1.1"
                        android:text="退款原因：" />

                    <TextView
                        android:id="@+id/tv_info"
                        style="@style/refund_detail_activiy_title"
                        android:layout_height="wrap_content"
                        android:text="退款说明：" />

                    <LinearLayout
                        android:id="@+id/llRefundCertificate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            style="@style/refund_detail_activiy_title"
                            android:text="退款凭证：" />

                        <RelativeLayout
                            android:id="@+id/fragment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="20dp" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_indemnity_money2"
                        style="@style/refund_detail_activiy_title"
                        android:layout_height="wrap_content"
                        android:textColor="@color/color_FF2121"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:maxLines="1"
                        android:text="额外赔偿：" />
                </LinearLayout>

            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:id="@+id/ll_bank"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:orientation="vertical"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="7dp">

                <TextView
                    style="@style/refund_detail_title"
                    android:text="收款账户信息" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/divider_line_base_1px" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="10dp"
                    android:showDividers="middle">

                    <TextView
                        android:id="@+id/tv_opening_bank"
                        style="@style/refund_detail_activiy_title"
                        android:lineSpacingExtra="2dp"
                        android:lineSpacingMultiplier="1.1"
                        android:text="开户行及支行：" />

                    <TextView
                        android:id="@+id/tv_credit_card_numbers"
                        style="@style/refund_detail_activiy_title"
                        android:lineSpacingExtra="2dp"
                        android:lineSpacingMultiplier="1.1"
                        android:text="银行卡号：" />

                    <TextView
                        android:id="@+id/tv_account_holder"
                        style="@style/refund_detail_activiy_title"
                        android:layout_height="wrap_content"
                        android:lineSpacingExtra="2dp"
                        android:lineSpacingMultiplier="1.1"
                        android:text="开户人：" />

                    <TextView
                        android:id="@+id/tv_phone"
                        style="@style/refund_detail_activiy_title"
                        android:layout_height="wrap_content"
                        android:text="联系电话：" />

                </LinearLayout>

            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:id="@+id/ll_expressage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:orientation="vertical"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="7dp">

                <TextView
                    style="@style/refund_detail_title"
                    android:text="退货物流信息" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/divider_line_base_1px" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="10dp"
                    android:showDividers="middle">

                    <TextView
                        android:id="@+id/tv_expressage_name"
                        style="@style/refund_detail_activiy_title"
                        android:lineSpacingExtra="2dp"
                        android:lineSpacingMultiplier="1.1"
                        android:text="快递名称：" />

                    <TextView
                        android:id="@+id/tv_expressage_number"
                        style="@style/refund_detail_activiy_title"
                        android:lineSpacingExtra="2dp"
                        android:lineSpacingMultiplier="1.1"
                        android:text="快递单号：" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            style="@style/refund_detail_activiy_title"
                            android:text="运单截图：" />

                        <RelativeLayout
                            android:id="@+id/fragment_expressage"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="20dp" />
                    </LinearLayout>

                </LinearLayout>

            </com.ybmmarket20.common.widget.RoundLinearLayout>

        </LinearLayout>
    </com.ybmmarket20.view.MyScrollView>

    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="@color/activity_bg" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="58dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingRight="12dp">

        <TextView
            android:id="@+id/btn_cancel_the_refund"
            android:layout_width="87dp"
            android:layout_height="33dp"
            android:layout_toLeftOf="@+id/tv_edit_gathering_id"
            android:background="@drawable/order_gray_border"
            android:gravity="center"
            android:text="取消退款"
            android:textColor="@color/text_292933"
            android:textSize="@dimen/check_order_tv3"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_edit_gathering_id"
            android:layout_width="87dp"
            android:layout_height="33dp"
            android:layout_marginLeft="10dp"
            android:layout_toLeftOf="@+id/tv_fill_in_return_logistics"
            android:background="@drawable/order_green_border"
            android:gravity="center"
            android:text="编辑收款账号"
            android:textColor="@color/base_colors"
            android:textSize="@dimen/check_order_tv3"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_fill_in_return_logistics"
            android:layout_width="87dp"
            android:layout_height="33dp"
            android:layout_marginLeft="10dp"
            android:layout_toLeftOf="@+id/tv_check_sales_commodity"
            android:background="@drawable/order_green_border"
            android:gravity="center"
            android:text="填写退货物流"
            android:textColor="@color/base_colors"
            android:textSize="@dimen/check_order_tv3"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_check_sales_commodity"
            android:layout_width="87dp"
            android:layout_height="33dp"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="10dp"
            android:background="@drawable/order_green_border"
            android:gravity="center"
            android:text="查看退货商品"
            android:textColor="@color/base_colors"
            android:textSize="@dimen/check_order_tv3" />

    </RelativeLayout>

</LinearLayout>