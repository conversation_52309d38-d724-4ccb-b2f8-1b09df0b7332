<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_F7F7F7">

    <RelativeLayout
        android:id="@+id/ll_title"
        style="@style/header_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivBack"
            style="@style/header_layout_left"
            android:src="@drawable/ic_back" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/header_layout_mid"
            android:text="" />

        <ImageView
            android:id="@+id/ivCart"
            style="@style/header_layout_right_img"
            android:src="@drawable/cart_icon"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tvCartNum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="8dp"
            android:layout_marginTop="@dimen/dimen_dp_5"
            android:background="@drawable/bg_message"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="10dp"
            android:visibility="gone"
            tools:text="9+"
            tools:visibility="visible" />
    </RelativeLayout>

    <View
        android:id="@+id/bgFilter"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_44"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@+id/ll_title" />

    <RadioGroup
        android:id="@+id/rgFilter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/bgFilter"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_title"
        app:layout_constraintTop_toTopOf="@+id/bgFilter">

        <RadioButton
            android:id="@+id/rbFilterOftenBuy"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_24"
            android:layout_marginStart="@dimen/dimen_dp_15"
            android:background="@drawable/selector_often_buy_filter"
            android:button="@null"
            android:checked="true"
            android:paddingStart="@dimen/dimen_dp_8"
            android:paddingEnd="@dimen/dimen_dp_8"
            android:text="最常购买"
            android:textColor="@color/selector_often_buy_text"
            android:textSize="@dimen/dimen_dp_12" />

        <RadioButton
            android:id="@+id/rbFilterRecentBuy"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_24"
            android:layout_marginStart="@dimen/dimen_dp_15"
            android:background="@drawable/selector_often_buy_filter"
            android:button="@null"
            android:paddingStart="@dimen/dimen_dp_8"
            android:paddingEnd="@dimen/dimen_dp_8"
            android:text="最近购买"
            android:textColor="@color/selector_often_buy_text"
            android:textSize="@dimen/dimen_dp_12" />

    </RadioGroup>

    <CheckBox
        android:id="@+id/cbOftenBuy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:button="@drawable/selector_often_buy_filter_time"
        android:paddingStart="@dimen/dimen_dp_5"
        android:text="仅查看30天内买过"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintBottom_toBottomOf="@+id/rgFilter"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/rgFilter" />

    <FrameLayout
        android:id="@+id/flFragment"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        app:layout_constraintVertical_weight="1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bgFilter"/>
</androidx.constraintlayout.widget.ConstraintLayout>