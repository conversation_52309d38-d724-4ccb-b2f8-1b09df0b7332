<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/hintTv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="#FFF7EF"
            android:paddingLeft="20dp"
            android:paddingTop="10dp"
            android:paddingRight="20dp"
            android:paddingBottom="10dp"
            android:text="密码必须8-16个字符，至少含数字、字母、符号两种组合，首位必须为字母"
            android:textColor="#99664d"
            android:textSize="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/passwordTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="12dp"
            android:text="新密码"
            android:textColor="#292933"
            android:textSize="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hintTv" />

        <View
            android:id="@+id/divider1"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="12dp"
            android:background="#F5F5F5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/passwordTv" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/passwordConfirmTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="12dp"
            android:text="确认密码"
            android:textColor="#292933"
            android:textSize="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider1" />

        <View
            android:id="@+id/divider2"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="12dp"
            android:background="#F5F5F5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/passwordConfirmTv" />

        <com.ybmmarket20.view.ButtonObserver
            android:id="@+id/new_forget_password_btn"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="60dp"
            android:layout_marginRight="20dp"
            android:background="@drawable/selector_common_btn"
            android:enabled="false"
            android:gravity="center"
            android:text="完成"
            android:textColor="@color/white"
            android:textSize="16dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider2"
            app:passwordToggleDrawable="@drawable/selector_password_toggle"
            app:passwordToggleEnabled="true" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/defaultInputLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:layout_marginRight="20dp"
            app:hintEnabled="false"
            app:layout_constraintBottom_toBottomOf="@id/passwordTv"
            app:layout_constraintLeft_toLeftOf="@id/confirmInputLayout"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/passwordTv"
            app:passwordToggleDrawable="@drawable/selector_password_toggle"
            app:passwordToggleEnabled="true">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_new_forget_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:hint="请输入您的新密码"
                android:background="@null"
                android:inputType="textPassword"
                android:textColorHint="@color/loginTextAppearance"
                android:textColor="#292933"
                android:textSize="15dp" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/confirmInputLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="35dp"
            android:layout_marginLeft="35dp"
            android:layout_marginEnd="20dp"
            android:layout_marginRight="20dp"
            app:hintEnabled="false"
            app:layout_constraintBottom_toBottomOf="@id/passwordConfirmTv"
            app:layout_constraintLeft_toRightOf="@id/passwordConfirmTv"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/passwordConfirmTv"
            app:passwordToggleDrawable="@drawable/selector_password_toggle"
            app:passwordToggleEnabled="true">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_again_new_forget_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:background="@null"
                android:hint="请再次输入新密码"
                android:inputType="textPassword"
                android:textColorHint="@color/loginTextAppearance"
                android:textColor="#292933"
                android:textSize="15dp" />

        </com.google.android.material.textfield.TextInputLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>