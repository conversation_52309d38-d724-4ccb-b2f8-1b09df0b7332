<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="7dp"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="match_parent"
        android:layout_height="63dp"
        android:gravity="center"
        android:src="@drawable/transparent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_F5F5F5" />

    <!--增加自己的固定布局-->
    <RelativeLayout
        android:id="@+id/rl_layout"
        android:layout_width="match_parent"
        android:layout_height="226dp"
        android:background="@color/white"
        android:clipChildren="false">

        <com.ybmmarket20.view.AutoHeightViewPager
            android:id="@+id/vp_arl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:clipChildren="false" />

        <LinearLayout
            android:id="@+id/ll_arl"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_alignParentBottom="true"
            android:layout_below="@+id/vp_arl"
            android:gravity="center"
            android:orientation="horizontal" />

    </RelativeLayout>

</LinearLayout>