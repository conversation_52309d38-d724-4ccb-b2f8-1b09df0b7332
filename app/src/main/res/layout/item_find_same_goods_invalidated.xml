<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_find_same"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <ImageView
        android:id="@+id/iv_invalidated_goods"
        android:layout_width="@dimen/dimen_dp_75"
        android:layout_height="@dimen/dimen_dp_75"
        android:layout_centerVertical="true"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:padding="@dimen/product_image_padding"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="@dimen/dimen_dp_75"
        android:layout_height="@dimen/dimen_dp_75"
        android:background="@drawable/shape_near_effective_icon_mask"
        app:layout_constraintStart_toStartOf="@+id/iv_invalidated_goods"
        app:layout_constraintTop_toTopOf="@+id/iv_invalidated_goods"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_goods_type"
        android:layout_width="@dimen/dimen_dp_75"
        android:layout_height="@dimen/dimen_dp_22"
        android:text="失效"
        android:textColor="@color/white"
        android:gravity="center"
        android:textSize="@dimen/dimen_dp_13"
        android:visibility="gone"
        android:background="@drawable/shape_near_effective_icon_unrefund"
        app:layout_constraintBottom_toBottomOf="@+id/iv_invalidated_goods"
        app:layout_constraintStart_toStartOf="@+id/iv_invalidated_goods" />

    <TextView
        android:id="@+id/tv_invalidated_title"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14"
        android:layout_marginTop="@dimen/dimen_dp_1"
        android:layout_marginStart="@dimen/dimen_dp_17"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:singleLine="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_invalidated_goods"
        app:layout_constraintTop_toTopOf="@+id/iv_invalidated_goods"
        tools:text="念慈菴蜜炼川贝枇杷膏" />

    <TextView
        android:id="@+id/tv_invalidated_spec"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_11"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:layout_marginStart="@dimen/dimen_dp_17"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:singleLine="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_invalidated_goods"
        app:layout_constraintTop_toBottomOf="@+id/tv_invalidated_title"
        tools:text="12颗*1盒 / 广州白云山医药集团有时限公司" />

    <TextView
        android:id="@+id/tv_invalidated_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_ff2121"
        android:textSize="@dimen/dimen_dp_11"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:layout_marginStart="@dimen/dimen_dp_17"
        android:layout_marginBottom="@dimen/dimen_dp_1"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:singleLine="true"
        app:layout_constraintBottom_toBottomOf="@+id/iv_invalidated_goods"
        app:layout_constraintStart_toEndOf="@+id/iv_invalidated_goods"
        tools:text="￥21.5" />

    <TextView
        android:id="@+id/suggest_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_11"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:layout_marginStart="@dimen/dimen_dp_18"
        android:layout_marginBottom="@dimen/dimen_dp_1"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:singleLine="true"
        app:layout_constraintBottom_toBottomOf="@+id/tv_invalidated_price"
        app:layout_constraintStart_toEndOf="@+id/tv_invalidated_price"
        tools:text="零售价 ¥19.00" />

    <Space
        android:id="@+id/space"
        android:layout_width="@dimen/dimen_dp_10"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dimen_dp_10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_invalidated_goods" />

    <View
        android:id="@+id/bg_title_desc"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_37"
        android:background="@color/color_f7f7f8"
        app:layout_constraintTop_toBottomOf="@+id/space" />

    <TextView
        android:id="@+id/tv_title_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="为你找到以下相似商品"
        android:drawableStart="@drawable/shape_find_same_goods_dot"
        android:drawableEnd="@drawable/shape_find_same_goods_dot"
        android:drawablePadding="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="@+id/bg_title_desc"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="@dimen/dimen_dp_4"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="bg_title_desc, tv_title_desc" />

</androidx.constraintlayout.widget.ConstraintLayout>