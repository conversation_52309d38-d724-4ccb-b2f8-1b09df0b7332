<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cyContent"
        android:layout_width="@dimen/pabr_dimen95dp"
        android:layout_height="wrap_content"
        android:layout_gravity="top">

        <ImageView
            android:id="@+id/ivPic"
            android:layout_width="@dimen/pabr_dimen87dp"
            android:layout_height="@dimen/pabr_dimen87dp"
            android:layout_marginStart="@dimen/dimen_dp_4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvProName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_4"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="2"
            android:minLines="2"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/pax_core_sp_12"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@id/ivPic"
            app:layout_constraintStart_toStartOf="@id/ivPic"
            app:layout_constraintTop_toBottomOf="@id/ivPic" />

        <TextView
            android:id="@+id/tvProPrice"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:textColor="@color/color_FF2121"
            android:textSize="@dimen/sp_14"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@id/ivPic"
            app:layout_constraintStart_toStartOf="@id/ivPic"
            app:layout_constraintTop_toBottomOf="@id/tvProName" />

        <TextView
            android:id="@+id/tvSubtract"
            android:layout_width="@dimen/dimen_dp_22"
            android:layout_height="@dimen/dimen_dp_24"
            android:layout_marginTop="@dimen/dimen_dp_2"
            android:layout_marginEnd="1dp"
            android:background="@drawable/shape_f3f5f7_radius"
            android:gravity="center"
            android:text="-"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/tvNum"
            app:layout_constraintStart_toStartOf="@id/ivPic"
            app:layout_constraintTop_toBottomOf="@id/tvProPrice" />

        <TextView
            android:id="@+id/tvNum"
            android:layout_width="0dp"
            android:layout_height="@dimen/dimen_dp_24"
            android:layout_marginTop="@dimen/dimen_dp_2"
            android:background="@color/text_F3F5F7"
            android:gravity="center"
            android:text="0"
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintEnd_toStartOf="@id/tvAdd"
            app:layout_constraintStart_toEndOf="@id/tvSubtract"
            app:layout_constraintTop_toBottomOf="@id/tvProPrice" />

        <TextView
            android:id="@+id/tvAdd"
            android:layout_width="@dimen/dimen_dp_22"
            android:layout_height="@dimen/dimen_dp_24"
            android:layout_marginStart="1dp"
            android:layout_marginTop="@dimen/dimen_dp_2"
            android:background="@drawable/shape_f3f5f7_radius"
            android:gravity="center"
            android:rotation="180"
            android:text="+"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@id/ivPic"
            app:layout_constraintStart_toEndOf="@id/tvNum"
            app:layout_constraintTop_toBottomOf="@id/tvProPrice" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tvSaveMoney"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_16"
            app:rv_backgroundColor="@color/color_FF6204"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dimen_dp_5"
            android:text="省10元"
            android:textColor="@color/white"
            android:textSize="@dimen/pax_core_sp_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_cornerRadius_BL="@dimen/dimen_dp_4"
            app:rv_cornerRadius_TL="@dimen/dimen_dp_4" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageButton
        android:id="@+id/ivGoodsContact"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/pabr_dimen87dp"
        android:layout_gravity="top"
        android:layout_marginStart="@dimen/pabr_dimen95dp"
        android:background="@android:color/transparent"
        android:clickable="false"
        android:paddingHorizontal="@dimen/dimen_dp_8"
        android:src="@drawable/ic_groupbuy_add"
        app:layout_constraintStart_toEndOf="@id/cyContent"
        app:layout_constraintTop_toTopOf="parent" />
</FrameLayout>