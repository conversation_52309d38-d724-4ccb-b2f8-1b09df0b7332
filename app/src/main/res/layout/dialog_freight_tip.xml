<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="@dimen/dimen_dp_2">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dimen_dp_20"
        android:text="@string/distribution_service_charge_standard"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_17"
        android:textStyle="bold" />

    <ScrollView
        android:id="@+id/sv_freight"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_10">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_tip_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_dp_16"
                android:lineSpacingExtra="@dimen/dimen_dp_3"
                android:layout_marginEnd="@dimen/dimen_dp_16" />
        </LinearLayout>
    </ScrollView>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/colors_DDDDDD" />

    <TextView
        android:id="@+id/tv_i_got_it"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_47"
        android:gravity="center"
        android:text="@string/i_got_it"
        android:textColor="@color/color_00b377"
        android:textSize="@dimen/dimen_dp_17"
        android:textStyle="bold" />

</com.ybmmarket20.common.widget.RoundLinearLayout>