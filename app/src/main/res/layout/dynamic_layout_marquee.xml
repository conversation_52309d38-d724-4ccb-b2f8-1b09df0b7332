<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:text="药提示:" />

        <com.ybmmarket20.view.MarqueeView
            android:id="@+id/marquee_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            app:mvAnimDuration="500"
            app:mvInterval="4000"
            app:mvTextColor="@color/record_red">

        </com.ybmmarket20.view.MarqueeView>
    </LinearLayout>
</merge>