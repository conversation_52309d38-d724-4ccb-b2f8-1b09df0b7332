<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_F7F7F7">

    <include
        android:id="@+id/title"
        layout="@layout/common_header_items" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        app:layout_constraintBottom_toTopOf="@+id/clConfirmLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clConfirmLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_64"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/rvConfirm"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_44"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:text="提交"
            android:textColor="@color/white"
            android:gravity="center"
            android:textSize="@dimen/dimen_dp_16"
            android:enabled="false"
            android:background="@drawable/selector_confirm_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>