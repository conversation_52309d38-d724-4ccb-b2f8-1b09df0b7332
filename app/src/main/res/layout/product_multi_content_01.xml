<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="160dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/home_white_product_bg"
    android:orientation="horizontal"
    android:padding="2dp">


    <RelativeLayout
        android:layout_width="128dp"
        android:layout_height="128dp"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="4dp">

        <ImageView
            android:id="@+id/iv_product"
            android:layout_width="128dp"
            android:layout_height="128dp"
            android:layout_marginTop="2dp"
            android:padding="@dimen/home_product_image_padding" />

        <ImageView
            android:id="@+id/iv_tag_left"
            android:layout_width="128dp"
            android:layout_height="128dp"
            android:layout_marginTop="2dp"
            android:scaleType="fitXY"
            android:src="@drawable/transparent" />

        <com.ybmmarket20.view.PromotionTagView
            android:id="@+id/view_ptv"
            android:layout_width="128dp"
            android:layout_marginTop="2dp"
            app:subTitleTextSize="5dp"
            app:contentTextSize="9dp"
            android:layout_height="128dp" />

        <TextView
            android:id="@+id/tv_activity_price"
            style="@style/activity_price"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="35dp"
            android:gravity="center_vertical"
            android:text=""
            android:visibility="gone" />

        <TextView
            android:id="@+id/shop_no_limit_tv01"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:background="@drawable/shop_limit01"
            android:gravity="center"
            android:text=""
            android:textColor="#ffffff"
            android:textSize="12dp"
            android:visibility="gone" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginLeft="12dp"
        android:layout_weight="1">

        <LinearLayout
            android:id="@+id/ll_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/tv_procurement_festival"
                android:layout_width="42dp"
                android:layout_height="17dp"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="2dp"
                android:background="@drawable/icon_procurement_festival"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_exclusive"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="2dp"
                android:background="@drawable/bg_brand_item_exclusive"
                android:paddingLeft="2dp"
                android:paddingRight="2dp"
                android:text="独家"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_health_insurance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="2dp"
                android:background="@drawable/bg_brand_item_health_insurance"
                android:paddingLeft="2dp"
                android:paddingRight="2dp"
                android:text="医保"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:singleLine="true"
                android:text="秦皇岛 阿莫西林胶囊"
                android:textColor="@color/brand_shop_name"
                android:textSize="15sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/shop_price_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_layout"
            android:layout_marginTop="5dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_spec"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/text_9494A6"
                android:textSize="10sp" />

            <TextView
                android:id="@+id/shop_price_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:text="/"
                android:textColor="@color/text_9494A6"
                android:textSize="10sp"
                android:visibility="visible" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/shop_price_layout"
            android:layout_marginBottom="5dp"
            android:ellipsize="end"
            android:lines="1"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/record_red"
            android:textSize="@dimen/brand_shop_name" />

        <RelativeLayout
            android:id="@+id/shop_price_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/brand_rl_layout"
            android:layout_marginBottom="4dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/shop_price_kxj_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:background="@drawable/bg_brand_item_control_market1"
                android:gravity="center"
                android:minWidth="38dp"
                android:paddingLeft="1.6dp"
                android:paddingRight="1.6dp"
                android:singleLine="true"
                android:text="@string/product_list_kxj_title"
                android:textColor="@color/detail_shop_price_kxj_title_tv"
                android:textSize="11sp"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/shop_price_kxj_number_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/shop_price_kxj_title_tv"
                android:background="@drawable/bg_brand_item_control_market2"
                android:gravity="center_vertical"
                android:paddingLeft="1.6dp"
                android:paddingRight="1.6dp"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/text_676773"
                android:textSize="11sp"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/shop_price_ml_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="5dp"
                android:layout_toRightOf="@+id/shop_price_kxj_number_tv"
                android:background="@drawable/bg_brand_item_control_market1"
                android:gravity="center"
                android:minWidth="38dp"
                android:paddingLeft="1.6dp"
                android:paddingRight="1.6dp"
                android:singleLine="true"
                android:text="@string/product_list_ml_title"
                android:textColor="@color/detail_shop_price_kxj_title_tv"
                android:textSize="11sp"
                android:visibility="invisible" />

                <TextView
                    android:id="@+id/shop_price_ml_number_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/shop_price_ml_title_tv"
                    android:background="@drawable/bg_brand_item_control_market2"
                    android:gravity="center_vertical"
                    android:paddingLeft="1.6dp"
                    android:paddingRight="1.6dp"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/text_676773"
                    android:textSize="11sp"
                    android:visibility="invisible" />
            </RelativeLayout>

        <RelativeLayout
            android:id="@+id/brand_rl_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_alignParentLeft="true"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="3dp">

            <com.ybmmarket20.view.TagView
                android:id="@+id/rl_icon_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <RelativeLayout
                android:id="@+id/el_edit_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true">

                <com.ybmmarket20.view.ProductEditLayout
                    android:id="@+id/el_edit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_brand_control"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="暂无购买权限"
                    android:textColor="@color/brand_control"
                    android:textSize="14sp"
                    android:visibility="gone" />
            </RelativeLayout>
        </RelativeLayout>
    </RelativeLayout>

</LinearLayout>