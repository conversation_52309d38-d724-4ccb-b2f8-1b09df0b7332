<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:layout_marginTop="1dp"
              android:background="@color/white"
              android:orientation="horizontal"
              android:padding="@dimen/normal_margin">
    <ImageView
        android:id="@+id/iv_supplier_logo"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:scaleType="centerInside"/>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/normal_margin"
        android:layout_marginRight="@dimen/normal_margin"
        android:layout_weight="1"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_supplier_name"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:text="江中集团"
            android:textColor="@color/text_292933"
            android:textSize="@dimen/text_simple_size"
            android:textStyle="bold"/>
        <LinearLayout
            android:id="@+id/ll_look_info"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_vertical">
            <com.ybmmarket20.common.widget.RoundTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:text="查看资质"
                android:textColor="@color/text_676773"
                android:textSize="@dimen/text_simple_small_size"
                app:rv_cornerRadius="10dp"
                app:rv_strokeColor="@color/text_676773"
                app:rv_strokeWidth="1dp"/>
        </LinearLayout>
    </LinearLayout>
    <TextView
        android:id="@+id/tv_into"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:drawableRight="@drawable/icon_right_gray"
        android:gravity="center"
        android:text="进店"
        android:textColor="@color/text_676773"
        android:textSize="@dimen/text_simple_size"/>
</LinearLayout>