<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivShare"
        android:layout_width="@dimen/dimen_dp_51"
        android:layout_height="@dimen/dimen_dp_51"
        android:src="@drawable/icon_share_wechat"
        android:layout_marginTop="@dimen/dimen_dp_18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvShare"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="图片分享微信"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="#363636"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivShare" />

</androidx.constraintlayout.widget.ConstraintLayout>