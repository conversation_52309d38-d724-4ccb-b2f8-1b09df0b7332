<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:layout_marginTop="18dp">

        <TextView
            android:id="@+id/tv_goods_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="共计3种药品"
            android:textColor="@color/text_292933"
            android:textSize="14sp" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_batch_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingLeft="20dp"
            android:paddingTop="4dp"
            android:paddingRight="20dp"
            android:paddingBottom="4dp"
            android:text="一键加购"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:rv_backgroundColor="#00B377"
            app:rv_cornerRadius="17dp" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="23dp"
        android:background="@color/white" />

</LinearLayout>