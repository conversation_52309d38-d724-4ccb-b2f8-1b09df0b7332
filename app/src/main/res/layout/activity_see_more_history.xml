<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <RelativeLayout
        android:id="@+id/ll_title"
        style="@style/header_layout">

        <ImageView
            android:id="@+id/iv_back"
            style="@style/header_layout_left"
            android:src="@drawable/ic_back" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/header_layout_mid"
            android:text="更多历史记录" />

        <LinearLayout
            android:id="@+id/ll_clean"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginRight="15dp"
            >
        <ImageView
            android:id="@+id/iv_clean"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_clean"
            android:layout_gravity="center_vertical"
            />
            <TextView
                android:id="@+id/tv_clean"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#9494A6"
                android:textSize="@dimen/dimen_dp_12"
                android:layout_gravity="center_vertical"
                android:text="清空" />
        </LinearLayout>

    </RelativeLayout>

    <com.ybmmarket20.common.statusview.StatusViewLayout
        android:id="@+id/see_More_StatusView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.ybm.app.view.CommonRecyclerView
            android:id="@+id/rv_see_more_his_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:descendantFocusability="blocksDescendants"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            android:paddingBottom="@dimen/dp_10"
            />

    </com.ybmmarket20.common.statusview.StatusViewLayout>

</LinearLayout>
