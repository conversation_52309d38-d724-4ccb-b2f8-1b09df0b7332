<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.ybmmarketkotlin.views.FlexBoxLayoutMaxLines
        android:id="@+id/fblml_order_search"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:background="@color/white" />
</androidx.constraintlayout.widget.ConstraintLayout>