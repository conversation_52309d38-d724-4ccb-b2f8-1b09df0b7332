<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:layout_marginBottom="1px"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp">

    <com.ybmmarket20.common.widget.RoundRelativeLayout
        android:id="@+id/fg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        card_view:rv_backgroundColor="@color/white"
        card_view:rv_cornerRadius_TL="2dp"
        card_view:rv_cornerRadius_TR="2dp">

        <TextView
            android:id="@+id/cart_new_rl_tv01"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@+id/cart_new_rl_iv"
            android:text="以下商品不可购买"
            android:textColor="@color/black"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/cart_new_rl_tv02"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:text="清空"
            android:textColor="@color/color_00b377"
            android:textSize="13sp"
            android:visibility="visible" />

    </com.ybmmarket20.common.widget.RoundRelativeLayout>
</RelativeLayout>