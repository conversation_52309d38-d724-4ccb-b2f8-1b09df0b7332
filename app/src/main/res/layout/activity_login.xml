<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    android:orientation="vertical"
    android:padding="20dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/registEvent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/regist"
        android:textColor="#292933"
        android:textSize="14sp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/welcomeTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="75dp"
        android:text="@string/login_welcome"
        android:textColor="#292933"
        android:textSize="27sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/registEvent" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/login_user_name_wrapper"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="41dp"
        android:textColorHint="@color/loginTextAppearance"
        android:theme="@style/inputLayoutLineColor"
        app:hintTextAppearance="@style/inputLayoutHintAppearance"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/welcomeTv">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/login_et1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="请输入手机号"
            android:inputType="number"
            android:maxLength="11"
            android:singleLine="true"
            android:textColor="#292933"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/login_password_wrapper"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:textColorHint="@color/loginTextAppearance"
        android:theme="@style/inputLayoutLineColor"
        app:hintTextAppearance="@style/inputLayoutHintAppearance"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/login_user_name_wrapper">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/login_et2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="请输入登录密码"
            android:inputType="textPassword"
            android:singleLine="true"
            android:textColor="#292933"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/login_forget"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="@color/colors_9595A6"
        app:layout_constraintTop_toTopOf="@+id/login_password_wrapper"
        app:layout_constraintBottom_toBottomOf="@+id/login_password_wrapper"
        app:layout_constraintEnd_toEndOf="@+id/login_password_wrapper"
        android:layout_marginEnd="@dimen/dimen_dp_5"
        android:text="@string/forget_passwd_with_symbol"/>

    <View
        android:layout_width="@dimen/dimen_dp_1"
        android:layout_height="@dimen/dimen_dp_12"
        app:layout_constraintTop_toTopOf="@+id/login_password_wrapper"
        app:layout_constraintBottom_toBottomOf="@+id/login_password_wrapper"
        app:layout_constraintEnd_toStartOf="@+id/login_forget"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:background="@color/color_a1a5b0" />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/cb_forget_passwd"
        android:layout_width="@dimen/dimen_dp_18"
        android:layout_height="@dimen/dimen_dp_16"
        app:layout_constraintTop_toTopOf="@+id/login_password_wrapper"
        app:layout_constraintBottom_toBottomOf="@+id/login_password_wrapper"
        app:layout_constraintEnd_toStartOf="@+id/login_forget"
        android:layout_marginEnd="21dp"
        android:button="@null"
        android:background="@drawable/selector_password_toggle"/>


    <com.ybmmarket20.view.ButtonObserver
        android:id="@+id/login_btn"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="50dp"
        android:background="@drawable/selector_common_btn"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/login"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/login_password_wrapper" />

    <com.ybmmarket20.view.CheckPrivacyView
        android:id="@+id/checkPrivacy"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="17dp"
        app:layout_constraintTop_toBottomOf="@+id/login_btn" />


    <TextView
        android:id="@+id/change_to_debug"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="40dp"
        android:background="@drawable/selector_common_btn"
        android:gravity="center"
        android:text="Debug"
        android:textSize="@dimen/dimen_dp_16"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/login_btn"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>