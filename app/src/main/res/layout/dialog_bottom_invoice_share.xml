<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#ffffff"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="21dp">

            <LinearLayout
                android:id="@+id/ll_share_wx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:src="@drawable/icon_share_wx2" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:text="微信好友"
                    android:textColor="@color/text_9494A6"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_share_qq"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="55dp"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:src="@drawable/icon_share_qq" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="8dp"
                    android:text="QQ"
                    android:textColor="@color/text_9494A6"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_1"
            android:layout_marginTop="@dimen/dimen_dp_20"
            android:background="@color/product_background"/>

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_18" />
    </LinearLayout>
</LinearLayout>