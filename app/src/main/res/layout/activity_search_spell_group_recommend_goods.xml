<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/base_header_default_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/title_left_search"
            android:layout_width="wrap_content"
            android:layout_height="25dp"
            android:layout_gravity="center"
            android:paddingStart="15dp"
            android:paddingEnd="10dp"
            android:src="@drawable/ic_back" />

        <RelativeLayout
            android:id="@+id/rel_search"
            android:layout_width="0dp"
            android:layout_height="@dimen/dimen_dp_34"
            android:layout_weight="1"
            android:background="@drawable/search_round_corner_gray_bg_03"
            android:focusable="true">

            <ImageView
                android:id="@+id/iv_a_magnifying_glass"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="6dp"
                android:src="@drawable/icon_home_steady_scan" />

            <View
                android:id="@+id/v_splite_line"
                android:layout_width="1dp"
                android:layout_height="18dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="2dp"
                android:layout_toRightOf="@id/iv_a_magnifying_glass"
                android:background="@color/gray_C4C4C4" />

            <EditText
                android:id="@+id/title_et"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:layout_marginLeft="3dp"
                android:layout_toLeftOf="@id/iv_clear"
                android:layout_toRightOf="@id/v_splite_line"
                android:background="@null"
                android:ellipsize="end"
                android:hint="搜索此商家随心拼商品"
                android:imeOptions="actionSearch"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/color_292933"
                android:textColorHint="@color/color_9494A6"
                android:textCursorDrawable="@drawable/color_cursor"
                android:textSize="13dp" />

            <ImageView
                android:id="@+id/iv_clear"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:src="@drawable/icon_home_steady_voice"
                android:visibility="visible" />

        </RelativeLayout>

        <ImageView
            android:id="@+id/iv_service"
            android:layout_width="42dp"
            android:layout_height="@dimen/dimen_dp_42"
            android:paddingStart="10dp"
            android:paddingTop="6dp"
            android:paddingEnd="10dp"
            android:paddingBottom="6dp"
            android:src="@drawable/icon_customer_service"
            app:layout_constraintBottom_toBottomOf="@id/iv_back"
            app:layout_constraintLeft_toRightOf="@id/ll_search_container"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_back" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        tools:itemCount="10"
        tools:listitem="@layout/item_search_spell_group_recommend_goods"
        app:layout_constraintVertical_weight="1"
        app:layout_constraintTop_toBottomOf="@+id/ll_title"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>