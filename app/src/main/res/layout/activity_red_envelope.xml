<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <include
        android:id="@+id/header"
        layout="@layout/common_header_items" />

    <View
        android:id="@+id/header_space1"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_9"
        android:background="@color/color_FAFAFA"
        app:layout_constraintTop_toBottomOf="@+id/header" />

    <TextView
        android:id="@+id/tv_red_envelope_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_20"
        android:text="0.0"
        android:textColor="#00B377"
        android:textSize="24dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_space1"
        tools:text="1000" />

    <TextView
        android:id="@+id/tv_red_envelope_amount_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_7"
        android:text="可用余额(元)"
        android:textColor="@color/balance_extract_text"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_red_envelope_amount" />

    <View
        android:id="@+id/rg_class_selector_top_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:layout_marginTop="@dimen/dimen_dp_27"
        android:background="@color/color_FAFAFA"
        app:layout_constraintTop_toBottomOf="@+id/tv_red_envelope_amount_tip" />

    <RadioGroup
        android:id="@+id/rg_class_selector"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_54"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/rg_class_selector_top_line">

        <RadioButton
            android:id="@+id/rb_my_red_envelope"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:button="@null"
            android:gravity="center"
            android:text="我的红包"
            android:checked="true"
            android:background="@color/transparent"
            android:textColor="@color/selector_red_envelope" />

        <View
            android:layout_width="@dimen/dimen_dp_1"
            android:layout_height="match_parent"
            android:background="@color/color_FAFAFA" />

        <RadioButton
            android:id="@+id/rb_my_red_envelope_record"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:button="@null"
            android:gravity="center"
            android:text="收支记录"
            android:background="@color/transparent"
            android:textColor="@color/selector_red_envelope" />

    </RadioGroup>

    <View
        android:id="@+id/header_space2"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_9"
        android:background="@color/color_FAFAFA"
        app:layout_constraintTop_toBottomOf="@+id/rg_class_selector" />

    <FrameLayout
        android:id="@+id/fl_red_envelope"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_space2"
        app:layout_constraintVertical_weight="1" />


</androidx.constraintlayout.widget.ConstraintLayout>