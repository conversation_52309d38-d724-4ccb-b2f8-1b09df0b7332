<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginBottom="@dimen/dimen_dp_10"
    android:layout_marginHorizontal="@dimen/dimen_dp_5">

    <TextView
        android:id="@+id/tvCnMedineExtTitle"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:gravity="center"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:text="规格"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvCnMedineExtCollapse"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:drawableEnd="@drawable/icon_search_filter_title_arrow_down"
        android:gravity="center_vertical"
        android:layout_marginEnd="@dimen/dimen_dp_5"
        android:text="展开"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_11"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvCnMedineExt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tvCnMedineExtTitle"
        tools:layout_height="@dimen/dimen_dp_100" />
</androidx.constraintlayout.widget.ConstraintLayout>