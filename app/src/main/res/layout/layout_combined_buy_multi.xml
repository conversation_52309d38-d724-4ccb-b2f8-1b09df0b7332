<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/pabr_dimen12dp"
    app:rv_cornerRadius="@dimen/pabr_dimen4dp"
    app:rv_strokeColor="@color/colors_FFC97E"
    app:rv_strokeWidth="0.5dp">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="0.5dp"
        android:layout_marginTop="0.5dp"
        android:background="@drawable/shape_product_combinedbuy_top_bg"
        android:drawableStart="@drawable/ic_combined_multi_title"
        android:drawablePadding="@dimen/dimen_dp_4"
        android:gravity="start|center_vertical"
        android:paddingStart="@dimen/dimen_dp_8"
        android:paddingTop="@dimen/pabr_dimen10dp"
        android:paddingBottom="@dimen/pabr_dimen10dp"
        android:text="加价购更优惠"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/pax_core_sp_14"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rlvGoods"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_8"
        android:layout_marginEnd="0.5dp"
        android:minHeight="@dimen/pabr_dimen179dp"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dimen_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rlvGoods">

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/color_e8e8e8"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTotalPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_6"
            android:gravity="center"
            android:text="￥30"
            android:textColor="@color/color_FF2121"
            android:textSize="@dimen/pax_core_sp_12"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTotalCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="已选3种50盒"
            android:textColor="@color/text_color_666666"
            android:textSize="@dimen/pax_core_sp_12"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTotalPrice" />

        <TextView
            android:id="@+id/tvDiscount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_4"
            android:gravity="center"
            android:text="已优惠￥2"
            android:textColor="@color/color_FF2121"
            android:textSize="@dimen/pax_core_sp_12"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/tvTotalCount"
            app:layout_constraintTop_toBottomOf="@id/tvTotalPrice" />


        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tvSettle"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_32"
            android:gravity="center"
            android:paddingHorizontal="@dimen/pabr_dimen10dp"
            android:text="去下单"
            android:textColor="@color/white"
            android:textSize="@dimen/pabr_dimen14sp"
            app:layout_constraintBottom_toBottomOf="@id/tvTotalCount"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvTotalPrice"
            app:rv_backgroundColor="@color/color_00b955"
            app:rv_cornerRadius="@dimen/dimen_dp_4" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.ybmmarket20.common.widget.RoundConstraintLayout>