<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              xmlns:tools="http://schemas.android.com/tools"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:baselineAligned="false"
              android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg"
        android:focusable="true"
        android:focusableInTouchMode="true">

        <com.ybmmarket20.view.DetailScrollView
            android:id="@+id/detail_scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="48dp"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/one"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!--产品图片-->
                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="322dp"
                        android:background="@color/white"
                        app:layout_scrollFlags="scroll|enterAlways">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal">

                            <com.ybmmarket20.view.CommodityBannerLayout
                                android:id="@+id/brand_iv"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_centerInParent="true"/>

                            <ImageView
                                android:id="@+id/iv_brand_mark"
                                android:layout_width="322dp"
                                android:layout_height="322dp"
                                android:layout_centerInParent="true"
                                android:scaleType="fitXY"
                                android:src="@drawable/transparent"/>

                            <TextView
                                android:id="@+id/tv_activity_price"
                                style="@style/activity_price"
                                android:layout_height="36dp"
                                android:layout_alignParentBottom="true"
                                android:layout_marginLeft="150dp"
                                android:text=""
                                android:textSize="20dp"
                                android:visibility="gone"/>

                        </RelativeLayout>

                        <TextView
                            android:id="@+id/tv_sold_out"
                            android:layout_width="90dp"
                            android:layout_height="90dp"
                            android:layout_centerInParent="true"
                            android:layout_gravity="center"
                            android:background="@drawable/shop_limit01"
                            android:gravity="center"
                            android:text=""
                            android:textColor="#ffffff"
                            android:textSize="16sp"
                            android:visibility="gone"/>

                        <ImageView
                            android:id="@+id/iv_video_paly"
                            android:layout_width="55dp"
                            android:layout_height="55dp"
                            android:layout_gravity="center"
                            android:src="@drawable/icon_video_play"
                            android:visibility="gone"/>

                    </FrameLayout>

                    <include layout="@layout/detail_time_promotion_layout"/>

                    <!--产品详情-->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/base_bg"
                        android:orientation="vertical"
                        app:layout_scrollFlags="scroll|enterAlways">

                        <!--产品规格名称-->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:orientation="vertical"
                                android:paddingBottom="13dp"
                                android:paddingLeft="10dp"
                                android:paddingRight="10dp"
                                android:paddingTop="5dp">

                                <LinearLayout
                                    android:id="@+id/lv_otc_name"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <ImageView
                                        android:id="@+id/tv_procurement_festival"
                                        android:layout_width="42dp"
                                        android:layout_height="17dp"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginRight="5dp"
                                        android:background="@drawable/icon_procurement_festival"
                                        android:paddingLeft="5dp"
                                        android:paddingRight="5dp"
                                        android:textColor="@color/white"
                                        android:visibility="gone"
                                        tools:visibility="visible"/>

                                    <com.ybmmarket20.common.widget.RoundTextView
                                        android:id="@+id/iv_company_name"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="4dp"
                                        android:gravity="center_vertical"
                                        android:paddingLeft="5dp"
                                        android:paddingRight="5dp"
                                        android:text="自营"
                                        android:textColor="@color/white"
                                        android:textSize="14sp"
                                        android:visibility="gone"
                                        app:rv_backgroundColor="@color/base_colors"
                                        app:rv_cornerRadius="2dp"
                                        tools:visibility="visible"/>

                                    <TextView
                                        android:id="@+id/iv_exclusive"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="4dp"
                                        android:background="@drawable/bg_brand_item_exclusive"
                                        android:gravity="center_vertical"
                                        android:paddingLeft="5dp"
                                        android:paddingRight="5dp"
                                        android:text="独家"
                                        android:textColor="@color/white"
                                        android:textSize="14sp"
                                        android:visibility="gone"/>

                                    <TextView
                                        android:id="@+id/tv_health_insurance"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="4dp"
                                        android:background="@drawable/bg_brand_item_health_insurance"
                                        android:gravity="center_vertical"
                                        android:paddingLeft="5dp"
                                        android:paddingRight="5dp"
                                        android:text="医保"
                                        android:textColor="@color/white"
                                        android:textSize="14sp"
                                        android:visibility="gone"/>

                                    <ImageView
                                        android:id="@+id/iv_otc"
                                        android:layout_width="30dp"
                                        android:layout_height="18dp"
                                        android:layout_centerVertical="true"
                                        android:layout_marginRight="5dp"
                                        android:gravity="center_vertical"/>

                                    <TextView
                                        android:id="@+id/tv_name"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:ellipsize="end"
                                        android:gravity="center_vertical"
                                        android:maxLines="1"
                                        android:text=""
                                        android:textColor="@color/tv_detail_color_name"
                                        android:textSize="16sp"/>
                                </LinearLayout>

                                <TextView
                                    android:id="@+id/tv_subtitle"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:layout_below="@+id/lv_otc_name"
                                    android:ellipsize="end"
                                    android:gravity="center_vertical"
                                    android:text=""
                                    android:textColor="@color/text_9494A6"
                                    android:textSize="12sp"/>

                            </RelativeLayout>

                            <RelativeLayout
                                android:id="@+id/rl_price_layout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:minHeight="50dp"
                                android:orientation="horizontal"
                                android:paddingLeft="10dp"
                                android:paddingRight="10dp">

                                <TextView
                                    android:id="@+id/tv_tax_amount"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_centerVertical="true"
                                    android:text="¥"
                                    android:textColor="@color/tv_tax"
                                    android:textSize="21sp"
                                    android:textStyle="bold"/>

                                <TextView
                                    android:id="@+id/tv_original_price"
                                    style="@style/commodity_tv_style_02"
                                    android:layout_width="wrap_content"
                                    android:layout_centerVertical="true"
                                    android:layout_marginLeft="10dp"
                                    android:layout_toRightOf="@+id/tv_tax_amount"
                                    android:text="¥"
                                    android:textSize="14sp"/>

                                <TextView
                                    android:id="@+id/tv_control"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_centerVertical="true"
                                    android:background="@color/white"
                                    android:text="暂无购买权限"
                                    android:textColor="@color/tv_control"
                                    android:textSize="18sp"
                                    android:visibility="gone"/>

                                <com.ybmmarket20.view.MyGridView
                                    android:id="@+id/tv_list"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:numColumns="3"
                                    android:visibility="gone"/>

                                <View
                                    android:id="@+id/line_depreciate_inform"
                                    android:layout_width="50dp"
                                    android:layout_height="48dp"
                                    android:layout_marginLeft="-10dp"
                                    android:layout_marginRight="-10dp"
                                    android:layout_toLeftOf="@+id/tv_depreciate_inform"
                                    android:background="@drawable/shape_line_dash"
                                    android:layerType="software"/>

                                <TextView
                                    android:id="@+id/tv_depreciate_inform"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_alignParentRight="true"
                                    android:layout_centerVertical="true"
                                    android:drawableTop="@drawable/icon_clock"
                                    android:gravity="center"
                                    android:text="降价通知"
                                    android:textColor="#8e8e8e"
                                    android:textSize="9sp"/>

                            </RelativeLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <!--效期-->
                                <RelativeLayout
                                    android:id="@+id/rl_validity"
                                    android:layout_width="match_parent"
                                    android:layout_height="44dp"
                                    android:layout_marginTop="10dp"
                                    android:background="@color/white"
                                    android:paddingLeft="10dp">

                                    <TextView
                                        android:id="@+id/tv_layout_08"
                                        style="@style/commodity_tv_style_02"
                                        android:text="@string/detail_tv_expiry_date"/>

                                    <TextView
                                        android:id="@+id/tv_validity"
                                        style="@style/commodity_tv_style"
                                        android:layout_toRightOf="@+id/tv_layout_08"/>

                                </RelativeLayout>

                                <RelativeLayout
                                    android:id="@+id/rl_limit_price"
                                    android:layout_width="match_parent"
                                    android:layout_height="44dp"
                                    android:background="@color/white"
                                    android:paddingLeft="10dp"
                                    android:paddingRight="10dp">

                                    <TextView
                                        android:id="@+id/tv_repertory_text"
                                        style="@style/commodity_tv_style_02"
                                        android:text="库存"/>

                                    <TextView
                                        android:id="@+id/tv_repertory"
                                        style="@style/commodity_tv_style"
                                        android:layout_width="wrap_content"
                                        android:layout_toRightOf="@+id/tv_repertory_text"
                                        android:gravity="center"
                                        android:textColor="@color/text_292933"/>

                                    <TextView
                                        android:id="@+id/tv_limit"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_centerVertical="true"
                                        android:layout_marginLeft="2dp"
                                        android:layout_toRightOf="@+id/tv_repertory"
                                        android:background="@drawable/forget_red_bg"
                                        android:paddingBottom="1dp"
                                        android:paddingLeft="3dp"
                                        android:paddingRight="3dp"
                                        android:paddingTop="1dp"
                                        android:text="限购20件"
                                        android:textColor="@color/detail_tv_forget_color"
                                        android:textSize="@dimen/detail_tv_dimen_14sp"
                                        android:visibility="invisible"/>

                                </RelativeLayout>

                                <!--控销价-->
                                <RelativeLayout
                                    android:id="@+id/ly_product_price_kxj_ml"
                                    android:layout_width="match_parent"
                                    android:layout_height="44dp"
                                    android:background="@color/white"
                                    android:paddingLeft="10dp"
                                    android:paddingRight="10dp">

                                    <TextView
                                        android:id="@+id/tv_ontrol_market"
                                        style="@style/commodity_tv_style_02"
                                        android:text="控销价"/>

                                    <RelativeLayout
                                        android:id="@+id/shop_price_layout"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_toRightOf="@+id/tv_ontrol_market">

                                        <TextView
                                            android:id="@+id/tv_product_price_kxj"
                                            style="@style/brand_item_kxj_lsj_base"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="¥20.05"
                                            android:textColor="@color/text_292933"
                                            android:textSize="15sp"
                                            android:visibility="visible"/>

                                        <TextView
                                            android:id="@+id/tv_product_price_ml"
                                            style="@style/brand_item_kxj_lsj_base"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_toRightOf="@+id/tv_product_price_kxj"
                                            android:maxLines="1"
                                            android:singleLine="true"
                                            android:text="（毛利率 40%）"
                                            android:textColor="@color/text_292933"
                                            android:textSize="15sp"
                                            android:visibility="visible"/>
                                    </RelativeLayout>
                                </RelativeLayout>
                            </LinearLayout>

                            <RelativeLayout
                                android:id="@+id/rl_third_party"
                                android:layout_width="match_parent"
                                android:layout_height="44dp"
                                android:layout_marginTop="10dp"
                                android:background="@color/white"
                                android:gravity="center_vertical"
                                android:paddingLeft="10dp"
                                android:visibility="gone">

                                <RelativeLayout
                                    android:id="@+id/rl2_third_party"
                                    android:layout_width="@dimen/detail_tv_wrap_91dp"
                                    android:layout_height="match_parent">

                                    <com.ybmmarket20.common.widget.RoundTextView
                                        android:id="@+id/tv_third_party"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_centerVertical="true"
                                        android:paddingBottom="3dp"
                                        android:paddingLeft="5dp"
                                        android:paddingRight="5dp"
                                        android:paddingTop="3dp"
                                        android:text="第三方发货"
                                        android:textColor="@color/detail_tv_color_9494A6"
                                        android:textSize="@dimen/detail_tv_dimen_14sp"
                                        app:rv_cornerRadius="2dp"
                                        app:rv_strokeColor="@color/detail_tv_color_9494A6"
                                        app:rv_strokeWidth="1dp"/>

                                </RelativeLayout>

                                <TextView
                                    android:id="@+id/tv_manufacturers"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_centerVertical="true"
                                    android:layout_toRightOf="@+id/rl2_third_party"
                                    android:drawableRight="@drawable/icon_show_promition_action"
                                    android:gravity="center_vertical"
                                    android:maxLines="1"
                                    android:paddingRight="10dp"
                                    android:singleLine="true"
                                    android:text=""
                                    android:textColor="#292933"
                                    android:textSize="14sp"/>
                            </RelativeLayout>

                            <RelativeLayout
                                android:id="@+id/rl_coupon_or_promotion"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp">

                                <!--优惠券-->
                                <RelativeLayout
                                    android:id="@+id/rl_coupon"
                                    android:layout_width="match_parent"
                                    android:layout_height="44dp"
                                    android:background="@color/white"
                                    android:gravity="center_vertical"
                                    android:paddingLeft="10dp"
                                    android:paddingRight="5dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_coupon"
                                        style="@style/commodity_tv_style_02"
                                        android:text="领券："/>

                                    <TextView
                                        android:id="@+id/tv_coupon_one"
                                        style="@style/detail_coupon_base"
                                        android:layout_toRightOf="@+id/tv_coupon"
                                        android:text=""/>

                                    <TextView
                                        android:id="@+id/tv_coupon_two"
                                        style="@style/detail_coupon_base"
                                        android:layout_marginLeft="8dp"
                                        android:layout_toRightOf="@+id/tv_coupon_one"
                                        android:text=""/>

                                    <ImageView style="@style/detail_iv_base"/>

                                </RelativeLayout>

                                <!--促销标签-->
                                <LinearLayout
                                    android:id="@+id/ll_show_promotion"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/rl_coupon"
                                    android:background="@color/white"
                                    android:gravity="center_vertical"
                                    android:minHeight="50dp"
                                    android:paddingLeft="10dp"
                                    android:paddingRight="5dp">

                                    <TextView
                                        android:id="@+id/tv_show_promotion"
                                        style="@style/commodity_tv_style_02"
                                        android:layout_gravity="top"
                                        android:layout_marginTop="13dp"
                                        android:text="促销"/>

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_marginBottom="13dp"
                                        android:layout_marginTop="13dp"
                                        android:layout_toRightOf="@+id/tv_show_promotion"
                                        android:layout_weight="1"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:id="@+id/ll_01"
                                            style="@style/detail_promotion_base">

                                            <TextView
                                                android:id="@+id/tv_icon_type_01"
                                                style="@style/detail_promotion_tv"
                                                android:text=""/>

                                            <ImageView
                                                android:id="@+id/iv_icon_type_01"
                                                style="@style/detail_promotion_iv"
                                                android:visibility="gone"/>

                                            <TextView
                                                android:id="@+id/tv_content_type_01"
                                                style="@style/detail_promotion_tv02"
                                                android:text=""/>

                                        </LinearLayout>

                                        <LinearLayout
                                            android:id="@+id/ll_02"
                                            style="@style/detail_promotion_base">

                                            <TextView
                                                android:id="@+id/tv_icon_type_02"
                                                style="@style/detail_promotion_tv"
                                                android:text=""/>

                                            <ImageView
                                                android:id="@+id/iv_icon_type_02"
                                                style="@style/detail_promotion_iv"
                                                android:visibility="gone"/>

                                            <TextView
                                                android:id="@+id/tv_content_type_02"
                                                style="@style/detail_promotion_tv02"
                                                android:text=""/>

                                        </LinearLayout>

                                        <LinearLayout
                                            android:id="@+id/ll_03"
                                            style="@style/detail_promotion_base">

                                            <TextView
                                                android:id="@+id/tv_icon_type_03"
                                                style="@style/detail_promotion_tv"
                                                android:text=""/>

                                            <ImageView
                                                android:id="@+id/iv_icon_type_03"
                                                style="@style/detail_promotion_iv"
                                                android:visibility="gone"/>

                                            <TextView
                                                android:id="@+id/tv_content_type_03"
                                                style="@style/detail_promotion_tv02"
                                                android:text=""/>

                                        </LinearLayout>
                                    </LinearLayout>

                                    <ImageView
                                        style="@style/detail_iv_base"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="top"
                                        android:layout_marginTop="13dp"/>

                                </LinearLayout>
                            </RelativeLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="44dp"
                                android:layout_marginTop="10dp"
                                android:background="@color/white"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                android:paddingBottom="3dp"
                                android:paddingLeft="10dp"
                                android:paddingRight="5dp">

                                <TextView
                                    style="@style/commodity_tv_style_02"
                                    android:layout_gravity="center_vertical"
                                    android:gravity="center_vertical"
                                    android:text="服务"/>

                                <com.ybmmarket20.view.MyGridView
                                    android:id="@+id/pl_service"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:numColumns="3"
                                    android:visibility="gone"/>

                                <ImageView
                                    android:id="@+id/iv_service"
                                    style="@style/detail_iv_base"/>

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:background="@color/white"
                                android:gravity="center_vertical"
                                android:orientation="vertical"
                                android:paddingBottom="3dp"
                                android:paddingLeft="10dp">
                                <!--规格-->
                                <RelativeLayout
                                    android:id="@+id/rl_spec"
                                    android:layout_width="match_parent"
                                    android:layout_height="33dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_01"
                                        style="@style/commodity_tv_style_02"
                                        android:text="@string/detail_tv_spec"/>

                                    <TextView
                                        android:id="@+id/tv_spec"
                                        style="@style/commodity_tv_style"
                                        android:layout_toRightOf="@+id/tv_layout_01"/>
                                </RelativeLayout>
                                <!--中包装-->
                                <RelativeLayout
                                    android:id="@+id/rl_medium_package"
                                    android:layout_width="match_parent"
                                    android:layout_height="33dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_02"
                                        style="@style/commodity_tv_style_02"
                                        android:text="@string/detail_tv_medium_package"/>

                                    <TextView
                                        android:id="@+id/tv_medium_package"
                                        style="@style/commodity_tv_style"
                                        android:layout_width="wrap_content"
                                        android:layout_toRightOf="@+id/tv_layout_02"/>

                                    <TextView
                                        android:id="@+id/tv_possible_to_disassemble"
                                        style="@style/commodity_tv_style"
                                        android:layout_width="wrap_content"
                                        android:layout_marginBottom="2dp"
                                        android:layout_marginLeft="10dp"
                                        android:layout_marginTop="2dp"
                                        android:layout_toRightOf="@+id/tv_medium_package"
                                        android:background="@drawable/bg_possible_to_disassemble"
                                        android:gravity="center"
                                        android:paddingLeft="5dp"
                                        android:paddingRight="5dp"
                                        android:text="不可拆零"
                                        android:textColor="@color/white"
                                        android:visibility="gone"/>
                                </RelativeLayout>
                                <!--件包装-->
                                <RelativeLayout
                                    android:id="@+id/rl_letter_package"
                                    android:layout_width="match_parent"
                                    android:layout_height="33dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_03"
                                        style="@style/commodity_tv_style_02"
                                        android:text="@string/detail_tv_letter_package"/>

                                    <TextView
                                        android:id="@+id/tv_letter_package"
                                        style="@style/commodity_tv_style"
                                        android:layout_toRightOf="@+id/tv_layout_03"/>
                                </RelativeLayout>
                                <!--控销价格-->
                                <RelativeLayout
                                    android:id="@+id/rl_control"
                                    android:layout_width="match_parent"
                                    android:layout_height="33dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_control_layout_03"
                                        style="@style/commodity_tv_style_02"
                                        android:text="@string/detail_tv_control_price"/>

                                    <TextView
                                        android:id="@+id/tv_control_price"
                                        style="@style/commodity_tv_style"
                                        android:layout_toRightOf="@+id/tv_control_layout_03"/>
                                </RelativeLayout>
                                <!--建议零售价-->
                                <RelativeLayout
                                    android:id="@+id/rl_suggested_retail_price"
                                    android:layout_width="match_parent"
                                    android:layout_height="33dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_04"
                                        style="@style/commodity_tv_style_02"
                                        android:text="@string/detail_tv_suggested_retail_price"/>

                                    <TextView
                                        android:id="@+id/tv_suggested_retail_price"
                                        style="@style/commodity_tv_style"
                                        android:layout_toRightOf="@+id/tv_layout_04"/>
                                </RelativeLayout>
                                <!--生产厂家-->
                                <RelativeLayout
                                    android:id="@+id/rl_manufacturer"
                                    android:layout_width="match_parent"
                                    android:layout_height="33dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_05"
                                        style="@style/commodity_tv_style_02"
                                        android:text="@string/detail_tv_manufacturer"/>

                                    <TextView
                                        android:id="@+id/tv_manufacturer"
                                        style="@style/commodity_tv_style"
                                        android:layout_toRightOf="@+id/tv_layout_05"/>

                                </RelativeLayout>
                                <!--批准文号-->
                                <RelativeLayout
                                    android:id="@+id/rl_approval_number"
                                    android:layout_width="match_parent"
                                    android:layout_height="33dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_06"
                                        style="@style/commodity_tv_style_02"
                                        android:text="@string/detail_tv_approval_number"/>

                                    <TextView
                                        android:id="@+id/tv_approval_number"
                                        style="@style/commodity_tv_style"
                                        android:layout_toRightOf="@+id/tv_layout_06"/>

                                </RelativeLayout>
                                <!--保质期-->
                                <RelativeLayout
                                    android:id="@+id/rl_expiration_date"
                                    android:layout_width="match_parent"
                                    android:layout_height="33dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_07"
                                        style="@style/commodity_tv_style_02"
                                        android:text="@string/detail_tv_expiration_date"/>

                                    <TextView
                                        android:id="@+id/tv_expiration_date"
                                        style="@style/commodity_tv_style"
                                        android:layout_toRightOf="@+id/tv_layout_07"/>

                                </RelativeLayout>

                                <!--效期gone-->
                                <RelativeLayout
                                    android:id="@+id/rl_validity_gone"
                                    android:layout_width="match_parent"
                                    android:layout_height="33dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_08_gone"
                                        style="@style/commodity_tv_style_02"
                                        android:text="@string/detail_tv_expiry_date"
                                        android:visibility="invisible"/>

                                    <TextView
                                        android:id="@+id/tv_validity_gone"
                                        style="@style/commodity_tv_style"
                                        android:layout_alignParentTop="true"
                                        android:layout_toRightOf="@+id/tv_layout_08_gone"/>

                                </RelativeLayout>
                                <!--毛利率-->
                                <RelativeLayout
                                    android:id="@+id/rl_grossMargin"
                                    android:layout_width="match_parent"
                                    android:layout_height="33dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_layout_09"
                                        style="@style/commodity_tv_style_02"
                                        android:text="@string/detail_tv_gross_margin"/>

                                    <TextView
                                        android:id="@+id/tv_grossMargin"
                                        style="@style/commodity_tv_style"
                                        android:layout_toRightOf="@+id/tv_layout_09"/>

                                </RelativeLayout>
                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/rl_recommend"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:minHeight="465dp"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_recommend"
                                style="@style/commodity_tv_style"
                                android:layout_height="35dp"
                                android:layout_marginTop="10dp"
                                android:background="@color/white"
                                android:gravity="center_vertical"
                                android:paddingLeft="10dp"
                                android:text="极力推荐"/>

                            <com.ybmmarket20.view.CommodityRecommendLayout
                                android:id="@+id/recommend_layout"
                                android:layout_width="match_parent"
                                android:layout_height="430dp"
                                android:background="@color/white"/>

                        </LinearLayout>

                        <!--第三方店铺-->
                        <LinearLayout
                            android:id="@+id/ll_company_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="67dp"
                                android:layout_marginTop="10dp"
                                android:background="@color/white">

                                <ImageView
                                    android:id="@+id/iv_image"
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_marginLeft="10dp"
                                    android:layout_marginTop="16dp"
                                    android:src="@drawable/transparent"/>

                                <TextView
                                    android:id="@+id/tv_company_name"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="8dp"
                                    android:layout_marginTop="16dp"
                                    android:layout_toRightOf="@+id/iv_image"
                                    android:text=""
                                    android:textColor="@color/text_292933"
                                    android:textSize="16sp"
                                    android:textStyle="bold"/>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/tv_company_name"
                                    android:layout_marginLeft="8dp"
                                    android:layout_toRightOf="@+id/iv_image"
                                    android:orientation="horizontal"
                                    android:visibility="invisible">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text=""
                                        android:textColor="@color/text_9494A6"
                                        android:textSize="12sp"/>

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="16dp"
                                        android:text=""
                                        android:textColor="@color/text_9494A6"
                                        android:textSize="12sp"/>

                                </LinearLayout>

                                <TextView
                                    android:id="@+id/tv_on_shop"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentRight="true"
                                    android:layout_marginRight="10dp"
                                    android:layout_marginTop="24dp"
                                    android:background="@drawable/bg_pop_merchants_tv"
                                    android:gravity="center"
                                    android:minHeight="24dp"
                                    android:minWidth="53dp"
                                    android:text="进店"
                                    android:textColor="@color/base_color"
                                    android:textSize="13sp"/>

                            </RelativeLayout>

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1px"
                                android:background="#eeeeee"/>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="58dp"
                                android:background="@color/white"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/tv_putaway"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:text="938种"
                                        android:textColor="@color/text_292933"
                                        android:textSize="16sp"/>

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:text="上架商品"
                                        android:textColor="@color/text_9494A6"
                                        android:textSize="12sp"/>

                                </LinearLayout>

                                <View
                                    android:layout_width="1px"
                                    android:layout_height="match_parent"
                                    android:background="#eeeeee"/>

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/tv_sale"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:text="93000件"
                                        android:textColor="@color/text_292933"
                                        android:textSize="16sp"/>

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:text="销量"
                                        android:textColor="@color/text_9494A6"
                                        android:textSize="12sp"/>

                                </LinearLayout>

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>
                </LinearLayout>

                <!--文字说明书-->
                <LinearLayout
                    android:id="@+id/two"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:background="@color/white"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="35dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:text="说明书"
                            android:textColor="@color/text_292933"
                            android:textSize="14sp"/>

                        <TextView
                            android:id="@+id/tv_explain"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableRight="@drawable/icon_right_green"
                            android:gravity="center_vertical|right"
                            android:text="查看全部"
                            android:textColor="@color/base_colors"/>

                    </LinearLayout>

                    <RelativeLayout
                        android:id="@+id/rl_product"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginLeft="10dp">

                        <TextView
                            android:id="@+id/tv_title_01"
                            style="@style/commodity_tv_style_02"
                            android:text="产品名"/>

                        <TextView
                            android:id="@+id/tv_product"
                            style="@style/commodity_tv_style"
                            android:layout_toRightOf="@+id/tv_title_01"/>

                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rl_specification"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginLeft="10dp">

                        <TextView
                            android:id="@+id/rb_bill_ordinary"
                            style="@style/commodity_tv_style_02"
                            android:text="产品名"/>

                        <TextView
                            android:id="@+id/tv_specification"
                            style="@style/commodity_tv_style"
                            android:layout_toRightOf="@+id/rb_bill_ordinary"/>

                    </RelativeLayout>
                </LinearLayout>

                <!--图文描述-->
                <LinearLayout
                    android:id="@+id/three"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!--产品说明书-->
                    <LinearLayout
                        android:id="@+id/ll_specification"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:background="@color/white"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:background="@color/white"
                            android:gravity="center_vertical"
                            android:paddingLeft="10dp"
                            android:text="图文描述"
                            android:textColor="#292933"
                            android:textSize="15sp"/>

                        <TextView
                            android:id="@+id/ll_specification_tv"
                            android:layout_width="match_parent"
                            android:layout_height="45dp"
                            android:background="@drawable/product_text_bg_2"
                            android:gravity="center"
                            android:text="点击图片可查看大图"
                            android:textSize="15sp"/>

                    </LinearLayout>

                    <com.ybmmarket20.view.ImageLayout
                        android:id="@+id/il_specification"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="10dp"
                        android:background="@color/white"/>

                    <!--关于药帮忙-->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="40dp"
                        android:background="@color/white"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/il_about"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:src="@drawable/icon_announcement"/>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>
        </com.ybmmarket20.view.DetailScrollView>

        <!--title_bar-->
        <include layout="@layout/commodity_tab_title"/>

        <!--操作栏-->
        <include layout="@layout/detail_operation_tool"/>
    </FrameLayout>

</LinearLayout>