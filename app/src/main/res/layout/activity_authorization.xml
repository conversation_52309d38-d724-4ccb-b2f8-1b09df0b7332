<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="23dp"
                android:text="请填写好友分享或销售提供的邀请码"
                android:textColor="#ff292933"
                android:textSize="16sp"
                android:textStyle="bold" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/authorization_et"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/shape_activate_bg"
                android:hint="请输入邀请码"
                android:paddingLeft="15dp"
                android:textSize="16sp"
                android:textColor="#292933" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/authorization_btn"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="42dp"
                android:background="@drawable/selector_common_btn"
                android:enabled="false"
                android:gravity="center"
                android:text="完成"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/authorization_tv_new"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:text="点击完成即表明您同意《服务条款》"
                android:textColor="@color/colors_9595A6" />

        </LinearLayout>

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/authorization_kefu"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="20dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:drawablePadding="10dp"
            android:gravity="center"
            android:paddingLeft="60dp"
            android:paddingRight="60dp"
            android:drawableLeft="@drawable/icon_phone_new"
            android:text="@string/kefuPhone"
            android:textColor="@color/home_back_selected"
            android:textSize="13sp"
            app:rv_cornerRadius="2dp"
            app:rv_strokeColor="@color/base_colors_new"
            app:rv_strokeWidth="0.5dp" />
    </RelativeLayout>

</LinearLayout>