<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.ybmmarket20.view.SquareImageView xmlns:ybm="http://schemas.android.com/apk/res-auto"
        android:id="@+id/iv_preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="5dp"
        android:scaleType="centerCrop"
        ybm:base_on="width" />

    <ImageView
        android:id="@+id/iv_delete"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_alignParentRight="true"
        android:src="@drawable/ic_delete_pic2"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_preview_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_centerInParent="true"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_add_image_cart" />

</RelativeLayout>