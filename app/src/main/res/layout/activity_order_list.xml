<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="match_parent" xmlns:tools="http://schemas.android.com/tools"
              android:orientation="vertical">


    <RelativeLayout
        android:id="@+id/ll_title"
        style="@style/header_white_layout">

        <ImageView
            android:id="@+id/iv_back"
            style="@style/header_layout_left"
            android:src="@drawable/nav_return" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/header_layout_mid"
            android:text=""
            android:textColor="#FF292933" />

        <TextView
            android:id="@+id/tv_right"
            style="@style/header_layout_right_text"
            android:text="我的账单"
            tools:visibility="visible"
            android:visibility="gone"/>

        <ImageView
            android:id="@+id/iv_right"
            style="@style/header_layout_right_img"
            android:src="@drawable/nav_scarch"
            android:visibility="gone" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_search_order"
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:background="@drawable/search_round_corner_gray_bg"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:layout_marginBottom="@dimen/dimen_dp_10"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/search_order_iv1"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:src="@drawable/icon_a_magnifying_glass" />


        <TextView
            android:id="@+id/search_title_et"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="5dp"
            android:layout_toRightOf="@id/search_order_iv1"
            android:background="@null"
            android:gravity="center_vertical"
            android:imeOptions="actionSearch"
            android:maxLines="1"
            android:singleLine="true"
            android:text="输入关键字搜索商品"
            android:textColor="#5c5c5c"
            android:textCursorDrawable="@drawable/color_cursor"
            android:textSize="@dimen/dimen_dp_13" />

    </RelativeLayout>


    <com.flyco.tablayout.SlidingTabLayout
        android:id="@+id/ps_tab"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white"
        android:orientation="horizontal"
        app:tl_indicator_color="@color/base_colors_new"
        app:tl_indicator_corner_radius="2dp"
        app:tl_indicator_height="4dp"
        app:tl_indicator_width_equal_title="true"
        app:tl_tab_space_equal="false"
        app:tl_textAllCaps="true"
        app:tl_textBold="BOTH"
        app:tl_textSelectColor="@color/text_292933"
        app:tl_textSelectSize="17sp"
        app:tl_textUnselectColor="@color/text_898999"
        app:tl_textsize="15sp" />

    <com.ybmmarket20.view.NoScrollViewPager
        android:id="@+id/vp"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>