<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/dimen_dp_15"
        android:paddingTop="@dimen/dimen_dp_10"
        android:paddingRight="@dimen/dimen_dp_15"
        android:paddingBottom="@dimen/dimen_dp_5">

        <TextView
            android:id="@+id/tv_pacakge_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="5dp"
            android:textColor="@color/colors_99664D"
            android:textSize="@dimen/dimen_dp_12"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="以下店铺加购商品不够起送门槛，不能提交订单" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</RelativeLayout>