<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_centerInParent="true">

        <com.ybmmarket20.common.widget.RoundedImageView
            android:id="@+id/iv_cover"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="5dp"
            android:scaleType="centerCrop"
            tools:src="@tools:sample/backgrounds/scenic" />

        <LinearLayout
            android:id="@+id/ll_del"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_marginEnd="2dp"
            android:layout_marginRight="2dp"
            android:gravity="end"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/iv_del"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:contentDescription="@null"
                android:scaleType="fitXY"
                android:src="@drawable/icon_correction_delete" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_saved_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:layout_marginBottom="5dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_12"
            android:visibility="gone"
            tools:text="已过期"
            tools:visibility="visible" />
    </RelativeLayout>
</RelativeLayout>