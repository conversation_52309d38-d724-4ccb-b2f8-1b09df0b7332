<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f8f8f8"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_10"
        android:text="@string/goods_correction_tips"
        android:textColor="#ff9494a6"
        android:textSize="@dimen/sp_14" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/priceTv"
            style="@style/style_goods_error_correcttion"
            android:text="@string/price" />

        <include layout="@layout/layout_divider" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/goodsTv"
            style="@style/style_goods_error_correcttion"
            android:text="@string/goods_info" />

        <include layout="@layout/layout_divider" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/othersTv"
            style="@style/style_goods_error_correcttion"
            android:text="@string/others" />
    </LinearLayout>

</LinearLayout>