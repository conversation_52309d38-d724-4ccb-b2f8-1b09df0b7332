<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_40"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/rb_refund_reason_title"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14"
        android:text="商品实物与展示不符"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/rb_refund_reason_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <RadioButton
        android:id="@+id/rb_refund_reason_parent"
        android:layout_width="@dimen/dimen_dp_16"
        android:layout_height="@dimen/dimen_dp_16"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/selector_refund_reason_radio_button"
        android:clickable="false"
        android:checked="false"
        android:button="@null"
        android:singleLine="true" />

</androidx.constraintlayout.widget.ConstraintLayout>