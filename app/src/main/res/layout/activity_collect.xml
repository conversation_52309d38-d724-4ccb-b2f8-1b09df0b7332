<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items_cart_collect" />

    <!--<RelativeLayout-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="0dp"-->
        <!--android:layout_weight="1">-->

        <com.flyco.tablayout.SlidingTabLayout
            android:id="@+id/ps_tab_new"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            app:tl_indicator_color="@color/base_colors_new"
            app:tl_indicator_corner_radius="2dp"
            app:tl_indicator_height="4dp"
            app:tl_indicator_width="17dp"
            app:tl_indicator_width_equal_title="false"
            app:tl_tab_padding="11dp"
            app:tl_tab_space_equal="true"
            app:tl_textAllCaps="true"
            app:tl_textBold="BOTH"
            app:tl_textSelectColor="@color/color_292933"
            app:tl_textSelectSize="17sp"
            app:tl_textUnselectColor="@color/text_676773"
            app:tl_textsize="15sp" />

        <!--<com.google.android.material.tabs.TabLayout-->
            <!--android:id="@+id/ps_tab"-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="36dp"-->
            <!--android:background="@drawable/base_header_default_bg"-->
            <!--android:orientation="horizontal"-->
            <!--app:tabGravity="fill"-->
            <!--app:tabIndicatorColor="@color/tv_tab_color"-->
            <!--app:tabIndicatorHeight="2dp"-->
            <!--app:tabMaxWidth="100dp"-->
            <!--app:tabMinWidth="50dp"-->
            <!--app:tabMode="fixed"-->
            <!--app:tabSelectedTextColor="@color/text_292933"-->
            <!--app:tabTextAppearance="@style/MyWishTabTextAppearance_collect"-->
            <!--app:tabTextColor="@color/text_676773"/>-->

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vp_client"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/ps_tab"
            android:background="@color/white" />

    <!--</RelativeLayout>-->

</LinearLayout>