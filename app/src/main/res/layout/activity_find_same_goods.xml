<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f7f7f8">

    <include
        android:id="@+id/header"
        layout="@layout/common_header_items_cart" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/sRefresh"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header"
        app:layout_constraintVertical_weight="1">

        <com.ybmmarket20.view.HomeSteadyHeader
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_70" />

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/abl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <include
                    layout="@layout/item_find_same_goods_invalidated"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_scrollFlags="scroll" />
            </com.google.android.material.appbar.AppBarLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"
                app:layout_scrollFlags="scroll|enterAlways">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dimen_dp_5"
                    android:layout_marginEnd="@dimen/dimen_dp_5"
                    app:spanCount="2"
                    tools:itemCount="20" />
            </LinearLayout>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <ImageView
        android:id="@+id/iv_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_find_same_goods_empty"
        android:layout_marginTop="@dimen/dimen_dp_166"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header" />

    <TextView
        android:id="@+id/tv_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="暂无相似商品～"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_empty" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="iv_empty, tv_empty" />

</androidx.constraintlayout.widget.ConstraintLayout>