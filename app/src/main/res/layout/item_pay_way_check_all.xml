<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_22"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tvCheckAll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="查看全部"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="#676773"
        android:drawableEnd="@drawable/icon_coupon_arrow_down"
        android:drawablePadding="@dimen/dimen_dp_3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_32" />
</androidx.constraintlayout.widget.ConstraintLayout>