<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvOftenBuy"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_5"
        app:layoutManager="androidx.recyclerview.widget.StaggeredGridLayoutManager"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintVertical_weight="1"
        tools:itemCount="20"
        tools:listitem="@layout/item_often_buy" />

</androidx.constraintlayout.widget.ConstraintLayout>