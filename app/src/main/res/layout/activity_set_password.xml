<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/new_password_rl03"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginTop="10dp"
            android:paddingLeft="@dimen/password_mg"
            android:paddingRight="@dimen/password_mg">

            <ImageView
                android:id="@+id/new_password_iv03"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:src="@drawable/login10" />

            <TextView
                android:id="@+id/textView3"
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@+id/new_password_iv03"
                android:text="当前密码:"
                android:textColor="@color/login_tv"
                android:textSize="@dimen/password_tv" />


            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignTop="@id/textView3"
                android:layout_alignBottom="@id/textView3"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@id/textView3"
                app:hintEnabled="false"
                app:passwordToggleDrawable="@drawable/password_taggle_select"
                app:passwordToggleEnabled="true">

                <EditText
                    android:id="@+id/new_password_et03"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_toRightOf="@id/textView3"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="请输入当前密码"
                    android:inputType="textPassword"
                    android:longClickable="false"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/login_et"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="@dimen/password_et_szie" />
            </com.google.android.material.textfield.TextInputLayout>

        </RelativeLayout>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/password_mg"
            android:layout_marginRight="@dimen/password_mg"
            android:src="@drawable/login03" />

        <RelativeLayout
            android:id="@+id/new_password_rl01"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:paddingLeft="@dimen/password_mg"
            android:paddingRight="@dimen/password_mg">

            <ImageView
                android:id="@+id/new_password_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:src="@drawable/login02" />

            <TextView
                android:id="@+id/textView1"
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@+id/new_password_iv"
                android:text="设置密码:"
                android:textColor="@color/login_tv"
                android:textSize="@dimen/password_tv" />

            <!--<EditText
                android:id="@+id/new_password_et01"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@id/textView1"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="请输入您的新密码"
                android:inputType="textPassword"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/login_et"
                android:textCursorDrawable="@drawable/color_cursor"
                android:textSize="@dimen/password_et_szie"/>-->
            <!--android:textColor="#5c5c5c"-->

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignTop="@id/textView1"
                android:layout_alignBottom="@id/textView1"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@id/textView1"
                app:hintEnabled="false"
                app:passwordToggleDrawable="@drawable/password_taggle_select"
                app:passwordToggleEnabled="true">

                <EditText
                    android:id="@+id/new_password_et01"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_toRightOf="@id/textView3"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="请输入您的新密码"
                    android:inputType="textPassword"
                    android:longClickable="false"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/login_et"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="@dimen/password_et_szie" />
            </com.google.android.material.textfield.TextInputLayout>

        </RelativeLayout>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/password_mg"
            android:layout_marginRight="@dimen/password_mg"
            android:src="@drawable/login03" />

        <RelativeLayout
            android:id="@+id/new_password_rl02"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:paddingLeft="@dimen/password_mg"
            android:paddingRight="@dimen/password_mg">

            <ImageView
                android:id="@+id/new_password_iv02"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:src="@drawable/login02" />

            <TextView
                android:id="@+id/textView2"
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@+id/new_password_iv02"
                android:text="确认密码:"
                android:textColor="@color/login_tv"
                android:textSize="@dimen/password_tv" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignTop="@id/textView2"
                android:layout_alignBottom="@id/textView2"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@id/textView2"
                app:hintEnabled="false"
                app:passwordToggleDrawable="@drawable/password_taggle_select"
                app:passwordToggleEnabled="true">

                <EditText
                    android:id="@+id/new_password_et02"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_toRightOf="@id/textView3"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="请再次输入您的密码"
                    android:inputType="textPassword"
                    android:longClickable="false"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/login_et"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="@dimen/password_et_szie" />
            </com.google.android.material.textfield.TextInputLayout>
            <!--        <EditText
                        android:id="@+id/new_password_et02"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:layout_toRightOf="@id/textView2"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="请再次输入您的密码"
                        android:inputType="textPassword"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/login_et"
                        android:textCursorDrawable="@drawable/color_cursor"
                        android:textSize="@dimen/password_et_szie"/>-->
        </RelativeLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="@dimen/password_mg"
            android:paddingRight="@dimen/password_mg"
            android:text="@string/validate_pwd_rule"
            android:textColor="#ff0000"
            android:textSize="10dp" />

        <Button
            android:id="@+id/new_password_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/password_mg"
            android:layout_marginTop="15dp"
            android:layout_marginRight="@dimen/password_mg"
            android:background="@drawable/round_corner_gay_bg"
            android:gravity="center"
            android:text="完成"
            android:textColor="@color/login_btn_tv"
            android:textSize="18sp" />
    </LinearLayout>
</LinearLayout>