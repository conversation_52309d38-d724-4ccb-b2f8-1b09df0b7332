<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <include
        android:id="@+id/header"
        layout="@layout/common_header_items" />

    <View
        android:id="@+id/header_space1"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_14"
        android:background="@color/color_FAFAFA"
        app:layout_constraintTop_toBottomOf="@+id/header" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_price"
        android:layout_width="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header_space1"
        android:layout_height="55dp">

        <TextView
            android:id="@+id/tv_red_envelope_amount_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="可用余额"
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/dimen_dp_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="20dp"
            app:layout_constraintBottom_toBottomOf="parent"/>


        <TextView
            android:id="@+id/tv_price_unit"
            android:layout_width="wrap_content"
            android:text="¥"
            android:textSize="21dp"
            android:textStyle="bold"
            android:textColor="@color/text_color_333333"
            app:layout_constraintEnd_toStartOf="@id/tv_virtual_money_amount"
            app:layout_constraintTop_toTopOf="@id/tv_virtual_money_amount"
            app:layout_constraintBottom_toBottomOf="@id/tv_virtual_money_amount"
            android:layout_marginEnd="3dp"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_virtual_money_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0.0"
            android:textColor="@color/text_color_333333"
            android:textSize="36dp"
            android:textStyle="bold"
            android:layout_marginEnd="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="199.99" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_FAFAFA"
        android:layout_marginHorizontal="20dp"
        app:layout_constraintTop_toBottomOf="@+id/cl_price" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_top_up"
        android:layout_width="0dp"
        android:layout_height="55dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line">

        <TextView
            android:id="@+id/tv_top_up_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:text="在线充值"
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/dimen_dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_top_up"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:paddingHorizontal="4dp"
            android:paddingVertical="3dp"
            android:text="最高返200元红包"
            android:textColor="@color/color_FE0F23"
            android:textSize="13dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_arrow1"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_backgroundColor="@color/color_FFECE9"
            app:rv_cornerRadius="2dp" />

        <ImageView
            android:id="@+id/iv_arrow1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:src="@drawable/icon_pingan_arrow_right"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line1"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="20dp"
        android:background="@color/color_FAFAFA"
        app:layout_constraintTop_toBottomOf="@+id/cl_top_up"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_pingan_user"
        android:layout_width="0dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line1"
        android:layout_height="55dp">

        <TextView
            android:id="@+id/tv_pingan_user"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="转到平安账户"
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/dimen_dp_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="20dp"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/iv_arrow"
            android:src="@drawable/icon_pingan_arrow_right"
            android:layout_width="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="20dp"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>


    <View
        android:id="@+id/space2"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_19"
        android:background="@color/color_FAFAFA"
        app:layout_constraintTop_toBottomOf="@+id/cl_pingan_user" />

    <com.flyco.tablayout.SlidingTabLayout
        android:id="@+id/stl_virtual_money"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_50"
        android:orientation="horizontal"
        app:tl_indicator_color="@color/color_00b377"
        app:layout_constraintTop_toBottomOf="@+id/space2"
        app:tl_indicator_corner_radius="1dp"
        app:tl_indicator_height="2dp"
        app:tl_indicator_width_equal_title="true"
        app:tl_tab_space_equal="true"
        app:tl_textAllCaps="true"
        app:tl_textBold="BOTH"
        app:tl_textSelectColor="@color/text_292933"
        app:tl_textSelectSize="17sp"
        app:tl_textUnselectColor="@color/text_999A9B"
        app:tl_textsize="15sp" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/vp_virtual_money"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        app:layout_constraintTop_toBottomOf="@+id/stl_virtual_money"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintVertical_weight="1" />


</androidx.constraintlayout.widget.ConstraintLayout>