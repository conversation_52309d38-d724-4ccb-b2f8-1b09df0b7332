<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!--企业信息-->
        <LinearLayout
            android:id="@+id/ll_basic_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="15dp"
                android:text="@string/str_account_basic_info_company_info_hint"
                android:textColor="@color/text_292933"
                android:textSize="15sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:text="@string/str_account_basic_info_company_name_hint"
                    android:textColor="@color/text_676773"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_company_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:textColor="@color/text_292933"
                    android:textSize="14sp"
                    tool:text="武汉健康大药房（金融港店）" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:text="@string/str_account_basic_info_company_type_hint"
                    android:textColor="@color/text_676773"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_company_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:textColor="@color/text_292933"
                    android:textSize="14sp"
                    tool:text="单体药店" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:text="@string/str_account_basic_info_company_address_hint"
                    android:textColor="@color/text_676773"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_company_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:textColor="@color/text_292933"
                    android:textSize="14sp"
                    tool:text="北京市朝阳区望京西街1029号字符长啊长啊长长长" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="15dp"
                android:background="@color/color_F5F5F5" />
            <!--    经营范围-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="15dp"
                android:text="@string/str_account_basic_info_business_scope_hint"
                android:textColor="@color/text_292933"
                android:textSize="15sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:text="@string/str_account_basic_info_business_scope_hint"
                    android:textColor="@color/text_676773"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_business_scope"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:textColor="@color/text_292933"
                    android:textSize="14sp"
                    tool:text="生化药品、化学药制剂、中药饮品、中成药、中成药、中成药、中成药、中成药、中成药、中成药" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="15dp"
                android:background="@color/color_F5F5F5" />
            <!--收货信息-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="15dp"
                    android:text="@string/str_account_basic_info_receiver_info_hint"
                    android:textColor="@color/text_292933"
                    android:textSize="15sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:text="@string/str_account_basic_info_receiver_name_hint"
                        android:textColor="@color/text_676773"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_receiver_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:textColor="@color/text_292933"
                        android:textSize="14sp"
                        tool:text="张大胖" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:text="@string/str_account_basic_info_receiver_phone_hint"
                        android:textColor="@color/text_676773"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_receiver_phone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:textColor="@color/text_292933"
                        android:textSize="14sp"
                        tool:text="************" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:text="@string/str_account_basic_info_receiver_address_hint"
                        android:textColor="@color/text_676773"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_receive_address"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:textColor="@color/text_292933"
                        android:textSize="14sp"
                        tool:text="北京市朝阳区望京西街1029号字符长啊长啊长长长" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="15dp"
                    android:background="@color/color_F5F5F5" />
            </LinearLayout>
        </LinearLayout>

        <!--发票信息-->
        <LinearLayout
            android:id="@+id/ll_invoice_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="15dp"
                android:text="@string/str_account_basic_info_invoice_info_hint"
                android:textColor="@color/text_292933"
                android:textSize="15sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:text="@string/str_account_basic_info_company_name_hint"
                    android:textColor="@color/text_676773"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_invoice_company_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:textColor="@color/text_292933"
                    android:textSize="14sp"
                    tool:text="武汉健康大药房（金融港店）" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:text="@string/str_account_basic_info_invoice_type_hint"
                    android:textColor="@color/text_676773"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_invoice_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:textColor="@color/text_292933"
                    android:textSize="14sp"
                    tool:text="电子普通发票" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="15dp"
                android:background="@color/color_F5F5F5" />
        </LinearLayout>

    </LinearLayout>
</ScrollView>
