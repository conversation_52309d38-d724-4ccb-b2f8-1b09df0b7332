<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="#FAFBFC"
    android:layout_width="match_parent"
    android:layout_margin="5dp"
    android:layout_height="80dp">

    <ImageView android:layout_width="70dp"
        android:layout_height="match_parent"
        android:layout_marginRight="3dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:id="@+id/sku_image"
        android:layout_alignParentLeft="true"
        android:background="#fff"
        android:layout_alignParentTop="true"
        />
    <TextView android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="12131231231231231231"
        android:layout_toRightOf="@id/sku_image"
        android:layout_marginTop="15dp"
        android:textColor="#000"
        android:textSize="14sp"
        android:id="@+id/sku_name"
        />

    <TextView android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/sku_effect"
        android:layout_alignLeft="@id/sku_name"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="15dp"
        android:textColor="#676773"
        android:textSize="12sp"
        android:text="1111111"
        />
</RelativeLayout>