<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/title"
        layout="@layout/common_header_items" />

    <com.ybmmarket20.view.X5WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        android:layout_marginBottom="@dimen/dimen_dp_10"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toTopOf="@+id/rtvConfirm"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintVertical_weight="1" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/rtvConfirm"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_44"
        android:text="已阅读并同意，继续申请售后"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_16"
        android:layout_marginBottom="@dimen/dimen_dp_10"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:rv_backgroundColor="@color/color_00b377"
        app:rv_cornerRadius="@dimen/dimen_dp_2" />
</androidx.constraintlayout.widget.ConstraintLayout>