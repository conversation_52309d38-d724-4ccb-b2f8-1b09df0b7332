<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:background="#FFFAFAFA"
    android:paddingRight="10dp"
    android:paddingLeft="10dp">

    <CheckBox
        android:id="@+id/cb_choice"
        style="@style/CustomCheckboxTheme"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:clickable="false"
        android:layout_gravity="center"
        android:layout_marginLeft="5dp" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:text=""
        android:textColor="@color/text_292933"
        android:textSize="16sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=""
        android:layout_marginRight="3dp"
        android:textColor="@color/text_9494A6"
        android:textSize="14sp" />

</LinearLayout>