package com.zxing.activity;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.Editable;
import android.text.Html;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.analysys.ANSAutoPageTracker;
import com.github.mzule.activityrouter.annotation.Router;
import com.google.gson.Gson;
import com.google.zxing.Result;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.PermissionDialogUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.LoginActivity;
import com.ybmmarket20.activity.NongWebviewActivity;
import com.ybmmarket20.activity.ReplenishProductActivity;
import com.ybmmarket20.activity.SearchVoiceActivity;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CaptureResultBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.PlanCaptureProductBean;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.view.BaseShowBottomSheetDialog;
import com.ybmmarket20.view.ShowListSheetDialog;
import com.zxing.decoding.InactivityTimer;
import com.zxing.view.ScannerView;

import butterknife.Bind;

import static com.ybmmarket20.constant.RouterConstantKt.CURRENT_PAGE;
import static com.ybmmarket20.constant.RouterConstantKt.CURRENT_PAGE_RESULT_QR;
import static com.ybmmarket20.constant.RouterConstantKt.OPENNEXTPAGEURLKEY;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Router({"captureactivity", "captureactivity_product/:capture_product", "captureactivity_plan/:capture_plan", "captureactivity_refund/:capture_refund"})
public class CaptureActivity extends BaseActivity implements ScannerView.ScanListener, ScannerView.ClickListener, ANSAutoPageTracker {
    private static int REQUEST_REPLENISHMENT_CODE = 10; //跳转请求码-补货登记
    private static final int ACTION_NUMBER = 10;
    private static long TIME_CODE = 200;
    public static String INTENT_FROM_SEARCH = "from_search";

    @Bind(R.id.scanner_view)
    ScannerView scannerView;
    @Bind(R.id.ll_title)
    RelativeLayout llTitle;
    @Bind(R.id.tv_add_num)
    TextView tvAddNum;
    private TextView mTvAddCapture;
    private ShowListSheetDialog mListSheetDialog;

    private InactivityTimer inactivityTimer;
    private String mCode;
    private String mCapturePlanCode;
    private String mCapturePlanName;
    private int addNumber = 0;
    private String capture_refund;

    private String openNextPageUrl;
    private String currentPage;

    private Boolean isFromSearch = false;
    private String jgspid;

    @Override
    protected void initData() {

        openNextPageUrl = getIntent().getStringExtra(OPENNEXTPAGEURLKEY);
        currentPage = getIntent().getStringExtra(CURRENT_PAGE);
        isFromSearch = Objects.equals(getIntent().getStringExtra(INTENT_FROM_SEARCH), "1");
        jgspid = getIntent().getStringExtra(IntentCanst.JG_JGSPID);

        if (!isLogin()) {
            //未登录
            gotoAtivity(LoginActivity.class, null);
            return;
        }
        setTitle("条码扫描");
        initPage();
        scannerView.setScanListener(this);
        scannerView.setClickListener(this);
        llTitle.setBackgroundColor(0x4c000000);
        inactivityTimer = new InactivityTimer(this);
    }

    @Override
    protected void initPermission() {
        super.initPermission();
        RxPermissions rxPermissions = new RxPermissions(this);
        if (rxPermissions.isGranted(android.Manifest.permission.CAMERA)) {
            requestCameraPermission();
        } else {
            PermissionDialogUtil.showPermissionInfoDialog(this,
                    "药帮忙App需要申请相机权限，用于扫描二维码",
                    () -> requestCameraPermission());
        }

    }

    private void requestCameraPermission() {
        requestEachPermissions(new PermissionCallBack("扫码功能需要申请相机权限") {
            @Override
            public void granted(Permission permission) {
//                ToastUtils.showLong("granted");
            }
        }, Manifest.permission.CAMERA);
    }

    @Override
    protected void onResume() {
        super.onResume();
        scannerView.resume();
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_scan_code;
    }

    @Override
    protected void onPause() {
        super.onPause();
        scannerView.pause();
    }

    @Override
    protected void onDestroy() {
        inactivityTimer.shutdown();
        super.onDestroy();
    }

    @Override
    public void decodeSuccess(Result result, Bitmap bitmap) {
        inactivityTimer.onActivity();
        mCode = result.getText();
        if (TextUtils.isEmpty(mCode)) {
            ToastUtils.showShort("扫描失败！");
            finish();
        } else {
            if (!TextUtils.isEmpty(mCapturePlanCode) && !TextUtils.isEmpty(mCapturePlanName)) {
                // 电子计划单跳转过来
                scannerView.pause();
                deCodeCommand(mCode);
            } else if (!TextUtils.isEmpty(capture_refund)) {
                //退款页面跳转过来的
                Intent intent = new Intent();
                Bundle bundle = new Bundle();
                bundle.putSerializable("mCode", mCode);
                intent.putExtras(bundle);
                setResult(Activity.RESULT_OK, intent);
                finish();
            } else if (!TextUtils.isEmpty(openNextPageUrl)) {
                String action = openNextPageUrl + mCode;
                RoutersUtils.open(action);
                finish();
            } else if(!TextUtils.isEmpty(currentPage)) {
                Intent intent = new Intent();
                Bundle bundle = new Bundle();
                bundle.putSerializable("mCode", mCode);
                intent.putExtras(bundle);
                setResult(CURRENT_PAGE_RESULT_QR, intent);
                finish();
            } else {
//                String action = "ybmpage://productdetail_no/" + mCode;
                //新加一种情况，如果是json字符串，跳转webview页面
                CaptureResultBean bean=null;
                try {
                   bean = new Gson().fromJson(mCode, CaptureResultBean.class);
                }catch (Exception e){
                    e.printStackTrace();
                }
                if (bean != null && !bean.getPaymentKey().isEmpty()) {
                    Intent intent = new Intent(CaptureActivity.this, NongWebviewActivity.class);
                    intent.putExtra("url", bean.getPaymentKey());
                    startActivity(intent);
                    return;
                }
                String action = "ybmpage://searchproductop?jgEntrance=首页(扫一扫)&scanCode=" + mCode;
                if (isFromSearch){
                    action = action+"&"+IntentCanst.JG_JGSPID+"="+ JGTrackTopLevelKt.replacePartJgspid(jgspid,"_s","_s107");
                }else {
                    action = action+"&"+IntentCanst.JG_JGSPID+"="+ JGTrackTopLevelKt.replacePartJgspid(jgspid,"_s","_s104");
                }
                RoutersUtils.open(action);
                finish();
            }
        }
    }

    @Override
    public void returnIntent(Intent intent) {
        // 这个地方老代码遗留不知需求用意，故保留
        if (intent == null) {
            return;
        }
        setResult(RESULT_OK, intent);
        finish();
    }

    @Override
    public void returnMessage(String message) {
        // TODO: 2017/6/26 不知这个地方是什么意思，故保留
        if (TextUtils.isEmpty(message)) {
            return;
        }
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(message));
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET);
        startActivity(intent);
    }

    private void initPage() {
        capture_refund = getIntent().getStringExtra(IntentCanst.CAPTURE_REFUND);

        //初始化页面的数据（从计划单跳转过来的参数）
        String planParam = getIntent().getStringExtra(IntentCanst.CAPTURE_PLAN);
        tvAddNum.setVisibility(View.GONE);
        if (!TextUtils.isEmpty(planParam)) {
            scannerView.switchModeEnable(true);
            tvAddNum.setVisibility(View.VISIBLE);
            String[] mCapturePlanIndex = planParam.split("-");
            if (mCapturePlanIndex.length == 2) {
                mCapturePlanCode = mCapturePlanIndex[0];
                mCapturePlanName = mCapturePlanIndex[1];
            }
        }
        tvAddNum.setText(Html.fromHtml(String.format(getResources()
                .getString(R.string.text_replenishment_num), "" + addNumber)));
    }

    //处理返回的商品code
    private void deCodeCommand(final String code) {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.PLAN_SEARCHPRODUCT)
                .addParam("code", code).addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
        HttpManager.getInstance().post(params, new BaseResponse<PlanCaptureProductBean>() {
            @Override
            public void onSuccess(String content, final BaseBean<PlanCaptureProductBean> bean, PlanCaptureProductBean data) {
                if (bean.isSuccess()) {
                    SmartExecutorManager.getInstance().executeUI(new Runnable() {
                        @Override
                        public void run() {
                            showDialog(bean.data);
                        }
                    }, TIME_CODE);
                } else {
                    SmartExecutorManager.getInstance().executeUI(new Runnable() {
                        @Override
                        public void run() {
                            gotoAction();
                        }
                    }, TIME_CODE);
                }
            }

            @Override
            public void onFailure(NetError error) {
                if (scannerView != null) {
                    scannerView.resume();
                }
                if (!TextUtils.isEmpty(error.message)) {
                    ToastUtils.showShort(error.message);
                }
            }
        });
    }

    //跳转补货登记
    public void gotoAction() {
        if (TextUtils.isEmpty(mCapturePlanCode)) {
            ToastUtils.showShort("请重新扫描");
            return;
        }
        Intent intent = new Intent(this, ReplenishProductActivity.class);
        intent.putExtra("plan_id", mCapturePlanCode);
        intent.putExtra("plan_name", mCapturePlanName);
        startActivityForResult(intent, REQUEST_REPLENISHMENT_CODE);
    }

    //弹出商品信息dialog
    public void showDialog(final PlanCaptureProductBean bean) {
        if (bean == null) {
            return;
        }
        final Dialog alert = new Dialog(BaseYBMApp.getApp().getCurrActivity(), com.ybm.app.R.style.Dialog);
        alert.setCanceledOnTouchOutside(false);
        alert.setCancelable(false);
        LayoutInflater inflater = LayoutInflater.from(BaseYBMApp.getApp().getCurrActivity());
        View v = inflater.inflate(R.layout.command_dialog2, null);
        TextView btnOK = (TextView) v.findViewById(R.id.btn_ok);
        TextView tvTitle = (TextView) v.findViewById(R.id.tv_title);
        TextView tvContent = (TextView) v.findViewById(R.id.tv_content);
        TextView tvContent2 = (TextView) v.findViewById(R.id.tv_content2);
        TextView tvChange = (TextView) v.findViewById(R.id.tv_change);
        final EditText tvNum = (EditText) v.findViewById(R.id.tv_num);
        final EditText tvPrice = (EditText) v.findViewById(R.id.tv_price);
        mTvAddCapture = (TextView) v.findViewById(R.id.tv_add_capture);
        ImageView ivOff = (ImageView) v.findViewById(R.id.iv_off);

        String purchaseNumberDefault = "请输入补货数量";
        String capturePlanName = "添加至" + mCapturePlanName;
        tvTitle.setText(bean.productName);
        tvContent.setText(bean.spec);
        tvContent2.setText(bean.manufacturer);
        tvNum.setHint(purchaseNumberDefault);
        mTvAddCapture.setText(capturePlanName);
        btnOK.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String num;
                if (TextUtils.isEmpty(tvNum.getText().toString().trim())) {
                    num = bean.purchaseNumberDefault;
                } else {
                    num = tvNum.getText().toString().trim();
                }
                if (TextUtils.isEmpty(num) || Integer.parseInt(num) < 1) {
                    num = "1";
                }
                String price = tvPrice.getText().toString();
                if (price == null) {
                    price = "";
                }
                addPlanAction(bean.productName, mCode, mCapturePlanCode, num, price);
                alert.dismiss();
            }

        });
        ivOff.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                alert.dismiss();
                if (scannerView != null) {
                    scannerView.resume();
                }
            }
        });
        tvChange.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                initPopPurchaseList();
                mListSheetDialog.show();
            }

        });
        tvPrice.addTextChangedListener(new TextWatcher() {
            public void afterTextChanged(Editable edt) {
                String temp = edt.toString();
                int posDot = temp.indexOf(".");
                if (posDot <= 0) return;
                if (temp.length() - posDot - 1 > 2) {
                    edt.delete(posDot + 3, posDot + 4);
                }
            }

            public void beforeTextChanged(CharSequence arg0, int arg1, int arg2, int arg3) {
            }

            public void onTextChanged(CharSequence arg0, int arg1, int arg2, int arg3) {
            }
        });
        alert.setOnShowListener(new DialogInterface.OnShowListener() {
            public void onShow(DialogInterface dialog) {
                InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.showSoftInput(tvNum, InputMethodManager.SHOW_IMPLICIT);
            }
        });
        alert.show();
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams((int) (com.ybm.app.utils.UiUtils.getScreenWidth() * 0.8), RelativeLayout.LayoutParams.WRAP_CONTENT);
        alert.setContentView(v, params);
    }

    //初始化更改所添加计划单列表
    private void initPopPurchaseList() {
        if (mListSheetDialog == null) {
            mListSheetDialog = new ShowListSheetDialog(CaptureActivity.this);
            mListSheetDialog.setOnSelectListener(new BaseShowBottomSheetDialog.OnSelectListener() {
                @Override
                public void getValue(SearchFilterBean show) {
                    mCapturePlanCode = show.id;
                    mCapturePlanName = show.realName;

                    if (mTvAddCapture != null) {
                        String capturePlanName = "添加至" + show.realName;
                        mTvAddCapture.setText(capturePlanName);
                    }
                }

                @Override
                public void OnDismiss() {

                }
            });
        }
    }

    //添加到计划单
    private void addPlanAction(String productName, String code, String planningScheduleId, final String purchaseNumber, String price) {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.ADD_PRODUCTTOPLAN)
                .addParam("code", code)
                .addParam("planningScheduleId", planningScheduleId)
                .addParam("purchaseNumber", purchaseNumber)
                .addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
        if (!TextUtils.isEmpty(price)) {
            params.put("price", price);
        }
        if (!TextUtils.isEmpty(productName)) {
            params.put("productName", productName);
        }
        HttpManager.getInstance().post(params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, final BaseBean<EmptyBean> bean, EmptyBean data) {
                if (scannerView != null && !scannerView.isInput()) {
                    scannerView.resume();
                }
                if (bean.isSuccess()) {
                    ToastUtils.showShort("添加成功");
                    mHandler.sendMessage(mHandler.obtainMessage(ACTION_NUMBER, purchaseNumber));
                }
            }

            @Override
            public void onFailure(NetError error) {
                if (scannerView != null && !scannerView.isInput()) {
                    scannerView.resume();
                }
                if (!TextUtils.isEmpty(error.message)) {
                    ToastUtils.showShort(error.message);
                }
            }
        });
    }

    //处理请求中但会的数据
    private Handler mHandler = new Handler(Looper.getMainLooper()) {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            //已添加数量
            if (msg.what == ACTION_NUMBER) {
                String purchaseNumber = (String) msg.obj;
                if (tvAddNum == null) {
                    return;
                }
                int parseInt = Integer.parseInt(purchaseNumber);
                if (parseInt > 0) {
                    addNumber++;
                }
                tvAddNum.setText(Html.fromHtml(String.format(getResources()
                        .getString(R.string.text_replenishment_num), "" + addNumber)));
            }
        }
    };

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_REPLENISHMENT_CODE && resultCode == RESULT_OK) {
            String purchaseNumber = data.getStringExtra(IntentCanst.REPLENISHMENTPROGRAM_NUMBER);
            mHandler.sendMessage(mHandler.obtainMessage(ACTION_NUMBER, purchaseNumber));
        }
    }

    @Override
    public void onClick(int tag, String content) {
        switch (tag) {
            case ScannerView.CLICK_TAG_CONFIRM:
                if (!TextUtils.isEmpty(content)) {
                    mCode = content;
                    hideSoftInput();
                    deCodeCommand(content);
                } else {
                    ToastUtils.showShort("请输入条形码");
                }
                break;
        }
    }

    @Override
    public Map<String, Object> registerPageProperties() {
        HashMap<String, Object> map = new HashMap<>();
        map.put(JGTrackManager.FIELD.FIELD_PAGE_ID, JGTrackManager.TrackCapture.PAGE_ID);
        map.put(JGTrackManager.FIELD.FIELD_TITLE, JGTrackManager.TrackCapture.TITLE);
        return map;
    }

    @Override
    public String registerPageUrl() {
        return AppUtilKt.getFullClassName(CaptureActivity.this);
    }
}
