package com.ybmmarket20.viewmodel

import android.app.Application
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.util.Base64
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.ybmmarket20.bean.*
import com.ybmmarket20.bean.payment.PaymentPayTypeBean
import com.ybmmarket20.network.request.JDPayRequest
import com.ybmmarket20.network.request.PayResultRequest
import com.ybmmarket20.network.request.PayWayV2Request
import com.ybmmarket20.network.request.QueryDeviceStatusOnPayRequest
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.YBMPayUtil
import com.ybmmarket20.view.PaymentBottomPayItemShowView
import kotlinx.coroutines.launch

/**
 * 收银台
 */
//支付宝或花呗
const val PAY_TYPE_ALIPY = "alipay"

//银联
const val PAY_TYPE_UNIONPAY = "unionpay"

//微信
const val PAY_TYPE_WEIXIN = "weixin"

//平安贷
const val PAY_TYPE_PINGAN = "pingancredit"

//京东银行卡
const val PAY_TYPE_JDCARD = "jdCardPay"

//小雨点
const val PAY_TYPE_XYD = "xydLoan"

//花呗
const val PAY_TYPE_ALIPY_HUABEI = "pcredit"

//银行卡
const val PAY_TYPE_JDCARD_ITEM = PAY_TYPE_JDCARD

//添加银行卡payId
const val VIRTUAL_PAY_ID_ADD_BANK_CARD = "virtualAdd"

//推荐银行卡payId
const val VIRTUAL_PAY_ID_RECOMMEND_BANK_CARD = "virtualRecommend"

//农行链e贷
const val PAY_TYPE_NONG = "abchinaLoan"
//金蝶
const val PAY_TYPE_JINDIE = "kingDee"

//银行卡组
const val PAY_LAYOUT_TYPE_BANK_GROUP = 1

//银行卡
const val PAY_LAYOUT_TYPE_BANK_CARD = 2

//查看全部银行卡
const val PAY_LAYOUT_TYPE_BANK_CARD_CHECK_ALL = 3

//添加银行卡
const val PAY_LAYOUT_TYPE_BANK_CARD_ADD = 4

//支付方式
const val PAY_LAYOUT_TYPE_BANK_ITEM = 5

//平安贷
const val PAY_LAYOUT_TYPE_BANK_ITEM_PINGAN = 6

//展开更多
const val PAY_LAYOUT_TYPE_EXPAND_MORE = 7

//推荐银行卡
const val PAY_LAYOUT_TYPE_BANK_CARD_RECOMMEND = 8

//银行卡Tip
const val PAY_LAYOUT_TYPE_BANK_CARD_TIPS = 9

//银行卡列表头部
const val PAY_LAYOUT_TYPE_BANK_CARD_HEAD = 10

//农行链e贷
const val PAY_LAYOUT_TYPE_NONG = 12
//小雨点
const val PAY_LAYOUT_TYPE_XYD = 13
//金蝶
const val PAY_LAYOUT_TYPE_JINDIE = 14

/**
 * 收银台
 */
class PayWayV2ViewModel(app: Application) : BaseViewModel(app) {

    private var mPayConfigBean: PayConfigBean? = null

    //选中的payId（银行卡使用cardId）
    private var mSelectedPayId = ""

    //选中的payCode
    var mSelectedPayCode = ""

    //银行卡列表是否展开
    private var mIsExpandedBankCard = false

    //支付方式列表是否展开
    private var mIsExpandedPayType = false

    //验证密码后生成的token用于支付
    private var checkPasswordForPayToken = ""

    //当前是否是绑卡状态
    @JvmField
    var isBindCardStatus = false

    var pingAnAvailableAmount: String? = null

    var mPinganPrice: String? = null

    //推荐的银行卡
    var recommendBankCard: PayTypeBankCard? = null

    var mRecommendBankCardOrigin: BankCardItemType? = null

    var mAddBankCardDialogTips: String? = null

    var mMtkTip: String? = null
    var tranNo: String? = null

    //列表数据
    private val _paymentListBeanLiveData = MutableLiveData<PayTypeConfigV2Bean>()
    val paymentListBeanLiveData: LiveData<PayTypeConfigV2Bean> = _paymentListBeanLiveData

    //选中
    private val _selectPayTypeItemLiveData = MutableLiveData<PayTypeSelectedState>()
    val selectPayTypeItemLiveData: LiveData<PayTypeSelectedState> = _selectPayTypeItemLiveData

    //是否设置支付密码
    private val _jDPWSettingLiveData = MutableLiveData<BaseBean<JDPWBean>>()
    val jDPWSettingLiveData: LiveData<BaseBean<JDPWBean>> = _jDPWSettingLiveData

    //是否设置支付密码
    private val _jDPayPWSettingLiveData = MutableLiveData<BaseBean<JDPWBean>>()
    val jDPayPWSettingLiveData: LiveData<BaseBean<JDPWBean>> = _jDPayPWSettingLiveData

    //判断是否需要身份认证弹窗
    private val _checkPayAuthenticationLiveData = MutableLiveData<BaseBean<ShowPopBean>>()
    val  checkPayAuthenticationLiveData = _checkPayAuthenticationLiveData

    //支付参数
    private val _getPayParamsLiveData = MutableLiveData<YBMPayEntity>()
    val getPayParamsLiveData: LiveData<YBMPayEntity> = _getPayParamsLiveData

    //查询结果参数
    private val _getPayResultParamsLiveData = MutableLiveData<YBMPayParam>()
    val getPayResultParamsLiveData: LiveData<YBMPayParam> = _getPayResultParamsLiveData

    //获取支付弹框
    private val _payDialogLiveData: MutableLiveData<BaseBean<PayDialogBean>> = MutableLiveData()
    val payDialogLiveData: LiveData<BaseBean<PayDialogBean>> = _payDialogLiveData

    //获取支付类型
    private val _getPayTypeLiveData = MutableLiveData<PayTypeEntry>()
    val getPayTypeLiveData = _getPayTypeLiveData

    //数据转换
    private val _convertPayTypeLiveData = MutableLiveData<MutableList<PayWayBean>>()
    val convertPayTypeLiveData = _convertPayTypeLiveData

    //底部支付项
    private val _bottomPayItemLiveData = MutableLiveData<PaymentBottomPayItemShowView.PayItemShowBean>()
    val bottomPayItemLiveData: LiveData<PaymentBottomPayItemShowView.PayItemShowBean> = _bottomPayItemLiveData

    private val _jumpToAddBankCardLiveData = MutableLiveData<Any>()
    val jumpToAddBankCardLiveData: LiveData<Any> = _jumpToAddBankCardLiveData

    //农行链e贷勾选check
    private val _nongPayItemLiveData =
        MutableLiveData<BaseBean<PayNongCheckData>>()
    val nongPayItemLiveData: LiveData<BaseBean<PayNongCheckData>> =
        _nongPayItemLiveData

    /**
     * 获取收银台支付方式列表
     */
    fun getPaymentList(orderId: String, payRoute: String, orderNo: String, selectedPayCode: String = "", isPassCardId: Boolean = true, payId: String? = "", virtualCardId: String? = null, itemType: Int = -1,amount:String,
                       rechargeType:String) {
        viewModelScope.launch {
            val cardId = if (itemType == PAY_LAYOUT_TYPE_BANK_CARD) {
                payId
            } else getShowBankCardId()
            val tempVirtualId = if (virtualCardId == VIRTUAL_PAY_ID_ADD_BANK_CARD
                || virtualCardId == VIRTUAL_PAY_ID_RECOMMEND_BANK_CARD) {
                virtualCardId
            } else ""

            //购物金充值时不传orderId和orderNo
            val mOrderId = if (rechargeType == "2"){
                ""
            }else orderId

            val mOrderNo = if (rechargeType == "2"){
                ""
            }else orderNo

            val paymentListConfig =
                PayWayV2Request().paymentList(SpUtil.getMerchantid(), mOrderId, payRoute, mOrderNo, selectedPayCode, cardId?: "", tempVirtualId,amount, rechargeType)
            if (paymentListConfig.isSuccess) {
                if (pingAnAvailableAmount.isNullOrEmpty()) {
                    val pingAnPayChannelTipsBean = PayWayV2Request().getPingAnPayChannelTips()
                    if (pingAnPayChannelTipsBean.isSuccess) {
                        pingAnAvailableAmount = pingAnPayChannelTipsBean.data?.tips
                        mPinganPrice = pingAnPayChannelTipsBean.data?.availableAmount
                    }
                }
                mSelectedPayId = ""
                mSelectedPayCode = ""
                mPayConfigBean = paymentListConfig.data
                val fromPayConfig = PayTypeConfigV2Bean.fromPayConfig(paymentListConfig.data)
                val payTypeList = handlePaymentListData()
                fromPayConfig.payTypeList = payTypeList
                _paymentListBeanLiveData.postValue(fromPayConfig)
            }
            dismissLoading()
        }
    }

    /**
     * 提单页转换支付列表数据
     */
    fun convertPaymentList(payType: PaymentPayTypeBean?) {
        if (payType == null) return
        viewModelScope.launch {
            if (pingAnAvailableAmount.isNullOrEmpty()) {
                val pingAnPayChannelTipsBean = PayWayV2Request().getPingAnPayChannelTips()
                if (pingAnPayChannelTipsBean.isSuccess) {
                    pingAnAvailableAmount = pingAnPayChannelTipsBean.data?.tips
                    mPinganPrice = pingAnPayChannelTipsBean.data?.availableAmount
                }
            }
            mSelectedPayId = ""
            mSelectedPayCode = ""
            val payWayList = payType.payWayList
            payWayList.forEach {
                if (it.payType == PayWayBean.PAY_TYPE_ONLINE) {
                    //在线支付
                    mPayConfigBean = PayConfigBean()
                    mPayConfigBean!!.paymentlist = it.cashier?.paymentlist?: emptyList()
                    mPayConfigBean!!.extendPaymentList = it.cashier?.extendPaymentList?: emptyList()
                    val fromPayConfig = PayTypeConfigV2Bean.fromPayConfig(mPayConfigBean!!)
                    val payTypeList = handlePaymentListData()
                    fromPayConfig.payTypeList = payTypeList
                    it.payTypeConfigV2Bean = fromPayConfig
                    val payConfigBean = PayConfigBean()
                    payConfigBean.payTypeEntryList = payTypeList
                    it.cashier = payConfigBean
                    _paymentListBeanLiveData.postValue(fromPayConfig)
                }
            }
            _convertPayTypeLiveData.postValue(payWayList)
        }
    }

    /**
     * 展开银行卡，查看更多银行卡
     */
    fun expandBankCardList() {
        viewModelScope.launch {
            val handlePaymentListData = handlePaymentListData(isExpandedBankCard = true)
            val data = _paymentListBeanLiveData.value
            data?.let {
                it.payTypeList = handlePaymentListData
                _paymentListBeanLiveData.postValue(it)
            }
        }
    }

    /**
     * 查看更多支付方式
     */
    fun expandPayTypeList() {
        viewModelScope.launch {
            val handlePaymentListData = handlePaymentListData(isExpandedPayType = true)
            val data = _paymentListBeanLiveData.value
            data?.let {
                it.payTypeList = handlePaymentListData
                _paymentListBeanLiveData.postValue(it)
            }
        }
    }

    fun isSelectedBankCard(): Boolean {
        val bankCard = _paymentListBeanLiveData.value?.payTypeList?.find {
            it.itemType == PAY_LAYOUT_TYPE_BANK_CARD
        } as? PayTypeBankCard
        return bankCard?.isSelected?: false
    }

    /**
     * 根据用户行为加工数据
     */
    private suspend fun handlePaymentListData(
        isExpandedBankCard: Boolean = mIsExpandedBankCard,
        isExpandedPayType: Boolean = mIsExpandedPayType,
        selectedPayId: String = mSelectedPayId
    ): MutableList<PayTypeEntry> {
        mIsExpandedPayType = isExpandedPayType
        mIsExpandedBankCard = isExpandedBankCard
        //获取所有的银行卡,判断是否已经绑定银行卡如果没绑定需要从服务端获取推荐绑定的银行卡
        val backCardList = mPayConfigBean?.paymentlist?.find { it.paycode == PAY_TYPE_JDCARD}?.cardList
        if (backCardList.isNullOrEmpty() && recommendBankCard == null) {
            val queryOneKeySignBankList = PayWayV2Request().queryOneKeySignBankList()
            if (queryOneKeySignBankList.isSuccess && !queryOneKeySignBankList.data.bankList.isNullOrEmpty()) {
                mRecommendBankCardOrigin = queryOneKeySignBankList.data.bankList!![0]
                mAddBankCardDialogTips = queryOneKeySignBankList.data.dialogTips
                recommendBankCard = PayTypeBankCard(
                    "",
                    PAY_TYPE_JDCARD_ITEM,
                    0,
                    mRecommendBankCardOrigin?.bankShortName
                ).apply {
                    logoUrl = mRecommendBankCardOrigin?.bankLogo
                    payName = mRecommendBankCardOrigin?.bankShortName
                    itemType = PAY_LAYOUT_TYPE_BANK_CARD_RECOMMEND
                    canUse = true
                    isSelected = false
                    payTypeCode = mRecommendBankCardOrigin?.bankCode
                }
            }
        }
        val payTypeEntryList = mutableListOf<PayTypeEntry>()
        val forFunction = fun(payConfig: PayConfig) {
            if (payConfig.state != 1) return
            when (payConfig.paycode) {
                PAY_TYPE_ALIPY,
                PAY_TYPE_WEIXIN,
                PAY_TYPE_ALIPY_HUABEI,
                PAY_TYPE_UNIONPAY-> {
                    // 支付宝、花呗、银联
                    payTypeEntryList.add(
                        getBaseWithCommon(
                            PayTypeItem(payConfig.paycode),
                            payConfig,
                            PAY_LAYOUT_TYPE_BANK_ITEM
                        ).apply {
                            isSelected = payConfig.isSelected == 1
                            tips = payConfig.tips
                        }
                    )
                }
                PAY_TYPE_XYD->{
                    //小雨点
                    payTypeEntryList.add(
                            getBaseWithCommon(
                                    PayTypeItem(payConfig.paycode),
                                    payConfig,
                                    PAY_LAYOUT_TYPE_XYD
                            ).apply {
                                isSelected = payConfig.isSelected == 1
                                tips = payConfig.tips
                                xydPrice = payConfig.pingAnCredBalance
                            }
                    )
                }
                PAY_TYPE_JINDIE->{
                    //金蝶
                    payTypeEntryList.add(
                            getBaseWithCommon(
                                    PayTypeItem(payConfig.paycode),
                                    payConfig,
                                    PAY_LAYOUT_TYPE_JINDIE
                            ).apply {
                                isSelected = payConfig.isSelected == 1
                                tips = payConfig.tips
                                xydPrice = payConfig.pingAnCredBalance
                            }
                    )
                }
                PAY_TYPE_PINGAN -> {
                    //添加平安贷
                    payTypeEntryList.add(
                        getBaseWithCommon(
                            PayTypeItem(payConfig.paycode),
                            payConfig,
                            PAY_LAYOUT_TYPE_BANK_ITEM_PINGAN
                        ).apply {
                            isSelected = payConfig.isSelected == 1
                            pingAnTips = pingAnAvailableAmount
                            pinganPrice = "￥$mPinganPrice"
                            tips = payConfig.tips
                        }
                    )
                }
                PAY_TYPE_NONG -> {
                    //添加农行链e贷
                    payTypeEntryList.add(
                        getBaseWithCommon(
                            PayTypeItem(payConfig.paycode),
                            payConfig,
                            PAY_LAYOUT_TYPE_NONG
                        ).apply {
                            isSelected = payConfig.isSelected == 1
                            tips = payConfig.tips
                        }
                    )
                }
                PAY_TYPE_JDCARD -> {
                    //添加京东支付银行卡组
                    getBaseWithCommon(
                        PayTypeBankGroup(payConfig.cardList?.isNotEmpty() == true),
                        payConfig,
                        PAY_LAYOUT_TYPE_BANK_GROUP
                    ).apply {
                        isSelected = false
                        isShowBtn = payConfig.cardList?.isNotEmpty() == true
                        note = payConfig.note
                    }.let(payTypeEntryList::add)
                    val bankCardPayLogo = payConfig.logo
                    //添加圆角头部
                    PayTypeCommon().apply {
                        itemType = PAY_LAYOUT_TYPE_BANK_CARD_HEAD
                    }.let(payTypeEntryList::add)

                    //添加京东支付银行卡
                    mMtkTip = payConfig.mktTip
                    payConfig.cardList?.take(
                        if (isExpandedBankCard) {
                            payConfig.cardList.size
                        } else {
                            payConfig.cardShowCount
                        }
                    )?.find {
                       it.isSelected == 1
                    }?.let {
                        PayTypeBankCard(
                            it.cardId,
                            PAY_TYPE_JDCARD_ITEM,
                            it.isValidBankMobile,
                            it.bankShowName
                        ).apply {
                            logoUrl = it.bankLogo
                            payName = it.bankShowName
                            itemType = PAY_LAYOUT_TYPE_BANK_CARD
                            canUse = it.isCanUse
                            cardCount = payConfig.cardList?.count() ?: 0
                            isSelected = it.isSelected == 1
                                    && payConfig.isSelected == 1
                                    && payConfig.virtualCardId != VIRTUAL_PAY_ID_ADD_BANK_CARD
                                    && payConfig.virtualCardId != VIRTUAL_PAY_ID_RECOMMEND_BANK_CARD
                            isShowUnselected = payConfig.isSelected == 1
                                    && payConfig.virtualCardId != VIRTUAL_PAY_ID_ADD_BANK_CARD
                                    && payConfig.virtualCardId != VIRTUAL_PAY_ID_RECOMMEND_BANK_CARD
                            mktTip = payConfig.mktTip
                        }.let(payTypeEntryList::add)
                    }
                    //添加京东支付银行卡查看更多
//                    if (payConfig.cardList!= null && !isExpandedBankCard && payConfig.cardList.size > payConfig.cardShowCount) {
//                        PayTypeCheckAllBanks().apply {
//                            itemType = PAY_LAYOUT_TYPE_BANK_CARD_CHECK_ALL
//                        }.let(payTypeEntryList::add)
//                    }
                    //添加京东支付银行卡添加银行卡
                    PayTypeAddBankCard().apply {
                        itemType = PAY_LAYOUT_TYPE_BANK_CARD_ADD
                        mktTip = payConfig.mktTip
                        payCode = payConfig.paycode
                        isSelected = payConfig.virtualCardId == VIRTUAL_PAY_ID_ADD_BANK_CARD && payConfig.isSelected == 1
                        payId = VIRTUAL_PAY_ID_ADD_BANK_CARD
                        logoUrl = bankCardPayLogo
                    }.let(payTypeEntryList::add)

                    //添加推荐的银行卡
                    if (payConfig.cardList.isNullOrEmpty()) {
                        recommendBankCard?.apply {
                            mktTip = payConfig.mktTip
                            payCode = payConfig.paycode
                            isSelected = payConfig.virtualCardId == VIRTUAL_PAY_ID_RECOMMEND_BANK_CARD && payConfig.isSelected == 1
                            payId = VIRTUAL_PAY_ID_RECOMMEND_BANK_CARD
                        }?.let(payTypeEntryList::add)
                    }

                    //银行卡提示
                    PayTypeCommon().apply {
                        itemType = PAY_LAYOUT_TYPE_BANK_CARD_TIPS
                    }.let(payTypeEntryList::add)

                }
            }
            Unit
        }
        //添加默认支付方式
        mPayConfigBean?.paymentlist?.forEach(forFunction)
        if (isExpandedPayType) {
            //添加折叠的支付方式
            mPayConfigBean?.extendPaymentList?.forEach(forFunction)
        } else if (mPayConfigBean?.extendPaymentList?.isNotEmpty() == true){
            //折叠支付方式的列表有数据才添加展开更多按钮
            PayTypeExpandMore().apply {
                itemType = PAY_LAYOUT_TYPE_EXPAND_MORE
            }.let(payTypeEntryList::add)
        }

        //设置选中
        payTypeEntryList.forEach { payTypeEntry ->
            if ((payTypeEntry.itemType == PAY_LAYOUT_TYPE_BANK_CARD
                        || payTypeEntry.itemType == PAY_LAYOUT_TYPE_BANK_ITEM
                        || payTypeEntry.itemType == PAY_LAYOUT_TYPE_NONG
                        || payTypeEntry.itemType == PAY_LAYOUT_TYPE_BANK_ITEM_PINGAN
                        || payTypeEntry.itemType == PAY_LAYOUT_TYPE_BANK_CARD_RECOMMEND
                        || payTypeEntry.itemType == PAY_LAYOUT_TYPE_BANK_CARD_ADD
                        || payTypeEntry.itemType == PAY_LAYOUT_TYPE_XYD
                        || payTypeEntry.itemType == PAY_LAYOUT_TYPE_JINDIE
                    )
                && payTypeEntry is PayTypeCommon
                && payTypeEntry.canUse
            ) {
//                val selectStatus = mSelectedPayId == "" || selectedPayId == payTypeEntry.payId
                val selectStatus = payTypeEntry.isSelected
                payTypeEntry.isSelected = selectStatus
                if (selectStatus) {
                    mSelectedPayId = payTypeEntry.payId ?: ""
                    mSelectedPayCode = payTypeEntry.payCode?: ""
                    setBottomShow(payTypeEntry)
                }
            }
        }
        return payTypeEntryList
    }

    /**
     * 设置底部显示支付方式数据
     */
    private fun setBottomShow(payCommon: PayTypeCommon) {
        PaymentBottomPayItemShowView.PayItemShowBean(
            payCommon.logoUrl,
            payCommon.mktTip,
            payCommon.payName,
            null
        ).apply {
            when(payCommon.itemType) {
                PAY_LAYOUT_TYPE_BANK_CARD_RECOMMEND -> payTypeTitleTips = SpannableStringBuilder("免输卡号，支持50+银行")
                PAY_LAYOUT_TYPE_BANK_CARD_ADD -> {
                    payTypeTitleTips = SpannableStringBuilder("免输卡号，支持50+银行")
                    payTypeTitle = "添加银行卡支付"
                }
                PAY_LAYOUT_TYPE_BANK_ITEM_PINGAN -> {
                    payTypeTitleTips = if (payCommon.pinganPrice.isNullOrEmpty()) {
                        null
                    } else {
                        val pingAnMktTips = SpannableStringBuilder("可用额度 ")
                        val pingAnBalance = SpannableStringBuilder(payCommon.pinganPrice)
                        pingAnBalance.setSpan(
                            ForegroundColorSpan(Color.parseColor("#FF4D4D")),
                            0,
                            payCommon.pinganPrice!!.length,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        pingAnMktTips.append(pingAnBalance)
                        pingAnMktTips
                    }
                }
            }
        }.let(_bottomPayItemLiveData::postValue)

    }

    private fun getBaseWithCommon(
        payTypeCommon: PayTypeCommon,
        payConfig: PayConfig,
        itemType: Int
    ): PayTypeCommon {
        payTypeCommon.payName = payConfig.payname
        payTypeCommon.logoUrl = payConfig.logo
        payTypeCommon.tag = payConfig.mktIcon
        payTypeCommon.mktTip = payConfig.mktTip
        payTypeCommon.mktIcon = payConfig.mktIcon
        payTypeCommon.itemType = itemType
        payTypeCommon.canUse = payConfig.isCanUse
        payTypeCommon.payId = "${payConfig.payid}"
        payTypeCommon.pingAnCredBalance = payConfig.pingAnCredBalance
        payTypeCommon.payCode = payConfig.paycode
        return payTypeCommon
    }

    fun jumpToAddBankCard() {
        _jumpToAddBankCardLiveData.postValue(Any())
    }

    /**
     * 是否设置支付密码
     */
    fun queryPWSettingStatus() {
        viewModelScope.launch {
            val queryPWSettingStatus = JDPayRequest().queryPWSettingStatus()
            _jDPWSettingLiveData.postValue(queryPWSettingStatus)
        }
    }

    /**
     * 是否设置支付密码
     */
    fun queryPayPWSettingStatus() {
        viewModelScope.launch {
            val queryPWSettingStatus = JDPayRequest().queryPWSettingStatus()
            _jDPayPWSettingLiveData.postValue(queryPWSettingStatus)
        }
    }

    /**
     * 获取路由
     */
    fun getSelectedPayRouter(pageType: String, virtualId: String? = null): Pair<String?, String?>? {
        val payType = _paymentListBeanLiveData.value?.payTypeList?.find {
            it is PayTypeCommon && it.isSelected
        }?: return null
        val payCommon = payType as PayTypeCommon
        return when (virtualId?: payCommon.payId) {
            VIRTUAL_PAY_ID_ADD_BANK_CARD -> {
                Pair("", "ybmpage://addbankcard?bindResultFrom=$pageType")
            }
            VIRTUAL_PAY_ID_RECOMMEND_BANK_CARD -> {
                if (mRecommendBankCardOrigin == null) return null
                val jsonStr = Gson().toJson(mRecommendBankCardOrigin)
                val params = Base64.encodeToString(jsonStr.toByteArray(), Base64.URL_SAFE)
                Pair(mAddBankCardDialogTips, "ybmpage://addbankcarddetail?params=$params&bindResultFrom=$pageType")
            }
            else -> null
        }
    }

    /**
     * 获取支付参数
     */
    fun getPayParams(orderId: String, payRoute: String, payChannel: String?, orderNo: String?, reqScene: String,amount:String?="",rechargeType:String?="") {
        val payTypeEntity = getSelectedPayType()
        val payCode = (payTypeEntity as? PayTypeCommon)?.payCode?: ""
        val cardId = if(payTypeEntity != null && payTypeEntity is PayTypeBankCard) {
            (payTypeEntity as? PayTypeBankCard)?.cardId?: ""
        } else ""
        val entity = YBMPayEntity()
        if (YBMPayUtil.PAY_ALI_MINI == payCode) {
            mSelectedPayCode = YBMPayUtil.PAY_ALISDK
        }
        if (YBMPayUtil.PAY_WX_MINI == payCode) {
            mSelectedPayCode = YBMPayUtil.PAY_WXSDK
        }
        entity.payTypeForFrontKey = payCode
        entity.order_id = orderId
        entity.payRoute = payRoute
        entity.reqScene = reqScene
        entity.payId = (payTypeEntity as? PayTypeCommon)?.payId?: ""
        entity.tranNo = tranNo
        entity.amount = amount
        entity.rechargeType = rechargeType
        if (!TextUtils.isEmpty(payChannel)) {
            entity.payChannel = payChannel
        }
        //添加token
        if (!TextUtils.isEmpty(checkPasswordForPayToken)) {
            entity.token = checkPasswordForPayToken
        }
        saveToken("")
        //添加CardId
        if (!TextUtils.isEmpty(cardId)) {
            entity.cardId = cardId
        }
        //bankShowName
        if (payTypeEntity is PayTypeBankCard) {
            entity.bankShowName = payTypeEntity.bankShowName
        }

        if (!orderNo.isNullOrEmpty()) {
            entity.orderNo = orderNo
        }
        _getPayParamsLiveData.postValue(entity)
    }

    /**
     * 获取支付结果查询结果
     */
    fun getPayResultQueryParams(orderId: String?, orderNo: String?, payRoute: String?, reqScene: String? = "cashier") {
        val payTypeEntity = getSelectedPayType()
        val payCode = (payTypeEntity as? PayTypeCommon)?.payCode?: ""
        if (YBMPayUtil.PAY_ALI_MINI == payCode) {
            mSelectedPayCode = YBMPayUtil.PAY_ALISDK
        }
        if (YBMPayUtil.PAY_WX_MINI == payCode) {
            mSelectedPayCode = YBMPayUtil.PAY_WXSDK
        }
        val entity = YBMPayEntity()
        entity.payTypeForFrontKey = payCode
        entity.order_id = orderId
        entity.payRoute = payRoute
        entity.reqScene = reqScene
        entity.orderNo = orderNo
        val params = YBMPayParam()
        params.entity = entity
        _getPayResultParamsLiveData.postValue(params)
    }

    /**
     * 获取支付弹框
     */
    fun getPayDialog(orderNo: String?,isFromShoppingRecharge:Boolean) {
        viewModelScope.launch {
            val payTypeEntity = getSelectedPayType()
            val payCode = (payTypeEntity as? PayTypeCommon)?.payCode?: ""
            if (!isFromShoppingRecharge && orderNo == null){
                _payDialogLiveData.postValue(BaseBean.newFailureBaseBean(PayDialogBean(null, null, null)))
            } else {
                val result = PayResultRequest().getPayDialog(orderNo?:"", payCode)
                _payDialogLiveData.postValue(result)
            }
        }
    }

    private fun getSelectedPayType(): PayTypeEntry? {
        return _paymentListBeanLiveData.value?.payTypeList?.find {
            it is PayTypeCommon && it.isSelected
        }
    }

    /**
     * 获取支付类型
     * 这个方法会调到支付的接口去
     */
    fun getPayType(isUseVirtualMoney: Boolean = false) {
        viewModelScope.launch {
            var payTypeEntity = getSelectedPayType()
            if (payTypeEntity == null && isUseVirtualMoney) payTypeEntity = PayTypeCommon("", "")
            if (payTypeEntity is PayTypeBankCard || isUseVirtualMoney) {
                //银行卡
                val queryPWSettingStatus = QueryDeviceStatusOnPayRequest().queryDeviceStatusOnPay()
                if (queryPWSettingStatus.isSuccess) {
                    payTypeEntity?.deviceStatusOnPay = queryPWSettingStatus.data
                }
            }
            payTypeEntity?.let(_getPayTypeLiveData::postValue)
        }
    }

    /**
     * 保存支付钱验证密码生成的token
     */
    fun saveToken(token: String) {
        checkPasswordForPayToken = token
    }

    fun getToken() = checkPasswordForPayToken

    /**
     * 获取所有的银行卡
     */
    fun getBankCards(): MutableList<PayTypeEntry> {
        val cardList: MutableList<PayTypeEntry> =
            mPayConfigBean?.paymentlist
                ?.find { it.paycode == PAY_TYPE_JDCARD }
                ?.cardList
                ?.map {
                PayTypeBankCard(
                    it.cardId,
                    PAY_TYPE_JDCARD_ITEM,
                    it.isValidBankMobile,
                    it.bankShowName
                ).apply {
                    logoUrl = it.bankLogo
                    payName = it.bankShowName
                    itemType = PAY_LAYOUT_TYPE_BANK_CARD
                    canUse = it.isCanUse
                    isSelected = it.isSelected == 1
                }
        }?.toMutableList()?: mutableListOf()
        cardList.add(
            PayTypeAddBankCard().apply {
                itemType = PAY_LAYOUT_TYPE_BANK_CARD_ADD
            }
        )
        return cardList
    }

    /**
     * 切换银行卡
     */
    fun switchBankCard(payId: String) {
        if (mSelectedPayId == payId) return
        val cardList = mPayConfigBean?.paymentlist?.find {
            it.paycode == PAY_TYPE_JDCARD
        }?.cardList
        //查看选中的支付方式是否存在
        val isContainsCard = cardList?.any { it.cardId == payId } ?: false
        if (isContainsCard) {
            //银行卡组是否被选中
            val isBankCardGroupSelected = mPayConfigBean?.paymentlist?.find {
                it.paycode == PAY_TYPE_JDCARD
            }?.isSelected == 1
            if (isBankCardGroupSelected) {
                mSelectedPayId = payId
            }
            //更新选中状态并获取新选中卡的信息
            val selectedCard = cardList?.map {
                if (isBankCardGroupSelected) {
                    if (it.cardId == payId) {
                        it.isSelected = 1
                    } else {
                        it.isSelected = 0
                    }
                }
                it
            }?.find { it.cardId == payId }
            val payTypeConfigV2Bean = _paymentListBeanLiveData.value
            val payTypeList = payTypeConfigV2Bean?.payTypeList
            val oldIndex = payTypeList?.filterIsInstance<PayTypeCommon>()?.indexOfFirst { it.isSelected }?: -1
            val itemBankCard = payTypeList?.filterIsInstance<PayTypeCommon>()?.find { it is PayTypeBankCard }
            //将旧选中项设置为非选中
            payTypeList?.filterIsInstance<PayTypeCommon>()?.find { it.isSelected }?.isSelected = false
            // 新选中的卡信息替换旧卡信息
            val payTypeBankCard = PayTypeBankCard(
                selectedCard?.cardId,
                PAY_TYPE_JDCARD_ITEM,
                selectedCard?.isValidBankMobile?: 0,
                selectedCard?.bankShowName
            ).apply {
                logoUrl = selectedCard?.bankLogo
                payName = selectedCard?.bankShowName
                itemType = PAY_LAYOUT_TYPE_BANK_CARD
                canUse = selectedCard?.isCanUse?: false
                isSelected = true
                mktTip = itemBankCard?.mktTip
            }
            val index = payTypeList?.indexOfFirst {
                it.itemType == PAY_LAYOUT_TYPE_BANK_CARD
            }?: -1
            if (index != -1) {
                payTypeList?.removeAt(index)
                payTypeList?.add(1, payTypeBankCard)
            }
            //更新数据
            mPayConfigBean?.paymentlist
                ?.find { it.paycode == PAY_TYPE_JDCARD }
                ?.cardList?.forEach {
                    if (it.cardId == payId) it.isSelected = 1
                    else it.isSelected = 0
                }

            _selectPayTypeItemLiveData.postValue(
                PayTypeSelectedState(
                    if (oldIndex == -1) index else oldIndex,
                    index
                ).apply {
                    selectedBankCard = payTypeBankCard
                }
            )
        }
    }

    /**
     * 获取显示的银行卡id
     */
    fun getShowBankCardId(): String {
        return _paymentListBeanLiveData.value?.payTypeList?.find {
            it.itemType == PAY_LAYOUT_TYPE_BANK_CARD || it is PayTypeBankCard
        }?.let {
            if (it is PayTypeCommon) {
                it.payId
            } else ""
        } ?: ""
    }

    fun getPayId(): String {
        return mSelectedPayId
    }

    /**
     * 农行链e贷勾选check
     */
    fun checkAbChinaLoan(orderNo: String) {
        viewModelScope.launch {
            //银行卡
            val queryPWSettingStatus = JDPayRequest().checkAbChinaLoan(orderNo)
            _nongPayItemLiveData.postValue(queryPWSettingStatus)
        }
    }
}