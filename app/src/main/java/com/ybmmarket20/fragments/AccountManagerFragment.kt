package com.ybmmarket20.fragments

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.fragment.app.viewModels
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import butterknife.Bind
import butterknife.OnClick
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.utils.BugUtil
import com.ybm.app.view.CommonRecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.bean.AccountBean
import com.ybmmarket20.bean.Login
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.constant.SP_KEY_LOGIN_IS_KA
import com.ybmmarket20.db.AccountTable
import com.ybmmarket20.db.info.HandlerGoodsDao
import com.ybmmarket20.message.Message
import com.ybmmarket20.utils.*
import com.ybmmarket20.view.DividerLine
import com.ybmmarket20.view.EditTextWithDel
import com.ybmmarket20.viewmodel.AccountInfoViewModel
import com.ybmmarket20.xyyreport.session.SessionManager
import com.ybmmarketkotlin.activity.LoginVertificationActivity

/**
 * 账户管理(和基本信息合到一个activity里了)
 */
class AccountManagerFragment : BaseFragment() {
    @Bind(R.id.tv_edit_account)
    lateinit var tvEditAccount: TextView

    @Bind(R.id.tv_tip)
    lateinit var tvTip: TextView

    @Bind(R.id.rv_account_list)
    lateinit var rvAccountList: CommonRecyclerView

    @Bind(R.id.tv_add_account)
    lateinit var tvAddAccount: TextView

    @Bind(R.id.title_et)
    lateinit var titleEt: EditTextWithDel

    private var mIsEdit: Boolean = false
    private val mData = ArrayList<AccountBean>()
    private val allList = ArrayList<AccountBean>()
    private val accountViewModel: AccountInfoViewModel by viewModels()

    private fun initObserver() {
        accountViewModel.loginWithMerchantLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                if (it.data.loginMerchantInfo?.isCrawler == true) {
                    launcher?.launch(LoginVertificationActivity.getStartIntent(notNullActivity, SpUtil.getLoginPhone(), SpUtil.getMerchantid()))
                } else {
                    processLoginData(notNullActivity, "ybmpage://main")
                }
            } else {
                RoutersUtils.open("ybmpage://addaccount/1")
            }
        }
    }

    private val mAccountAdapter = object : YBMBaseAdapter<AccountBean>(R.layout.item_account_manager, mData) {
        override fun bindItemView(holder: YBMBaseHolder, bean: AccountBean) {
            holder.setText(R.id.tv_shop_name, bean.shopName)
            holder.setText(R.id.tv_shop_phone, "电话：${getSafePhone(bean.phone)}")
            holder.setText(R.id.tv_shop_address, "地址：${getSafeAddress(bean.address)}")
            val contentView = holder.getConvertView()
            val ivStatue = holder.getView<ImageView>(R.id.iv_statue)
            val res: Int
            val merchant_id = SpUtil.getMerchantid()
            val accountId = SpUtil.getAccountId()
            val phoneNo = SpUtil.getLoginPhone()
            res = if (mIsEdit) {//编辑状态下
                if (TextUtils.equals(bean.phone, phoneNo)) R.drawable.icon_account_manager_checkbox_disselect else R.drawable.icon_delete_red
            } else {//非编辑状态下
                if (TextUtils.equals(bean.phone, phoneNo)) R.drawable.checkbox_select else R.drawable.transparent
            }
            ivStatue.setImageResource(res)
            contentView.isEnabled = !mIsEdit
            ivStatue.isEnabled = mIsEdit
            contentView.setOnClickListener(View.OnClickListener {
                val position = holder.adapterPosition
                if (position >= 0 && mData.size > position && bean.phone != phoneNo) { //如果item已经移出了列表会返回-1(NO_POSITION)
                    switchAccount(mData[position] as AccountBean)
                    (notNullActivity.application as YBMAppLike).saasOrderSourcePath = null
                }
            })

            ivStatue.setOnClickListener(View.OnClickListener {
                if (bean.phone == phoneNo) {
                    return@OnClickListener
                }
                val position = holder.adapterPosition
                if (position >= 0 && mData.size > position) {
                    val item = mData[position] as AccountBean
                    deleteAccount(if (item != null) item.phone else "")
                }
            })
        }
    }

    /**
     * 手机号隐藏中间号码
     */
    private fun getSafePhone(phone: String?): String {
        return phone?.also {
            if (phone.contains("*")) return phone
            if (phone.length < 11) return phone
            return "${it.substring(0, 3)}****${it.substring(7, it.length)}"
        }?: ""
    }

    /**
     * 隐藏部分地址
     */
    private fun getSafeAddress(address: String?): String {
        return address?.also {
            val result = when {
                address.contains("市") -> {
                    it.substring(0, it.indexOf("市") + 1)
                }
                address.contains("区") -> {
                    it.substring(0, it.indexOf("区") + 1)
                }
                else -> {
                    if (it.length > 10) {
                        it.substring(0, 10)
                    } else address
                }
            }
            val star = "*".repeat(address.length - result.length)
            return "$result$star"
        }?: ""
    }

    override fun initData(content: String) {
        rvAccountList.setRefreshEnable(false)
        rvAccountList.setShowAutoRefresh(false)
        rvAccountList.setLoadMoreEnable(false)
        val divider = DividerLine(DividerLine.VERTICAL)
        divider.setSize(1)
        divider.setColor(-0xe0e0f)
        rvAccountList.addItemDecoration(divider)
        rvAccountList.setAdapter(mAccountAdapter)

        titleEt.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable) {
                search(s.toString())
            }
        })
        initObserver()
    }

    private fun search(key: String) {
        var tmp: MutableList<AccountBean> = ArrayList()
        if (TextUtils.isEmpty(key)) {
            tmp = allList
        } else {
            for (bean in allList) {
                if (!TextUtils.isEmpty(bean.shopName) && bean.shopName.contains(key)) {
                    tmp.add(bean)
                }
            }
        }
        setListData(tmp)
    }

    private fun setListData(data: List<AccountBean>?) {
        var data = data
        if (titleEt == null) {
            return
        }
        if (data == null) {
            data = ArrayList()
        }
        mData.clear()
        mData.addAll(data)
        mAccountAdapter.setNewData(mData)

    }

    override fun onResume() {
        super.onResume()
        val list = AccountTable.getAllAccount(notNullActivity)
        if (list != null) {
            mData.clear()
            mData.addAll(list)
            allList.clear()
            allList.addAll(list)
            mAccountAdapter.notifyDataSetChanged()
        }
    }

    @OnClick(R.id.tv_add_account, R.id.tv_edit_account)
    fun onViewClicked(v: View) {
        when (v.id) {
            R.id.tv_add_account//添加账号
            -> if (mIsEdit) {//完成按钮
                mIsEdit = false
                tvEditAccount.visibility = View.VISIBLE
                tvAddAccount.text = "添加新账号"
                tvTip.visibility = View.VISIBLE
                mAccountAdapter.notifyDataSetChanged()
            } else {
                RoutersUtils.open("ybmpage://addaccount")
            }
            R.id.tv_edit_account//编辑
            -> {
                mIsEdit = true
                tvEditAccount.visibility = View.GONE
                tvAddAccount.text = "完成"
                tvTip.visibility = View.GONE
                mAccountAdapter.notifyDataSetChanged()
            }
        }
    }

    private fun switchAccount(bean: AccountBean) {
        val loginPhone = bean.phone
        val checkLoginPhone = bean.userName
        val merchantId = bean.merchantId
        val loginPassword = bean.password
        if (TextUtils.isEmpty(loginPhone) || TextUtils.isEmpty(loginPassword)) {
            //其中"1"表示有错误，需要处理
            RoutersUtils.open("ybmpage://addaccount/1")
            return
        }
        showProgress("正在切换账号")
        accountViewModel.loginWithMerchant(loginPhone, loginPassword, merchantId)
    }

    private var launcher: ActivityResultLauncher<Intent>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        launcher = registerForActivityResult(StartActivityForResult()) { result ->
            if (result.data?.getBooleanExtra(IntentCanst.LOGINVERTIFICATIONRESULT, false) == true) {
                checkoutLoginData?.let {
                    storeAndProcessLoginData(checkoutLoginData!!, checkLoginPhone)
                }
            }
        }
        super.onCreate(savedInstanceState)
    }

    private var checkoutLoginData: Login? = null
    private var checkLoginPhone: String? = null

    private fun storeAndProcessLoginData(data: Login, loginPhone: String?) {
        AuditStatusSyncUtil.getInstance().setLicenseStatusOnly(data.licenseStatus)
        SpUtil.setValidityStatus(data.validity)
        SpUtil.setLoginPhone(loginPhone)
        //保存并更新登录的信息
        updateLogin(data)
        //清除usersp电话信息
        SpUtil.writeString("phone", "")
        SpUtil.writeInt("show_ad_collect_pop", 1)
        SpUtil.writeInt("show_dialog_in_pay_result", 0)
        // 是否是ka用户
        SpUtil.writeBoolean(SP_KEY_LOGIN_IS_KA, data.isKa)
        //清除密码强度的提示
        SpUtil.writeBoolean("already_mention", false)
        //后台获取购物车的信息
        YbmCommand.getCartListForBack()
        YbmPushUtil.bindPushtoken()
        //重新获取消息数量

        notNullActivity.finish()
    }


    private fun deleteAccount(phoneNo: String) {
        if (TextUtils.isEmpty(phoneNo) || isDestroy) {
            ToastUtils.showShort("删除失败")
            return
        }
        val alert = AlertDialogEx(notNullActivity)
        alert.setTitle("删除")
        alert.setMessage("是否删除该账号？")
        alert.setCancelButton("取消", null)
        alert.setConfirmButton("确定") { dialog, button ->
            val isDelete = AccountTable.delete(notNullActivity.applicationContext, phoneNo)
            if (isDelete) {
                ToastUtils.showShort("删除成功")
                val list = AccountTable.getAllAccount(notNullActivity.applicationContext)
                mData.clear()
                if (list != null) {
                    mData.addAll(list)
                }
                allList.clear()
                if (list != null) {
                    allList.addAll(list)
                }
                mAccountAdapter.notifyDataSetChanged()
            } else {
                ToastUtils.showShort("删除失败")
            }
        }
        alert.show()

    }

    fun updateLogin(login: Login?) {
        if (login == null) {
            return
        }
        val merchantId = login.merchantId
        if (merchantId != null && java.lang.Long.parseLong(merchantId) > 0) {
            SessionManager.get().newSession()
            SpUtil.setMerchantid(merchantId + "")
            BugUtil.updateUserId(SpUtil.getMerchantid())
            SpUtil.setToken(login.token)
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_account_manager
    }

    override fun initTitle() {

    }

    override fun getParams(): RequestParams? {
        return null
    }

    override fun getUrl(): String? {
        return null
    }
}

fun processLoginData(context: Context, jumpUrl: String) {
    LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_AD_COLLECT_HINT_POP))
    //登陆广播
    LocalBroadcastManager.getInstance(context.applicationContext).sendBroadcast(Intent(IntentCanst.ACTION_REFRESH_RECOMMEND))
    //登陆广播
    LocalBroadcastManager.getInstance(context.applicationContext).sendBroadcast(Intent(IntentCanst.ACTION_LOGIN))
    //更新购物车数量广播
    LocalBroadcastManager.getInstance(context.applicationContext).sendBroadcast(Intent(IntentCanst.ACTION_SHOPNUMBER))
    //购物车商品数
    HandlerGoodsDao.getInstance().create4Sp()
    //通知"我的"页面重新刷新数据
    LocalBroadcastManager.getInstance(context.applicationContext).sendBroadcast(Intent(IntentCanst.ACTION_SWITCH_USER))
    LocalBroadcastManager.getInstance(context.applicationContext).sendBroadcast(Intent(IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE))
    //通知"全部药品列表"页面重新刷新数据
    LocalBroadcastManager.getInstance(context.applicationContext).sendBroadcast(Intent(IntentCanst.ACTION_BRAND_SET))
    //通知首页更新
//    LocalBroadcastManager.getInstance(context.applicationContext).sendBroadcast(Intent(IntentCanst.REFRESH_PAGE))
    Message.instance.findUnReadCount()
    RoutersUtils.open(jumpUrl)
}
