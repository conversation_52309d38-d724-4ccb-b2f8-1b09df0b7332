package com.ybmmarket20.constant;

import android.net.Uri;
import android.text.TextUtils;

import com.ybm.app.utils.SpUtil;
import com.ybmmarket20.BuildConfig;

/**
 * API 接口管理类
 */
public class AppNetConfig {
    //有路径的API
    public static String HOST = getApiUrl();

    public static String PING_AN_URL = getPingAnPrePayUrl();
    public static String PING_AN_PAY_URL = getPingAnPayUrl();

    //图片加载
    public static String CDN_HOST = getCDNHost();

    public static String HOST_URL = null;
    public static String STATIC_HOST_URL = null;
    public static String STATIC_HOST_HTTPS_URL = null;
    public static String STATIC_HOST_HTTPS_URL_NEW_STATIC = null;
    public static String API_PATH = null;
    public static String CDN_URL = null;

    public final static String API_HOST_KEY = "ybm_api_host";
    public final static String API_STATIC_KEY = "ybm_api_static";
    public final static String API_STATIC_HTTPS_KEY = "ybm_api_static_https";
    public final static String API_PATH_KEY = "ybm_api_path";
    public final static String API_CDN_KEY = "ybm_api_cdn";
    public final static String API_PING_AN_URL = "ping_an_url";
    public final static String API_PING_AN_PAY_URL = "ping_an_pay_url";

    /*------------------------------------------正式环境-------------------------------------------*/
    //线上版本API
    public static final String API_HOST_RELEASE = "http://app-v4.ybm100.com/";
    //线上路径
    public static final String API_PATH_RELEASE = "app/";
    //图片加载
    public static final String CDN_HOST_RELEASE = "http://upload.ybm100.com";
    //静态资源
    public static final String STATIC_HOST_RELEASE = "http://app-v4.ybm100.com/static/";

    //静态资源
    public static final String STATIC_HOST_RELEASE_HTTPS = "https://app-v4.ybm100.com/static/";
    public static final String STATIC_HOST_RELEASE_HTTPS_NEW_STATIC = "https://app-v4.ybm100.com/newstatic/";

    //平安进件
    public static final String PING_AN_PRE_PAY_RELEASE = "https://b.pingan.com.cn/kyb/apply/index.html#/pfcs/home";
    //平安支付
    public static final String PING_AN_PAY_RELEASE = "https://cloudpayweb.orangebank.com.cn/h5/creditSdk/cashier";

    /*----------------------------------------测试环境--------------------------------------------*/

    //测试版本API
    public static final String API_HOST_TEST = "http://new-app.test.ybm100.com/";

    //测试路径
    public static final String API_PATH_TEST = "app/";

    //  图片加载
    public static final String CDN_HOST_TEST = "http://t-upload.ybm100.com";

    //测试静态资源
    public static final String STATIC_HOST_TEST = "http://t-app.ybm100.com/static/";

    //测试静态资源
    public static final String STATIC_HOST_TEST_HTTPS = "https://new-app.test.ybm100.com/static/";
    public static final String STATIC_HOST_TEST_HTTPS_NEW_STATIC = "https://new-app.test.ybm100.com/newstatic/";

    //平安进件
    public static final String PING_AN_PRE_PAY = "https://test-b-fat.pingan.com.cn/kyb7/apply/index.html#/pfcs/home";
    //平安支付
    public static final String PING_AN_PAY = "https://cloudpayweb-fat7.orangebank.com.cn/h5/creditSdk/cashier";

    /*----------------------------------------预发环境--------------------------------------------*/
    //本地测试版本API
    public static final String API_HOST_STAGE = "http://new-app.stage.ybm100.com/";

    //本地测试路径
    public static final String API_PATH_STAGE = "app/";

    //  本地图片加载
    public static final String CDN_HOST_STAGE = "http://t-upload.ybm100.com";

    //本地静态资源
    public static final String STATIC_HOST_STAGE = "https://new-app.stage.ybm100.com/static/";

    //本地静态资源
    public static final String STATIC_HOST_STAGE_HTTPS = "https://new-app.stage.ybm100.com/static/";
    public static final String STATIC_HOST_STAGE_HTTPS_NEW_STATIC = "https://new-app.stage.ybm100.com/newstatic/";

    /*----------------------------------------开发环境--------------------------------------------*/
    //本地测试版本API
//    public static final String API_HOST_DEV = "http://new-app.dev.ybm100.com/";
    public static final String API_HOST_DEV = "http://ecapp.ybm100.com/";

    //本地测试路径
    public static final String API_PATH_DEV = "app/";

    //  本地图片加载
    public static final String CDN_HOST_DEV = "http://t-upload.ybm100.com";

    //本地静态资源
    public static final String STATIC_HOST_DEV = "https://new-app.dev.ybm100.com/static/";

    //本地静态资源
    public static final String STATIC_HOST_DEV_HTTPS = "https://new-app.dev.ybm100.com/static/";
    public static final String STATIC_HOST_DEV_HTTPS_NEW_STATIC = "https://new-app.dev.ybm100.com/newstatic/";

    /*----------------------------------------新预发环境--------------------------------------------*/
    //本地测试版本API
    public static final String API_HOST_NEW_STAGE = "http://app-new.stage.ybm100.com/";

    //本地测试路径
    public static final String API_PATH_NEW_STAGE = "app/";

    //  本地图片加载
    public static final String CDN_HOST_NEW_STAGE = "http://upload.ybm100.com";

    //本地静态资源
    public static final String STATIC_HOST_NEW_STAGE = "https://app-new.stage.ybm100.com/static/";

    //本地静态资源
    public static final String STATIC_HOST_NEW_STAGE_HTTPS = "https://app-new.stage.ybm100.com/static/";

    /*----------------------------------------api接口开始------------------------------------*/
    //更新api 接口
    public static String API = HOST + "index/fetchApiUrl";

    //为你推荐h5
    public static final String URL_RECOMMEND_BETA = "xyyvue/dist/#/recomlist?ybm_title=为你推荐";

    //为你推荐h5
    public static final String URL_RECOMMEND_RELEASE = "xyyvue/dist/#/recomlist?ybm_title=为你推荐";

    //登录
    public static String LOGIN = HOST + "login";
    public static String NEWLOGIN = HOST + "newLogin";
    //登录商户基本信息
    public static String LOGIN_INFO = HOST + "checklogin";
    public static String NEW_LOGIN_INFO = HOST + "newChecklogin";

    public static String REGISTER_SEARCH = HOST + "register/search";
    //注册
    public static String REGISTER = HOST + "registerStep1";
    //注册-添加店铺
    public static String REGISTER_ADD_SHOP = HOST + "register/checkname";
    //添加店铺
    public static String MERCHANT_ADD_SHOP = HOST + "merchant/add";
    //注册邀请码
    public static String REGISTER_STEP = HOST + "registerStep2";
    //我的-商户基本信息
    public static String BASE_INFO = HOST + "findMerchantBaseInfo";
    // 我的-常用工具
    public static String USETOOLS = HOST + "getuseTools";
    // 获取常用工具-推荐清单 读取状态
    public static String GET_PUSH_STATUS_FROM_CRM = HOST + "sku/selectPushStatusByMerchantId";



    //region 支付相关接口
    //支付配置信息
    public static String PAYCONFIG = HOST + "paymentlist";
    // 药学院支付配置
    public static String PAYCONFIG_FOR_DRUGSCHOOL = HOST + "virtual/goods/paymentlist";
    //实名认证确认验证接口
    public static String CERTIFICATION = HOST + "ra/smsAuthentication";
    //重新发送验证码
    public static String AGAIN_SENDCODE = HOST + "ra/authenticationRepeat";
    //判断是否需要身份确认弹框
    public static String IS_NEED_POP = HOST + "ra/authentication";
    //首页实名认证弹框
    public static String ALERT_FLAG = HOST + "merchantAuthentication/getAlertFlag";
    //发送验证码
    public static String SEND_CODE = HOST + "merchantAuthentication/sendAuthenticationVerificationCode";
    //更换手机号确认提交接口
    public static String CHANGE_PHONE_SUBMIT = HOST + "merchantAuthentication/changeAuthMobile";
    //查看认证信息
    public static String GET_INFOBY_MERCHANTID = HOST + "merchantAuthentication/getInfoByMerchantId";
    //支付sdk_content
    public static String SDK_PAYMENT = HOST + "payment";
    public static String SDK_PAYMENTV2 = HOST + "payment/V2";
    public static String SDK_PAYMENTV3 = HOST + "payment/V3";
    // 药学院支付 sdk content 获取
    public static String SDK_PAYMENT_FOR_DRUGSCHOOL = HOST + "virtual/goods/payment";
    //支付结果查询
    public static String ORDERPAYRESULT = HOST + "queryPay";

    // 支付结果页红包推荐
    public static String ORDERPAYRESULT_REBATEVOUCHER = HOST + "marketing/rebateVoucher/getMarketingRebateVoucherTemplateList";




    // 药学院支付结果查询
    public static String ORDERPAYRESULT_FOR_DRUGSCHOOL = HOST + "virtual/goods/queryPay";
    //支付成功页面查看对应用户是否开户
    public static String FIND_ACCOUNT_IS_OPEN = HOST + "findAccountIsOpen";
    //endregion


    //<editor-fold desc=" 营销相关接口 ">

    // 查询商品营销相关信息（ 包括促销、优惠信息 ）
    public static String SKU_PROMOTION = HOST + "promotion/csuDetail/activityInfo";
    public static String SKU_DETAIL_PROMOTION = HOST + "promotion/csuDetail/promoInfo";
    //</editor-fold>


    // 店铺列表获取
    public static String SHOP_LIST = HOST + "shop/shopList";
    public static String VOUCHER_SHOP_LIST = HOST + "voucher/shopList";


    //商品列表
    public static String SORTNET = HOST + "search/v1/productList";
    //商品列表_V2
    public static String SORTNET_V2 = HOST + "search/v2/listProducts";
    //商品列表KA
    public static String SORTNET_KA = HOST + "ka/search/productList";
    //专区搜索
    public static String SORTNET_SECTION = HOST + "section/search/listProducts";
    //订单凑单搜索
    public static String SORTNET_ORDER_BUNDLING = HOST + "order/bundling/search/listProducts";

    // 搜索筛选项获取
    public static String SORTNET_aggs = HOST + "search/v1/aggs";
    //大搜V2
    public static String SORTNET_AGGS_V2 = HOST + "search/v2/aggs";
    public static String SORTNET_AGGS_SECTION = HOST + "section/search/aggs";
    // 组合购、加价购 订单组合信息查询
    public static String SORTNET_COMBINEDINFO_QUERY = HOST + "combinedInfoQuery";
    //订单凑单搜索
    public static String SORTNET_AGGS_ORDER_BUNDLING = HOST + "order/bundling/search/aggs";

    public static String LIST_GROUP_BUYING_PRODUCT_CATEGORIES = HOST + "sku/listGroupBuyingProductCategories";


    //高毛搜索
    public static String FIND_SKU_INFO_FOR_GROSS = HOST + "sku/findSkuInfoForGross";

    //分类---------一级分类
    public static String STAIR_SORTNET = HOST + "category/getCategoryList";
    //详细信息
    public static String DETAIL_MESSAGE = HOST + "sku/findSkuDetail";
    // 商品详情折扣价
    public static String DETAIL_MESSAGE_DISCOUNT = HOST + "marketing/discount/satisfactoryInHandPriceWithDetail";
    // 商品列表折扣价
    public static String LIST_PRODUCT_DISCOUNT = HOST + "marketing/discount/satisfactoryInHandPrice";
    //ka详细信息
    public static String KA_DETAIL_MESSAGE = HOST + "ka/search/findSkuDetail";
    //限时特惠
    public static String TIME_FAVORABLE = HOST + "product/findProduct";
    //加载小图片
    public static String LORD_IMAGE = CDN_HOST + "/ybm/product/min/";

    //加载品牌
    public static String LORD_BRAND_IMAGE = CDN_HOST + "/ybm/brand/";

    //加载大图片
    public static String LORD_BIDIMAGE = CDN_HOST + "/ybm/product/";
    //加载商品详情页轮播图路径
//    public static String LORD_PICIMAGE = CDN_HOST + "/ybm/product/pic/min/";
    public static String LORD_PICIMAGE = CDN_HOST + "/ybm/product/pic/";
    //商品详情-视频拼接地址
    public static String LORD_IMAGESVIDEOS = CDN_HOST + "/ybm/product/video/";
    //标签图片
    public static String LORD_TAG = CDN_HOST;
    //加载大图结尾
    public static String L_0 = "_L_0.jpg";

    //产品说明书加载小图片
    public static String LORD_IMAGE_SPECIFICATION = CDN_HOST + "/ybm/product/desc/min/";
    //产品说明书加载大图片
    public static String LORD_BIDIMAGE_SPECIFICATION = CDN_HOST + "/ybm/product/desc/";
    //活动图片地址
    public static String LORD_ACTIVITY_TAG_MARKER = CDN_HOST + "/ybm/product/desc/";

    //获取收货地址
    public static String GET_ADDRESS = HOST + "findGSPAddress";
    public static String GET_ADDRESS_V3 = HOST + "findAddressList";
    //药帮忙地址新接口兼容ka地址
    public static String GET_ADDRESS_NEW = HOST + "findAllAddress";
    //编辑地址
    public static String EDIT_ADDRESS = HOST + "updateGSPAddress";

    //加入清单
    public static String ADD_ORDER_SELL_NO = HOST + "addOrderSellNo";
    //剔除清单
    public static String CANCEL_ORDER_SELL_NO = HOST + "cancelOrderSellNo";
    //收藏
    public static String COLLECT = HOST + "favorite/attention";
    //取消收藏
    public static String CANCEL_COLLECT = HOST + "favorite/cancelAttention";
    //收藏列表
    public static String COLLECTPAGER = HOST + "favorite/findAttention";
    //批量取消收藏商品
    public static String CANCEL_ATTENTION_BATH_DELETE = HOST + "favorite/cancelAttentionBath";
    //常用采购
    public static String COMMON_PROCURE = HOST + "orders/common";
    //我的礼品包
    public static String MINE_VIP_GIFT = HOST + "giftpackage/selectUserPackage";
    //购买商品
    public static String BUY_COMMODITY = HOST + "changeCart";
    //购物车
    public static String CART = HOST + "cart";
    //购物车-新
    public static String CART_V1 = HOST + "cart/v1";
    //购物车-查询店铺优惠券信息
    public static String CART_SHOP_VOUCHER = HOST + "cart/getShopVoucher";

    //购物车底部优惠券弹窗数据
    public static String CART_BOTTOM_COUPON = HOST + "cart/v1/findShopVoucher";
    //购物车底部优惠券弹窗数据-tab样式（商家券+平台券）
    public static String CART_BOTTOM_SHOP_COUPON = HOST + "cart/v1/findShopCoupon";

    //购物车-底部弹框选中优惠券下商品
    public static String CART_SELECT_GOODS_FOR_COUPON = HOST + "cart/voucher/changeCart";

    //购物车商品数量
    public static String CART_NUM = HOST + "getCartNum";

    //删除购物车数据
    public static String CART_DELETE = HOST + "batchRemoveProductFromCart";

    //购物车选择单个商品
    public static String SELECT_ITEM = HOST + "selectItem";
    //购物车取消选择单个商品
    public static String CANCEL_ITEM = HOST + "cancelItem";
    //购物车选择所有商品
    public static String SELECT_ALL_ITEM = HOST + "selectAllItem";
    //购物车取消选择所有商品
    public static String CANCEL_ALL_ITEM = HOST + "cancelAllItem";

    //购物车去结算-新接口
    public static String ORDER_V1_PRESETTLE = HOST + "order/v1/preSettle";

    //代下单去结算（校验是否可以跳转到待确认订单页）-新接口
    public static String ORDER_SPROUT_V2_PRESETTLE = HOST + "order/sprout/v2/preSettle";

    //订单中心
    public static String ORDER = HOST + "findOrders";
    //订单商品可购买状态
    public static String ORDER_STOCK_STATE = HOST + "orders/getOrderStockState";
    //剩余商品加入购物车
    public static String ORDER_CART_ADD = HOST + "orders/cartAdd";
    //找相似
    public static String ORDER_FIND_SAME_PRODUCT = HOST + "recommend/v1/failureSimilarProducts";

    //查询订单是否已经生成-new
    public static String ORDER_V1_FINDORDER = HOST + "order/v1/findOrder";
    //订单详情
    public static String ORDER_DETAIL = HOST + "orders/detail";
    //订单数量状态
    public static String ORDER_FIND_NUM_GROUP_BY_STATUS = HOST + "orders/findNumGroupByStatus";
    //交易快照列表
    public static String TRADING_SNAPSHOT_LIST = HOST + "orderTransactionSnapshot/getOrderSnapshotListByOrderNo";
    //交易快照详情
    public static String TRADING_SNAPSHOT_DETAIL = HOST + "orderTransactionSnapshot/getOrderSnapshotById";

    //订单明细分享
    public static String ORDER_SHARE = HOST + "orders/share";
    // 订单明细入库价提示
    public static String SHOULD_SHOWDIALOG_AUTO = HOST + "shouldShowDialogAuto";

    //取消大礼包
    public static String CANCEL_BIG_GIFT_PACKAGE = HOST + "cancelBigGiftPackage";

    //确认订单-new
    public static String ORDER_V1_ORDERCONFIRM = HOST + "order/v1/orderConfirm";
    //代下单确认订单-new
    public static String ORDER_SPROUT_V2_ORDERCONFIRM = HOST + "order/sprout/v2/orderConfirm";

    //取消订单
    public static String ORDERS_CANCEL = HOST + "orders/cancel";
    //删除订单 
    public static String ORDERS_DELETE = HOST + "orders/delete";
    //再次购买订单商品
    public static String ORDERS_AGAINBUY = HOST + "orders/rebuyForOrder";
    //领取余额
    public static String ORDERS_BALANCE = HOST + "balanceJournal/addBalanceJournal";
    //确认收货接口
    public static String ORDER_RECEIPT = HOST + "orders/finish";

    //待支付订单-新接口
    public static String ORDERS_V1_SETTLE = HOST + "order/v1/settle";

    //代下单待支付订单-新接口
    public static String order_sprout_v2_settle = HOST + "order/sprout/v2/settle";

    //图片上传
    public static String PICTURE_CALL = HOST + "uploadFile/invoke";

    //上传采购单图片
    public static String UPLOAD_PICTURE = HOST + "planningSchedule/picture/uploadPicture";

    //上传评论图片
    public static String UPLOAD_COMMENT_PICTURE = HOST + "evaluate/uploadPicture";

    //保存采购单图片
    public static String PICTURE_ADD = HOST + "planningSchedule/picture/add";

    //解密药口令
    public static String DECODECOMMAND = HOST + "share/getSkuDetail";

    //加密药口令
    public static String ENCODECOMMAND = HOST + "share/share";

    //资质信息
    public static String APTITUDE_INFO = HOST + "license/findLicenseCategoryInfoNew";

    //资质页面
    public static String FIND_LICENSE_LIST = HOST + "license/findLicenseList";
    //资质管理点击添加首营资质
    public static String INIT_LICENSE_AUDIT_DETAIL = HOST + "licenseAudit/initLicenseAuditDetail";
    //资质上传图片
    public static String LICENSE_AUDIT_UPLOADIMG = HOST + "licenseAudit/uploadImg";
    //添加店铺上传图片
    public static String ADD_SHOP_UPLOAD_IMAGE = HOST + "licenseAudit/account/uploadImg";
    //添加首营资质
    public static String ADD_LICENSE_AUDIT = HOST + "licenseAudit/addLicenseAudit";
    //变更资质
    public static String UPDATE_LICENSE_AUDIT = HOST + "licenseAudit/updateLicenseAudit";
    //资质变更记录
    public static String QUERY_LICENSE_AUDIT_LIST = HOST + "licenseAudit/queryLicenseAuditList";
    //资质详情
    public static String QUERY_LICENSE_AUDIT_DETAIL = HOST + "licenseAudit/queryLicenseAudit";
    //审批日志
    public static String QUERY_LICENSE_AUDIT_LOG_LIST = HOST + "licenseAudit/queryLicenseAuditLogList";
    //资质编辑前init
    public static String INIT_BILL_DETAIL = HOST + "licenseAudit/initBillDetail";
    //下载示例和模板图片
    public static String LICENSE_DOWNLOAD_VIEW_EXAMPLES_IMG = HOST + "licenseAudit/down/viewExamplesImg.json";
    //资质优化 获取添加首营或变更资质基本信息
    public static String QUERY_LICENSE_AUDIT_BASICINFO = HOST + "licenseAudit/getMerchantBasicInfo";
    // 资质管理 资质列表
    public static String LICENSE_AUDIT_FIND_LICENSE_LIST = HOST + "licenseAudit/findLicenseList";
    //资质认证
    public static String APITUDE_AUTHENTICTION = HOST + "license/authentication";

    //资质加载图片
    public static String APTITUDE_LOAD_IAMGE = CDN_HOST + "/ybm/license/";
    //资质图片结尾
    public static String APTITUDE_END = "/min/";
    //保存资质
    public static String SAVE_APTITUDE = HOST + "license/saveLicenseInfo";
    //更新指定资质信息
    public static String UPDATE_APTITUDE = HOST + "license/updateLicenseInfo";

    //pop-开户
    public static String GET_KAIHU_INFO = HOST + "getkaiHuInfo";

    //修改邮箱
    public static String UPDATE_EAMIL = HOST + "updateMerchantInfo";

    //
    public static String CHECKCRAWLERCODE = HOST + "crawler/checkCrawlerCode";
    // 发送解除爬虫账号封禁的验证码
    public static String LOGIN_CRAWLERCODE = HOST + "crawler/sendCrawlerCode";
    //发送验证码
    public static String PASSWORD_SMSAUTHCODE = HOST + "password/sendVerificationCode";
    public static String REGISTER_SMSAUTHCODE = HOST + "sendRegisterVerificationCode";
    public static String IMG_CODE = HOST + "getVerifCode";
    //通过验证码
    public static String PASSWORD_PASS = HOST + "/password/checkVerificationCode";

    //校验密码强度
    public static String PASSWORD_VERIFY = HOST + "modify/state";

    //确认修改密码
    public static String PASSWORD_AFFIRM = HOST + "/password/updatePassword";

    //签到初始化
    public static String SIGN_INIT = HOST + "/signCenter/initSignData";

    //签到
    public static String SIGN_IN = HOST + "/signCenter/signIn";

    //获取系统时间戳
    public static String GETCURRENTTIME = getCurrentTimeUrl();

    //检测版本更新
    public static String FIND_LATEST_VERSION = HOST + "version/findLatestVersion";

    // 检测版本更新v2
    public static String getLastestVersion = HOST + "terminalDeployManagement/getLatestTerminalDeploy";

    //检测补丁更新
    public static String FIND_LATEST_PATCH = HOST + "version/findPatchVersion";

    //获取没有领的优惠券
    public static String RECEIVE_VOUCHER = HOST + "voucher/receiveVoucher";

    //提交选中优惠券
    public static String CONFIRM_VOUCHER = HOST + "confirmVoucher";
    //提交选中优惠券
    public static String CONFIRM_VOUCHER_NEW = HOST + "order/v1/confirmVoucher";
    //提交选中优惠券 优惠券选择确认 来自代下单的
    public static String CONFIRM_VOUCHER_NEW_AGENT_ORDER = HOST + "order/sprout/v1/confirmVoucher";

    //删除优惠券
    public static String DEL_VOUCHER = HOST + "voucher/removeVoucher";

    //黑白换肤开关
    public static String GET_SKIN_PEELER_SWITCH = HOST + "layout/indexSubject";
    // 启用cms的开关接口
    public static String GET_LAYOUT_TYPE = HOST + "layout/getUsingAppLayoutModuleType";

    public static String GET_CART_LAYOUT_TYPE = HOST + "order/style";

    public static String GET_LAYOUT_TYPE_V2 = HOST + "layout/getUsingAppLayoutModuleType";

    //首页动态布局新接口
    public static String HOME_DYNAMIC_NEW = HOST + "layout/initAppLayoutModule?versionid=32";

    // 活动专区
    public static String HOME_ACTIVITYS = HOST + "layout/initAppLayoutModule/columnProduct";

    //首页动态布局
    public static String HOME_DYNAMIC = HOST + "layout/initModuleCategory?version=1";

    //首页动态布局第一次
    public static String HOME_DYNAMIC_FIRST = HOST + "layout/initModuleCategory?version=1&needShow=0";

    //天降大礼包
    //public static String HOME_FRAME_GET_FRAME = HOST + "layout/frame/getFrame";

    //控销动态布局
    public static String CONTROL_DYNAMIC = HOST + "layout/initControlMall";

    //获取商户优惠券信息
    public static String VOUCHER_FIND_PRODUCE = HOST + "voucher/findSkuInfo";

    //我的优惠券新接口
    public static String VOUCHER_FINDALLVOUCHER_WITH_SHOP = HOST + "voucher/findAllVoucherInfoWithShop";

    //优惠券获取商品列表
    public static String SELECT_INVITE_USER_VOUCHER = HOST + "invite/selectInviteUserVoucher";

    //获取商户可用优惠券 药帮忙的
    public static String VOUCHER_AVAILABLE_ALLVOUCHER = HOST + "voucher/findAvailableVoucherInfo";
    public static String VOUCHER_FINDVOUCHERINFO_V2 = HOST + "voucher/v2/findVoucherInfo";
    //获取商户可用店铺优惠券 店铺的
    public static String VOUCHER_AVAILABLE_ALLVOUCHER_FOR_SHOP = HOST + "order/v1/findShopAvailableVouchers";
    //获取商户可用店铺优惠券 查询可用/不可用优惠券 来自代下单
    public static String VOUCHER_AVAILABLE_ALLVOUCHER_FOR_SHOP_AGENT_ORDER = HOST + "order/sprout/v1/findShopAvailableVouchers";

    //获取采购单可用优惠券
    public static String SELECT_CART_VOUCHER = HOST + "cart/selectCartVoucher";
    //获取下单返券购物车小计和优惠提示信息
    public static String SELECT_CART_ACTIVITY_VOUCHER = HOST + "bill/collect";

    //商品信息查询接口
    public static String COMPANY_CENTER_COMPANYINFO_POPCOMPANYLIST = HOST + "company/center/companyInfo/popCompanyList";

    //消息中心
    public static String MSG_CENTER = HOST + "getMessagerList";

    //消息状态更新
    public static String MSG_CENTER_STATUS = HOST + "updateMessagerStatus";

    //首页配置文件
    public static String HOME_CONFIGN = HOST + "layout/findExtraInfo";

    //首页1010首页优惠大礼包
    public static String HOME_COUPON = HOST + "couponPackage/findVoucherBagTemplateList";

    //领取优惠大礼包
    public static String HOME_ADDCOUPON = HOST + "couponPackage/addCouponPackageecord";

    //会员大礼包
    public static String GIFTPACKAGE_FINDLATESTGIFTBAG = HOST + "giftpackage/findLatestGiftBag";

    //会员大礼包弹窗
    public static String GIFTPACKAGE_RECORDLATESTGIFTBAG = HOST + "giftpackage/recordLatestGiftBag";

    //首页-商户资质临期提醒
    public static String HOME_GETMERCHANTCREDENTIALSDEADLINEINFO = HOST + "getMerchantCredentialsDeadlineInfo";

    //首页对话框
    public static String HOME_DIALOG = HOST + "layout/getRedBagRainDialog";

    //首页大转盘
    public static String HOME_DIAL_INFO = HOST + "layout/frame/getBigWheelFrame";

    //消息未读数量
    public static String MSG_CENTER_UNREAD = HOST + "messageCenter/findUnReadCount";

    //获取搜索推荐列表(大搜入口 高毛搜索 保驾护航)
    public static String SUGGEST = HOST + "sku/findSkuNameList";
    //获取搜索推荐列表（大搜入口新接口）
//    public static String SUGGEST_NEW = HOST + "sku/findSkuNameList/oneBox";
    public static String SUGGEST_NEW = HOST + "search/v1/suggest";
    public static String SUGGEST_NEW_V2 = HOST + "search/v1/suggestV2";

    // 保存sug店铺点击历史
    public static String ADD_HISTORY_SUGGEST = HOST + "search/v1/history/add";


    //搜索关键词
    public static String FIND_HOT_SEARCH_LIST = HOST + "finHotSearchlist";
    //搜索关键词
    public static String START_WORD_LIST = HOST + "search/v1/startWordList";
    //清除历史搜索
    public static String DEL_HISTORY_HOT_SEARCH_LIST = HOST + "delhistoryHotSearchlist";
    //一个月搜索记录列表查询
    public static String SEE_MORE_HISTORY_RECORD = HOST + "finSearchRecordMorelist";
    // 一个月搜索记录删除接口
    public static String DEL_HISTORY_ONE_MONTHS = HOST + "delSearchRecordMoreKeyword";

    //极力推荐
    public static String FINDRECOMMENDEDPRODUCT = HOST + "sku/findRecommendedSku";

    //获取第三方厂家基本信息
    public static String GET_POPCOMPANY_DETAIL = HOST + "company/center/companyInfo/getPopCompanyDetail";

    //商品说明书
    public static String SKU_FINDSKUINSTRUCTION = HOST + "sku/findSkuInstruction";

    //获取系统时间
    public static String GETCURRENTTIMEFORSERVER = null;


    // 查看是否可发起退款
    public static String CANREFUND = HOST + "canRefund ";
    //申请退款
    public static String REFUND = HOST + "applyOrderRefund";
    //退款原因和取消订单原因 APP根据订单状态获取退款原因
    public static String REFUND_OR_CANCEL_ORDER_REASON = HOST + "findRefundReason";
    //退款列表
    public static String REFUNDLIST = HOST + "findRefundOrders";

    //取消退款单
    public static String CANCELREFUNDORDER = HOST + "cancelRefundOrder";

    //编辑收款账号信息
    public static String SAVE_ORUPDATE_ORDER_REFUND_BANK = HOST + "saveOrUpdateOrderRefundBank";

    //编辑收款账号信息
    public static String SAVE_OR_UPDATE_ORDER_REFUND_EXPRESS = HOST + "saveOrUpdateOrderRefundExpress";

    //计算退款金额
    public static String REFUND_MONEY = HOST + "getOrderRefundCensus";

    //退款详情
    public static String REFUND_DETAIL = HOST + "refundOrders/detail";

    //退款详情
    public static String REFUND_STATUS_DETAIL = HOST + "refundStatusDetail";

    //退款状态校验
    public static String FIND_REFUND_ORDER_STATUS = HOST + "findRefundOrderStatus";

    //退款商品列表
    public static String REFUND_DETAIL_LIST = HOST + "findRefundOrderDetails";

    public static String REFUND_CARD_INFO = HOST + "findLastOrderRefundBankByMerchantId";

    //地址选择
    public static String FIND_AREA = HOST + "area/findAreaByParentId";
    //行政区域地址查询新接口
    public static String FIND_AREA_NEW = HOST + "area/findAreaByParentIdNew";

    //控销列表
    public static String CONTROL_SALES = HOST + "controlSales/fetchData";

    //心原单列表
    public static String WISH_LIST = HOST + "wishOrder/findWishOrder";

    //添加心愿单
    public static String WISH_LIST_SAVE = HOST + "wishOrder/saveWishOrder";

    //服务条款
    public static String TERM_SERVICE = getStaticHost2Https() + "xyyvue/dist/#/termService?";
    //服务协议
//    public static String CLAUSE = getStaticHost2Https() + "xyyvue/dist/#/service?";
    //北京主体
    public static String CLAUSE = getStaticHost2Https() + "xyyvue/dist/#/agreement-Bj-1-********-zh-cn";
    // 《药帮忙账号注销须知》
    public static String UNREGISTER_PRICACE = getStaticHost2Https() + "xyyvue/dist/#/userLogout";
    //隐私协议
//    public static String PRIVACE = getStaticHost2Https() + "xyyvue/dist/#/privacy?";
//    public static String PRIVACE = "https://popapp.ybm100.com/#/ybmPrivacy";
    public static String PRIVACE = "https://popapp.ybm100.com/#/ybmPrivacyBj";

    //厂家
//    public static String FIND_MANUFACTURER = HOST + "sku/findManufacturerInfo";
    public static String FIND_MANUFACTURER = HOST + "search/v1/findManufacturerInfo";

    public static String FIND_MANUFACTURER_V2 = HOST + "search/v2/findManufacturerInfo";

    //专区搜索-厂商
    public static String FIND_MANUFACTURER_SECTION = HOST + "section/search/findManufacturerInfo";
    //订单凑单搜索
    public static String FIND_MANUFACTURER_ORDER_BUNDLING = HOST + "order/bundling/search/findManufacturerInfo";

    //售后规则
    public static String RULE = getStaticHost2Https() + "xyyvue/temp/afterTerm.html";
    //控价规则
    public static String RULE_PRICE_CONTROL = getStaticHost2Https() + "xyyvue/dist/#/koujiades?ybm_title=控价规则&merchantId=";
    //意见反馈
    public static String FEED_BACK = getStaticHost2Https() + "xyyvue/dist/#/feedback?ybm_title=意见反馈&head_menu=0&merchantId=";
    //协议管理
    public static String CONSULTATIVE_MANAGEMENT = getStaticHost2Https() + "xyyvue/dist/#/protocollist?ybm_title=协议管理&issign=1&head_menu=0&merchantId=";
    //被委托人信息确认
    public static String ATTORNEY_INFO_CONFIRM = getStaticHost2Https() + "xyyvue/dist/#/customConfirm?";
    //药学院
    public static String SHOP_PHARMACY = getStaticHost2Https() + "/xyyvue/dist/#/pharmacy?ybm_title=药学院&head_menu=0&merchantId=";
    //余额明细
    //public static String BALANCE_INFO = HOST + "balanceJournal/pageList.json";
    public static String BALANCE_INFO = HOST + "getBalanceJournalInfo";
    //余额说明
    public static String BALANCE_EXPLAIN = getStaticHost2Https() + "temp/coinReule.html";
    //系统权限
    public static String PERMISSION = getStaticHost2Https() + "xyyvue/dist/#/authority_list?ybm_title=系统权限";
    //余额提现
    public static String BANK_CHECK = HOST + "bank/check";
    //开通平安页面（有底部按钮）
    public static String SHOPPING_GOLD_USER_INFO = getStaticHost2HttpsNewStatic()+"#/pinganMerchant/introduce";
    //购物金明细
    public static String SHOPPING_GOLD_DETAIL_INFO =  getStaticHost2HttpsNewStatic()+"#/shoppingGold/useDirection?isShowCart=0";
    //开通平安页面（无底部按钮）
    public static String SHOPPING_GOLD_USER_INFO_FROM_MY = getStaticHost2HttpsNewStatic()+"#/pinganMerchant/introduce?from=my";
    //转到平安账户
    public static String OUT_TO_PINGAN_ACCOUNT = getStaticHost2HttpsNewStatic()+"#/shopMoney/buyMoneyOut?merchantId="+ com.ybmmarket20.utils.SpUtil.getMerchantid();


    public static String BILL_REULE = getStaticHost2Https() + "temp/billReule.html";
    //电子计划单说明
    public static String PLAN_EXPLAIN = getStaticHost2Https() + "temp/********.html";
    //添加计划单
    public static String PLAN_ADD_SCHEDULE = HOST + "planningSchedule/editPlanningSchedule";
    //获取计划单有货商品的分类
    public static String PLAN_SCHEDULE_CATEGORY = HOST + "planningSchedule/category";
    //增加选中商品
    public static String PLAN_SCHEDULE_CHECKED = HOST + "planningSchedule/checked";
    //电子计划单厂家
    public static String PLAN_SCHEDULE_MANUFACTURER = HOST + "planningScheduleProduct/manufacturer";
    //获取计划单列表
    public static String PLAN_SCHEDULE_LIST = HOST + "planningSchedule/planningScheduleList";
    //获取计划单图片采购单列表
    public static String PLANNINGSCHEDULE_PICTURE_LIST = HOST + "planningSchedule/picture/list";
    //获取当前商户某个图片采购单详情
    public static String PLANNINGSCHEDULE_PICTURE_DETAILS = HOST + "planningSchedule/picture/details";

    //删除计划单
    public static String PLAN_SCHEDULE_DELETE = HOST + "planningSchedule/deletePlanningSchedule";
    //分享计划单
    public static String PLAN_SCHEDULE_SHARE = HOST + "planningSchedule/export";
    //计划单详情
    public static String PLAN_SCHEDULE_DETAIL = HOST + "planningSchedule/planningScheduleProductList";
    //批量添加到购物车
    public static String PLAN_PRODUCT_TO_CART = HOST + "batchAddProductToCart";
    //加入成功修改计划单状态
    public static String UPDATE_PLAN_SCHEDULE_STATUS = HOST + "planningSchedule/updatePlanScheduleStatus";
    //删除计划单中的商品
    public static String PLAN_PRODUCT_DELETE = HOST + "/planningSchedule/deletePlanningScheduleProduct";

    //电子计划单查询药品
    public static String PLAN_SEARCHPRODUCT = HOST + "planningSchedule/searchProduct";

    //查询药品添加到计划单
    public static String ADD_PRODUCTTOPLAN = HOST + "planningSchedule/addProductToPlanningSchedule";

    //修改计划单商品数量
    public static String PLAN_PRODUCT_MODIFY = HOST + "planningSchedule/editPlanningScheduleProductNum";

    //补货登记搜索厂家
    public static String REPLENISH_MANUFACTURER = HOST + "planningSchedule/searchManufacturer";

    //补货登记搜索规格
    public static String REPLENISH_SPEC = HOST + "planningSchedule/searchSpec";

    //补货登记搜索药品
    public static String REPLENISH_PRODUCT_SEARCH = HOST + "planningSchedule/findProductForPlanningSchedule";

    //补货登记添加药品到计划单
    public static String REPLENISHMENT_ADD_PRODUCTTOPLAN = HOST + "/planningSchedule/addProductToPlanningScheduleByInput";
    //订单各个状态的数量
    public static String ORDER_STATUS_NUMER = HOST + "orders/findNumGroupByStatus";

    //  新开屏广告地址信息
    public static String AD_CONFIG_NEW = HOST + "AppStartPage/getCmsStartBootPage";

    //发票信息
    public static String ORDER_INVOICE = HOST + "orders/invoice";

    //发票集合
    public static String QUERY_INVOICE = HOST + "invoice/queryInvoice";

    //发送邮箱
    public static String QUERY_INVOICE_EMAIL = HOST + "orders/sendInvoiceEmail";

    //诊所分类
    public static String CLINIC_CATEGORY_API = HOST + "clinicArea/findClinicCategoryItem";

    //获取某商品相关的优惠券
    public static String MAY_DRAW_VOUCHER = HOST + "voucher/voucher";

    // 领取优惠券
    public static String RECEIVE_USABLE_VOUCHER = HOST + "voucher/receiveVoucher";

    //第三方厂家商品列表
    public static String FIND_THIRD_SKUINFO = HOST + "sku/findThirdSkuInfo";

    //评价标签获取
    public static String COMMENT_LABEL = HOST + "evaluate/lable";

    //评价详情获取
    public static String COMMENT_DETAIL = HOST + "evaluate/detail";

    //保存评论
    public static String COMMENT_SAVE = HOST + "evaluate/save";

    //im
    public static String GET_IM_PACKURL = HOST + "messageCenter/getIMPackUrl";

    //查看我的供应商列表
    public static String GET_MY_SUPPLIER_LIST = HOST + "supplier/supplierList";
    //查看我的供应商资质
    public static String GET_SUPPLIER_QUALIFICATION = HOST + "supplier/aptitude";
    // 获取自营店铺侧边栏楼层信息
    public static String SHOP_PROPRIETARY_FLOOR = HOST + "shop/shopIndex";
    // 获取自营或pop楼层的商品信息
    public static String SHOP_GETFLOORGOODS = HOST + "company/center/companyInfo/getFloorGoods";
    //  POP 店铺中优惠券信息
    public static String SHOP_COUPON_INFO = HOST + "coupon/getPopCouponList";
    public static String SHOP_COUPON_INFO_SELF = HOST + "coupon/getShopCoupon";

    //首页
    public static String SHOP_HOME_INDEX = HOST + "company/center/companyInfo/shopIndex";
    public static String SHOP_HOME_INDEX_V2 = HOST + "company/center/companyInfo/shopIndexV2";

    //获取店铺商品分类
    public static String SHOP_GOODS_CATEGORY = HOST + "company/center/companyInfo/getCategoryList";

    //获取所有药品，全部分类
    public static String GET_ALL_GOODS_CATEGORY = HOST + "app/category/getCategoryList";

    //店铺经营范围查询
    public static String GET_SHOP_CATEGORY = HOST + "company/center/companyInfo/getShopCategory";

    //发现--我的清单列表
    public static String FIND_MY_ORDER_LIST = HOST + "getOrderSellNo";

    //发现--联合用药-分类列表
    public static String FIND_ALLIANCE_CATEGORY = HOST + "sku/getAllDrugType";

    //发现--联合用药-左侧分类列表
    public static String FIND_ALLIANCE_CATEGORY_LEVEL2 = HOST + "sku/findDrugByDrugTypeId";

    //发现--联合用药-批量加入购物车
    public static String BATCH_ADD = HOST + "changeCartBatch";

    //发现--联合用药-分类下的商品列表
    public static String FIND_ALLIANCE_GOODS_LIST = HOST + "sku/findDrugByDrugId";

    //首页--为你推荐
    public static String RECOMMENDED_SKU_FOR_YOU_FRONT = HOST + "sku/recommendedSkuForFront";
    //ka首页--为你推荐
    public static String KA_RECOMMENDED_SKU_FOR_YOU_FRONT = HOST + "ka/search/recommendedSkuForFront";

    //订单--物流跟踪
    public static String GET_ORDER_DELIVERY_BY_ORDER_NO = HOST + "orderDelivery/getOrderDeliveryByOrderNo";

    //个人中心-专属销售
    public static String GET_SALES_INFO = HOST + "salesInfo";

    //账户指引
    public static String COMMONPROBLEM_ACCOUNT = getStaticHost2Https() + "xyyvue/temp/commonproblem-account.html?ybm_title=账户指引";

    //资质指引
    public static String COMMONPROBLEM_QUALIFICATION = getStaticHost2Https() + "xyyvue/temp/commonproblem-qualification.html?ybm_title=资质指引";

    //订单问题
    public static String COMMONPROBLEM_ORDER = getStaticHost2Https() + "xyyvue/temp/commonproblem-order.html?ybm_title=订单问题";

    //售后问题
    public static String COMMONPROBLEM_AFTERMARKET = getStaticHost2Https() + "xyyvue/temp/commonproblem-aftermarket.html?ybm_title=售后问题";

    //其他问题
    public static String COMMONPROBLEM_OTHER = getStaticHost2Https() + "xyyvue/temp/commonproblem-other.html?ybm_title=其他问题";

    //三合一协议
    public static String THREE_MERGE_ONE = getStaticHost2Https() + "xyyvue/dist/#/agreementzone?ybm_title=签约特供&issign=1&head_menu=0";

    //获取物流信息
    public static String GET_POST_MAIL_INFO = HOST + "getPostMailInfo";

    //获取物流方式
    public static String GET_LOGISTICS_WAY = HOST + "getLogisticsInfo";

    //保存物流信息
    public static String ADD_POST_MAIL_INFO = HOST + "addPostMailInfo";

    //申请保价订单列表
    public static String FIND_ORDERS_ESCORT = HOST + "findOrdersEscort";

    //保驾护航申请记录
    public static String ESCORT_INDEX = HOST + "escort/index";

    //申请记录详情
    public static String ESCORT_ORDER_ESCORT_INFO = HOST + "escort/orderEscortInfo";

    //申请报价商品列表
    public static String FIND_ORDERS_ESCORT_DETAIL = HOST + "findOrdersEscortDetail";

    //选择申请订单搜索
    public static String ESCORT_GET_ORDER_DETAIL_ESCORT = HOST + "escort/getOrderDetailEscort";

    //申请保驾护航保存
    public static String ESCORT_SAVE_ORDER_ESCORT = HOST + "escort/saveOrderEscort";

    //删除单个搜索记录
    public static String DEL_HISTORY_SEARCH_RECORD = HOST + "delHistorySearchRecord";

    //邀请
    public static String THE_INVITATION = HOST + "invite/getMyAwardsView";

    //被邀请人列表
    public static String BONUS_POOLS = HOST + "invite/getInviteeUserLs";

    //短信邀请好友
    public static String SMS_INVITATION = HOST + "invite/sendMessage";

    //生成海报
    public static String SELECT_INVITATION = HOST + "invite/selectInvitationInfo";

    //邀请有礼活动规则
    public static String THE_RULES = getStaticHost2Https() + "xyyvue/dist/#/weixinrule?ybm_title=邀请有礼活动规则&head_menu=0";

    //分享
    public static String THE_INVITATION_SHARE = getStaticHost2Https() + "xyyvue/dist/#/weixininvitation?ybm_title=邀请函&merchantId=";

    //邀请新用户获取验证码
    public static String GET_VERIF_CODE = HOST + "invite/getVerifCode";
    //商品纠错
    public static String GOODS_MISTAKE_CORRECTION = HOST + "errorCollection/save";

    //代下单授权列表
    public static String AGENT_ORDER_AUTHORIZATION_LIST = HOST + "authorize/list";

    //授权红点
    public static String AGENT_ORDER_AUTHORIZATION_DOT_COUNT = HOST + "authorize/red/count";

    //代下单授权详情
    public static String AGENT_ORDER_AUTHORIZATION_Detail = HOST + "authorize/detail";

    //同意代下单授权
    public static String AGENT_ORDER_AUTHORIZATION_AGREE = HOST + "authorize/apply";

    //代下单专区列表
    public static String AGENT_ORDER_LIST = HOST + "sprout/order/list";

    //代下单订单详情
    public static String AGENT_ORDER_DETAIL = HOST + "sprout/order/detail";

    //代下单确认订单
    public static String AGENT_ORDER_CONFIRM_ORDER = HOST + "sprout/order/orderConfirm";

    //代下单驳回
    public static String AGENT_ORDER_REJECT_ORDER = HOST + "sprout/order/orderRefuse";

    //优惠券-去使用-获取分类
    public static String COUPON_TO_USE_GET_CATEGORY = HOST + "voucher/searchSkuCategoryForVoucher";
    //优惠券-去使用-获取分类V2
    public static String COUPON_TO_USE_GET_CATEGORY_V2 = HOST + "voucher/crossStoreSkuCategory";

    //优惠券 新的 查询用户已领取的券中 根据券模板id查询对应的券的信息接口
    public static String GET_COUPON_TEMPLATE_BY_INFO = HOST + "voucher/getMyVoucherListByTemplateInfo";

    //优惠券-商品查询接口
    public static String GET_COUPON_GOODS = HOST + "voucher/searchSkuInfoForVoucher";
    public static String GET_COUPON_GOODS_V2 = HOST + "voucher/searchSkuInfoForVoucher/v2";

    public static String GET_COUPON_GOODS_V3 = HOST + "voucher/crossStoreSkuInfo";

    //小药药资质查看获取合同列表接口
    public static String GET_APTITUDE_XYY_LIST = HOST + "new/license/list";
    //小药药资质下载到邮箱接口(自营)
    public static String APTITUDE_XYY_DOWN_TO_EMAIL = HOST + "new/license/email/down";
    //小药药资质下载到邮箱接口(POP)
    public static String APTITUDE_XYY_DOWN_TO_EMAIL_POP = HOST + "company/center/companyInfo/licenseSend";
    //获取pdf合同的地址
    public static String APTITUDE_XYY_PDF_DOWN_URL = HOST + "new/license/down";
    //pop 获取资质下载地址
    public static String APTITUDE_XYY_ZIP_POP_DOWN_URL = HOST + "company/center/companyInfo/licenseDown";
    //获取全部客户类型
    public static String GET_CUSTOMER_TYPE_ALL = HOST + "merchantCustomerType/getAll";

    //我的金融白条审批列表
    public static String MY_BANKING_BAI_TIAO_LIST = HOST + "finance/gfbt/getIousApprovalProgress";
    //我的金融贷款申请心愿单
    public static String MY_BANKING_WISH_LIST = HOST + "loanwish/add";
    //小药条简介
    public static String MY_BANKING_XYBT_SYNOPSI = getStaticHost2Https() + "xyyvue/dist/#/xysynopsis?ybm_title=小药白条简介&head_menu=0";
    //小药条常见问题
    public static String MY_BANKING_XYBT_FAQ = getStaticHost2Https() + "xyyvue/dist/#/faq?ybm_title=常见问题&head_menu=0";
    //小药条审核状态
    public static String MY_BANKING_XYBT_XYWB = getStaticHost2Https() + "xyyvue/dist/#/xywb?ybm_title=小药白条&head_menu=0";
    //小药条 授权委托与承诺书
    public static String MY_BANKING_XYBT_EMPOWER = getStaticHost2Https() + "xyyvue/dist/#/empower?ybm_title=授权委托与承诺书&head_menu=0";

    //控销商城分享-拼接地址
    public static String GET_SHARE_URL_BY_IMAGE = CDN_HOST + "/ybm/applayoutbanner/";

    //埋点sdk-上传埋点数据
    public static String XYYIOSDK_UPLOAD_TRACK = "https://msg.api.ybm100.com/snow";
    //埋点sdk-上传设备信息
    public static String XYYIOSDK_UPLOAD_DEVICEINFO = "http://app-v4.ybm100.com/app/snowground/deviceinfo";

    //查询白条审批状态
    public static String FINANCE_GFBT_GETIOUSINFO = HOST + "finance/gfbt/getIousInfo";

    //查询白条交易明细
    public static String FINANCE_GFBT_GETIOUSTRANSACTIONDETAILS = HOST + "finance/gfbt/getIousTransactionDetails";

    //查询白条利息补贴
    public static String FINANCE_GFBT_GETIOUSINTERESTSUBSIDY = HOST + "finance/gfbt/getIousInterestSubsidy";

    //拼团-预结算
    public static String ORDER_V1_GROUPPURCHASEPRESETTLE = HOST + "order/v1/groupPurchasePreSettle";

    //拼团-去结算
    public static String ORDER_V1_GROUPPURCHASESETTLE = HOST + "order/v1/groupPurchaseSettle";

    // 拼团-提交订单
    public static String ORDER_V1_GROUPPURCHASEORDERCONFIRM = HOST + "order/v1/groupPurchaseOrderConfirm";

    // 拼团-加购商品
    public static String CHANGECARTFORPROMOTION = HOST + "changeCartForPromotion";

    // 获取埋点sId
    public static String FLOW_DATA_ANALYSIS_GET_SID = HOST + "sku/getSid";
    // 获取直播的券信息列表
    public static String TV_LIVE_LIST_COUPONS = HOST + "webcast/live/listCoupons";
    // 获取直播的商品信息列表
    public static String TV_LIVE_LIST_PRODUCTS = HOST + "webcast/live/listProducts";
    // 获取直播间商品所能领取的所有优惠券
    public static String TV_LIVE_LIST_OBTAIN_COUPONS = HOST + "webcast/live/receiveMultipleCoupons";
    // 获取直播间信息
    public static String GET_WEBCASTINFO_BY_LIVEID = HOST + "webcast/live/getWebcastInfoByLiveId";


    //轮训拉取新消息
    public static String TV_LIVE_PULL_NEW_MSG = HOST + "webcast/live/pullNewMessage";
    //直播领券
    public static String TV_LIVE_GET_COUPON = HOST + "webcast/live/receiveCoupon";

    // 获取直播间信息
    public static String GET_LIVE_INFO = HOST + "webcast/getLiveInfo";

    //首页为你推荐
    public static String HOME_RECOMMENDED_SKU = HOST + "search/recommendedSku";
    //首页布局
    public static String HOME_INDEX_DATA = HOST + "layout/getIndexData";

    public static String HOME_INDEX_DATA_V2 = HOST + "layout/getIndexData/V2";
    //专题页tab
    public static String HOME_SUBJECT_TABS = HOST + "search/project/basicInfo";
    //ka专题页tab
    public static String KA_HOME_SUBJECT_TABS = HOST + "ka/search/project/basicInfo";
    // 精选补货必买
    public static String SEARCH_RECOMMEND_BUY = HOST + "recommend/v1/better/cprf/buy";
    //专题页列表
    public static String HOME_SUBJECT_LIST = HOST + "search/project/recommendedSku";
    //ka专题页列表
    public static String KA_HOME_SUBJECT_LIST = HOST + "ka/search/project/recommendedSku";
    //频道页列表
    public static String HOME_CHANNEL_LIST = HOST + "search/channel/recommendedSku";
    //频道页头部信息
    public static String HOME_CHANNEL_HEADER = HOST + "search/channel/basicInfo";
    //首页常购清单
    public static String HOME_HOME_OFTEN_BUY_LIST = HOST + "search/oftenBuy/sku";

    // 发现页常够清单
    public static String HOME_OFTEN_BUY_LIST = HOST + "recommend/v1/oftenBuy";
    //ka首页常购清单
//    public static String KA_HOME_HOME_OFTEN_BUY_LIST = HOST + "ka/search/oftenBuy/sku";
    public static String KA_HOME_HOME_OFTEN_BUY_LIST = HOST + "ka/search/oftenBuy/productList";
    //H5文件上传
    public static String TASK_CENTER_UPLOAD_FILE = HOST + "upload/file";
    // 查看CRM推荐清单（默认当天，可指定日期）
    public static String CRM_RECOMMEND_LIST = HOST + "sku/selectCrmRecommendList";
    // 查看CRM历史推荐清单
    public static String CRM_RECOMMEND_RECORD_LIST = HOST + "sku/selectCrmRecommendRecordList";
    // 查询豆芽历史详情
    public static String CRM_RECOMMEND_RECORD_DETAIL_LIST = HOST + "sku/selectCrmRecommendRecordDetailList";
    //获取运费提示内容
    public static String FREIGHT_DIALOG = HOST + "freight/query";
    //运费超重商品列表
    public static String FREIGHT_OVER_WEIGHT_GOODS_LIST = HOST + "freight/queryFreightBlacklist";
    //运费凑单包邮列表
    public static String FREIGHT_ADD_ON_ITEM_GOODS_LIST = HOST + "freight/queryFreightProductForFreeShipping";
    //运费凑单页加购商品查询小计金额、运费（购物车页面过来的）
    public static String FREIGHT_CART_ADD_ON_ITEM_AMOUNT_SUBTOTAL = HOST + "freight/cart/queryFreightProductForFreeShippingSubtotal";
    //运费凑单页加购商品查询小计金额、运费(待确认订单页面过来的)
    public static String FREIGHT_SETTLE_ADD_ON_ITEM_AMOUNT_SUBTOTAL = HOST + "freight/settle/queryFreightProductForFreeShippingSubtotal";
    //注册pushtoken接口
    public static String PUSH_REGISTER_TOKEN = HOST + "registerPushtoken";
    //pushtoken绑定用户
    public static String PUSH_BIND_TOKEN = HOST + "pushTokenBindMember";
    //在线客服未读消息数量
    public static String ONLINE_SERVICE_INFO_COUNT = HOST + "messageCenter/queryMessageCount";
    //更新客服维度消息为已读状态
    public static String ONLINE_SERVICE_INFO_STATUS_UPDATE = HOST + "messageCenter/updateStatusByMerchantId";
    //他人代付获取分享内容
    public static String PAY_FOR_ANOTHER_GET_SHARE_URL = HOST + "payment/getShareUrl";
    //他人代付获取分享内容
    public static String PAY_FOR_ANOTHER_GET_SHARE_URL_LIST = HOST + "payment/getShareUrlList";
    //首页弹窗详情
    public static String DIALOG_DETAIL = HOST + "layout/dialogDetail";
    //个人中心展位
    public static String MINE_BOOTH = HOST + "layout/personalCenter/exhibitionPosition";
    // 展位 CMS场景类型。1首页，3个人中心，4排行榜，6支付结果页
    public static String EXHIBITIONPOSITION = HOST + "layout/exhibitionPosition";
    // 补货提醒推荐
    public static String PUSH_RECOMMEND = HOST + "recommend/v1/push/page";

    // 获取自营店铺的店铺信息
    public static String SHOP_INFO = HOST + "shop/shopBaseInfo";

    //首页feed流
    public static String HOME_FEED_LIST = HOST + "search/recommendedSkuByTab";

    //首页feed流带店铺
    public static String HOME_FEED_LIST_WITH_SHOP = HOST + "search/recommendedSkuByTabWithShopInfo";

    // 商品详情分享内容
    public static String GOODS_DETAIL_SPELL_GROUP_CONTENT = HOST + "share/getShareContent";

    //自营店铺资质
    public static String SHOP_SELF_LICENSE = "shop/license";

    //获取商品的优惠信息
    public static String PROM_INFO_URL = HOST + "promotion/csuDetail/promoInfoForAct";

    public static String USER_CALL = HOST + "layout/getUserRecallGuideInfo";

    //包含path
    private static String getApiUrl() {
        return getApiHost() + getApiPath();
    }

    //获取平安进件url
    private static String getPingAnPrePayUrl() {
        return selectApiPingAnUrl(getApiHost());
    }

    public static String getPingAnPayUrl() {
        return selectApiPingPayAnUrl(getApiHost());
    }

    public static String getStaticHost2Https() {
        if (STATIC_HOST_HTTPS_URL == null) {
            String host = SpUtil.readString(API_STATIC_HTTPS_KEY, "");
            if (!isHost(host)) {
                if ("debug".equals(BuildConfig.BUILD_TYPE)) {
                    if (API_HOST_STAGE.equals(SpUtil.readString(API_HOST_KEY, ""))) {
                        host = STATIC_HOST_STAGE_HTTPS;
                    } else if (API_HOST_DEV.equals(SpUtil.readString(API_HOST_KEY, ""))) {
                        host = STATIC_HOST_DEV_HTTPS;
                    } else {
                        host = STATIC_HOST_TEST_HTTPS;
                    }
                } else {
                    host = STATIC_HOST_RELEASE_HTTPS;
                }
            }
            STATIC_HOST_HTTPS_URL = host;
        }
        return STATIC_HOST_HTTPS_URL;
    }


    public static String getStaticHost2HttpsNewStatic() {
        if (STATIC_HOST_HTTPS_URL_NEW_STATIC == null) {
            String host = SpUtil.readString(API_STATIC_HTTPS_KEY, "");
            host = host.replace("static", "newstatic");
            if (!isHost(host)) {
                if ("debug".equals(BuildConfig.BUILD_TYPE)) {
                    if (API_HOST_STAGE.equals(SpUtil.readString(API_HOST_KEY, ""))) {
                        host = STATIC_HOST_STAGE_HTTPS_NEW_STATIC;
                    } else if (API_HOST_DEV.equals(SpUtil.readString(API_HOST_KEY, ""))) {
                        host = STATIC_HOST_DEV_HTTPS_NEW_STATIC;
                    } else {
                        host = STATIC_HOST_TEST_HTTPS_NEW_STATIC;
                    }
                } else {
                    host = STATIC_HOST_RELEASE_HTTPS_NEW_STATIC;
                }
            }
            STATIC_HOST_HTTPS_URL_NEW_STATIC = host;
        }
        return STATIC_HOST_HTTPS_URL_NEW_STATIC;
    }

    //返回没有路径的API 就是域名
    public static String getApiHost() {
        if (HOST_URL == null) {
            String host = SpUtil.readString(API_HOST_KEY, "");
            if (!isHost(host)) {
                if ("debug".equals(BuildConfig.BUILD_TYPE)) {
                    host = API_HOST_TEST;
                } else {
                    host = API_HOST_RELEASE;
                }
            }
            HOST_URL = host;
        }
        return HOST_URL;
    }

    //返回没有路径的API 就是域名
    public static String getApiPath() {
        if (API_PATH == null) {
            String path = SpUtil.readString(API_PATH_KEY, "");
            if (TextUtils.isEmpty(path) || path.startsWith("/")) {
                if ("debug".equals(BuildConfig.BUILD_TYPE)) {
                    path = API_PATH_TEST;
                } else {
                    path = API_PATH_RELEASE;
                }
            }
            API_PATH = path;
        }
        return API_PATH;
    }

    public static String getCDNHost() {
        if (CDN_URL == null) {
            String cdn_host = SpUtil.readString(API_CDN_KEY, "");
            if (!isHost(cdn_host)) {//检查域名
                if ("debug".equals(BuildConfig.BUILD_TYPE)) {
                    cdn_host = CDN_HOST_TEST;
                } else {
                    cdn_host = CDN_HOST_RELEASE;
                }
            }
            CDN_URL = cdn_host;
        }
        return CDN_URL;
    }

    private static boolean isHost(String host) {
        if (TextUtils.isEmpty(host)) {
            return false;
        }
        Uri uri = Uri.parse(host);
        String scheme = uri.getScheme();
        if (TextUtils.isEmpty(scheme) || !scheme.startsWith("http")) {
            return false;
        }
        if (TextUtils.isEmpty(uri.getHost())) {
            return false;
        }
        return true;
    }

    private static String getCurrentTimeUrl() {
        if (GETCURRENTTIMEFORSERVER == null) {
            GETCURRENTTIMEFORSERVER = getApiHost() + "getCurrentTimeForServer";
        }
        return GETCURRENTTIMEFORSERVER;
    }

    //设置host 不带path
    public static void setApiHost(String host) {
        if (isHost(host)) {
            SpUtil.writeString(API_HOST_KEY, host);
        }
    }

    public static String selectApiPingAnUrl(String host) {
        if (TextUtils.equals(host, API_HOST_RELEASE) || TextUtils.equals(host, API_HOST_NEW_STAGE)) {
            return PING_AN_PRE_PAY_RELEASE;
        } else {
            return PING_AN_PRE_PAY;
        }
    }

    public static String selectApiPingPayAnUrl(String host) {
        if (TextUtils.equals(host, API_HOST_RELEASE) || TextUtils.equals(host, API_HOST_NEW_STAGE)) {
            return PING_AN_PAY_RELEASE;
        } else {
            return PING_AN_PAY;
        }
    }

    public static void setApiPingAnUrl(String url) {
        if (isHost(url)) {
            SpUtil.writeString(API_PING_AN_URL, url);
        }
    }

    public static void setApiPingAnPayUrl(String url) {
        if (isHost(url)) {
            SpUtil.writeString(API_PING_AN_PAY_URL, url);
        }
    }

    //设置 path
    public static void setApiPath(String path) {
        if (TextUtils.isEmpty(path) || path.startsWith("/")) {
            return;
        } else {
            SpUtil.writeString(API_PATH_KEY, path);
        }
    }

    //设置图片host
    public static void setCdnHost(String cdnHost) {
        if (isHost(cdnHost)) {
            SpUtil.writeString(API_CDN_KEY, cdnHost);
        }
    }

    public static void setStaticHost(String staticHost) {
        if (isHost(staticHost)) {
            SpUtil.writeString(API_STATIC_HTTPS_KEY, staticHost);
        }
    }


    public static void cleanIp() {
        SpUtil.writeString(API_CDN_KEY, "");
        SpUtil.writeString(API_HOST_KEY, "");
        SpUtil.writeString(API_PATH_KEY, "");
        SpUtil.writeString(API_STATIC_KEY, "");
    }
}

