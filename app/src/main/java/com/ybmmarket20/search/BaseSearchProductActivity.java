package com.ybmmarket20.search;


import static com.ybmmarket20.bean.SearchRowsBeanKt.SEARCH_LIST_CARD_TYPE_COMBINATIONBUY_MULTI;
import static com.ybmmarket20.bean.SearchRowsBeanKt.SEARCH_LIST_CARD_TYPE_COMBINATIONBUY_SINGLE;
import static com.ybmmarket20.bean.SearchRowsBeanKt.SEARCH_LIST_CARD_TYPE_GOODS;
import static com.ybmmarket20.bean.SearchRowsBeanKt.SEARCH_LIST_CARD_TYPE_OPERATION_POSITION;
import static com.ybmmarket20.bean.SearchRowsBeanKt.SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS;
import static com.ybmmarket20.common.AppUtilKt.getFullClassName;
import static com.ybmmarket20.common.JGTrackTopLevelKt.replacePartJgspid;
import static com.ybmmarket20.common.JGTrackTopLevelKt.splicingUrlWithParams;
import static com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt.flowDataPageListPageExposureNew;
import static com.ybmmarket20.view.searchFilter.view.SearchDynamicLabelViewKt.DYNAMIC_LABEL_STYLE_GRID;
import static com.ybmmarket20.view.searchFilter.view.SearchDynamicLabelViewKt.DYNAMIC_LABEL_STYLE_HORIZONTAL_LINEAR;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.InputType;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.LinkMovementMethod;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.view.animation.AnimationSet;
import android.view.animation.TranslateAnimation;
import android.view.inputmethod.EditorInfo;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.github.mzule.activityrouter.router.Routers;
import com.google.android.material.appbar.AppBarLayout;
import com.google.gson.Gson;
import com.luck.picture.lib.tools.StringUtils;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.WrapGridLayoutManager;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.SearchVoiceActivity;
import com.ybmmarket20.adapter.SearchAdapter;
import com.ybmmarket20.adapter.YBMBaseListAdapter;
import com.ybmmarket20.bean.AggregationsBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.BaseSearchResultBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.GroupPurchaseInfoKt;
import com.ybmmarket20.bean.HistoryKeyWord;
import com.ybmmarket20.bean.ManufacturersBean;
import com.ybmmarket20.bean.RecommendKeyWord;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.SearchAggsBean;
import com.ybmmarket20.bean.SearchDynamicLabelConfig;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.bean.SearchResultBean;
import com.ybmmarket20.bean.SearchResultOPBean;
import com.ybmmarket20.bean.SearchRowsBean;
import com.ybmmarket20.bean.StartWord;
import com.ybmmarket20.bean.SuggestListItemLabel;
import com.ybmmarket20.bean.SuggestSearchConditions;
import com.ybmmarket20.bean.SuggestShopBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.eventbus.C;
import com.ybmmarket20.common.eventbus.Event;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.home.newpage.bean.HotStyleBean;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.reportBean.AppActionSearchFilterClick;
import com.ybmmarket20.reportBean.JGPageListCommonBean;
import com.ybmmarket20.reportBean.PageListBuild;
import com.ybmmarket20.utils.AdapterUtils;
import com.ybmmarket20.utils.RoundBackgroundColorSpan;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.AllProductPopWindowV2;
import com.ybmmarket20.view.BaseFilterPopWindow;
import com.ybmmarket20.view.FiltrateClassifyPop2;
import com.ybmmarket20.view.LeftPopWindow;
import com.ybmmarket20.view.Manufacturers2Pop;
import com.ybmmarket20.view.MyFastScrollViewKt;
import com.ybmmarket20.view.SearchCategorySelectLevelTrack;
import com.ybmmarket20.view.SearchFindView;
import com.ybmmarket20.view.cms.SearchStartRecommendView;
import com.ybmmarket20.view.searchFilter.view.SearchDynamicLabelView;
import com.ybmmarket20.view.taggroupview.TagContainerLayout;
import com.ybmmarket20.view.taggroupview.TagView;
import com.ybmmarket20.view.SearchDeliveryGuideView;
import com.ybmmarket20.utils.SearchGuideManager;
import com.ybmmarket20.viewmodel.SearchDataViewModel;
import com.ybmmarket20.widget.AlphaBackgroundSpan;
import com.ybmmarketkotlin.adapter.GoodListAdapterNew;
import com.ybmmarketkotlin.utils.ExposureCallback;
import com.ybmmarketkotlin.utils.ExposureManager;
import com.ybmmarketkotlin.views.FlexBoxLayoutMaxLines;
import com.zxing.activity.CaptureActivity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 商品搜索
 */

public abstract class BaseSearchProductActivity extends BaseSearchProductAnalysisActivity {

    @Bind(R.id.titleEtMask)
    View titleEtMask;
    @Bind(R.id.title_left_search)
    ImageView titleLeft;
    @Bind(R.id.title_et)
    EditText titleEt;
    @Bind(R.id.rel_search)
    RelativeLayout relSearch;
    @Bind(R.id.cl_before_search_result)
    CoordinatorLayout clBeforeSearchResult;
    @Bind(R.id.ll_history)
    LinearLayout llHistory;
    @Bind(R.id.flex_box_history)
    FlexBoxLayoutMaxLines flexBoxHistory;
    @Bind(R.id.search_product_list_view)
    RecyclerView searchProductListView;
    @Bind(R.id.iv_clear)
    ImageView ivClear;
    @Bind(R.id.iv_cart)
    ImageView ivCart;
    @Bind(R.id.tv_num)
    TextView tvNum;
    @Bind(R.id.rl_cart)
    RelativeLayout rlCart;
    @Bind(R.id.iv_fast_scroll_search)
    MyFastScrollViewKt mIvFastScrollSearch;

    @Bind(R.id.brand_rg_01)
    RadioGroup mBrandRg01;
    // KA: 默认 + 分类 + 最新 + 厂家 + 筛选;
    // 非KA：综合 +  分类 + 规格 + 商家 + 筛选；
    @Bind(R.id.tv_synthesize)
    TextView tvSynthesize;              // 综合（位置1）
    @Bind(R.id.tv_default)
    TextView tvDefault;                 // 默认（位置1 KA）
    @Bind(R.id.rb_all_category)
    TextView mRbAllCategory;            // 分类（位置2）
    @Bind(R.id.tv_specification)
    TextView tv_specification;          // 规格（位置3）
    @Bind(R.id.rb_brand_rg_01_new)
    TextView rbBrandRg01New;            // 最新（位置3 KA）
    @Bind(R.id.tv_manufacturer)
    TextView mTvManufacturer;            // 厂家（位置4 KA）
    @Bind(R.id.tv_shopStore)
    TextView tvShopStore;               // 商家（位置4）
    @Bind(R.id.tv_shop)
    TextView mTvShop;                   // 筛选(位置5)

    @Bind(R.id.brand_rg_02)
    RadioGroup mBrandRg02;
    @Bind(R.id.rb_available)
    TextView mRbAvailable;
    @Bind(R.id.rb_dpby)
    TextView mRbDpby;
    @Bind(R.id.rb_can_use_coupon)
    TextView mRbCanUseCoupon;
    @Bind(R.id.rb_promotion)
    TextView mRbPromotion;
    @Bind(R.id.appbar)
    AppBarLayout appbar;
    @Bind(R.id.rl_root)
    RelativeLayout rl_root;
    @Bind(R.id.search_guid_layout_by_layout)
    LinearLayout search_guid_layout_by_layout;
    @Bind(R.id.rb_self_support)
    TextView mRbSelfSupport;
    @Bind(R.id.rb_Gross)
    TextView rbGross;
    @Bind(R.id.ll_hotkey)
    LinearLayout llHotKey;
    @Bind(R.id.tv_hightlight_keyword)
    TextView tvHightLightKeyword;
    @Bind(R.id.crv_hot_keyword)
    TagContainerLayout crvHotKeyword;
    @Bind(R.id.tv_hot_keyword_list_head)
    TextView tv_hot_keyword_list_head;
    @Bind(R.id.cl_recommend_header)
    ConstraintLayout clRecommentHeader;
    @Bind(R.id.tv_tag_tips)
    TextView tvTagTips;//战略专区搜索提示
    @Bind(R.id.layout_tag_no_more)
    LinearLayout llTagNoMore;//战略专区无结果显示
    @Bind(R.id.tv_tag_search_no_more)
    TextView tvTagSearchNoMore;//[专区]内没有更多了
    @Bind(R.id.cb_activity_tag)
    CheckBox cbActivityTag;// 319 活动筛选

    @Bind(R.id.rv_recommed)
    RecyclerView rv_recommed;
    @Bind(R.id.rb_express)
    TextView rbExpress;
    @Bind(R.id.rb_spell_group)
    TextView rbSpellGroup;
    @Bind(R.id.rb_chinese_medicine)
    TextView rbChineseMedicine;
    @Bind(R.id.rb_same_province)
    TextView rbSameProvince;
    @Bind(R.id.rb_spell_group_and_pgby)
    TextView rbSpellGroupAndPgby;
    @Bind(R.id.rl_title_from_often)
    RelativeLayout rl_title_from_often;
    @Bind(R.id.ll_title)
    LinearLayout ll_title;
    @Bind(R.id.tv_title)
    TextView tv_title;
    @Bind(R.id.iv_back)
    ImageView ivBack;
    @Bind(R.id.llFromOftenBuy)
    LinearLayout llFromOftenBuy;
    @Bind(R.id.searchFindView)
    SearchFindView searchFindView;
    @Bind(R.id.search_start_recommend_view)
    SearchStartRecommendView searchStartRecommendView;
    @Bind(R.id.dynamicLabel)
    SearchDynamicLabelView dynamicLabelView;

    // 配送时效引导组件
    private SearchDeliveryGuideView deliveryGuideView;

    //主搜（支持运营位）
    public static int PAGE_TYPE_OP = 1;
    //搜索
    public static int PAGE_TYPE_NORMAL = 2;

    protected List<RowsBean> comments = new ArrayList<>();

    private List<String> lastNames = new ArrayList<>();
    protected YBMBaseListAdapter detailAdapter;
    private View tagFootView;
    private SuggestNewPopWindowNewV2 suggestPopWindow;       // Sug的推荐弹框
    View view;
    protected String suggestStr = "";
    private boolean isSuggest = true;
    protected String keyword = "";    //关键词过来的
    private String mContent = "";  //带内容过来，但是和关键词不同 这个是配合searchJumpLink使用的 和上面区分出来
    private String hotLabel = "";  //热搜标签
    private String searchJumpLink = ""; //带跳转链接  点击搜索跳转对应的url
    private String couponId = ""; //带跳转链接  点击搜索跳转对应的url  券id
    protected String preKeyWord = null;
    private String show = "";
    private String voice = "";          //语音搜索过来的
    private String scanCode = "";       //二维码扫描搜索过来的
    protected String id = "";             //商品分类过来的
    private String categoryName = "";
    protected String manufacturer = "";//厂家
    protected String shopCodesFromOut = "";
    protected String isExcludePt = "";    // 是否排除拼团: 0-否，1-是
    protected String drugClassification = ""; //经营范围
    protected String drugClassificationName = ""; //经营范围内容
    protected String tagList;                 //搜索参数  商品标签列表(多个标签用逗号分隔) 例：pg_yx商品组优选商品
    private String title;                   //专区搜索title 战略专区
    private boolean isTagSearch;            //是否专区搜索
    protected String spFrom = "1";                  //搜索类型 1-大搜；2-店铺内搜索；3-专区搜索；

    //排序
    public static final String PROPERTY_SYNTHESIZE = "smsr.sale_num";       // 综合
    public static final String PROPERTY_SALESVOLUME = "spa.sale_num";       // 销量
    public static final String PROPERTY_ACTPT = "actPt";                    // 拼团综合排序
    public static final String PROPERTY_PRICE = "fob";                      // 价格
    public static final String DIRECTION_ASC = "asc";                       // 升序
    public static final String DIRECTION_DESC = "desc";                     // 降序

    public static final String PROPERTY_FILTER_DEFAULT_V2 = "1"; //默认
    public static final String PROPERTY_FILTER_SALE_VOLUME_V2 = "2";//销量由高到低
    public static final String PROPERTY_FILTER_PRICE_V2 = "3";//价格由低到高

    protected String property = "smsr.sale_num", propertyDescOrAsc = "desc", propertyName = "";

    //商家过滤条件下拉框
    public static final int MANUFACTURER_TYPE_POPUP_WINDOW = 0;
    //商家过滤条件过滤框
    public static final int MANUFACTURER_TYPE_FILTER = 1;

    //选中的规格sug词
    public String selectedSugSpec = null;
    public String selectedSugCategory = null;


    private String lastFilter = "";//上次搜索参数
    private List<RecommendKeyWord> recommend = new ArrayList<>();//推荐搜索
    private List<HistoryKeyWord> history = new ArrayList<>();//历史搜索
    protected FiltrateClassifyPop2 mClassifyPop2;
    private Manufacturers2Pop mPopManufacturer;
    private AllProductPopWindowV2 mPopWindowProduct;
    protected int style = 1;
    private boolean selectId;

    //有货
    protected boolean isAvailable = false;
    //只看中药
    protected boolean isTraditionalChineseMedicine = false;
    //同省
    protected boolean isSameProvince = false;
    //服务字段 从筛选的Popwindow带过来
    protected String jgFuwu = "";

    protected boolean isDpby = false;
    //促销
    protected boolean isPromotion = false;
    protected String priceRangeFloor = "";
    protected String priceRangeTop = "";
    private boolean isClassA = false;
    private boolean isClassB = false;
    private boolean isClassRx = false;
    private boolean isClassElse = false;
    private boolean mIsManufacturer = false;
    private boolean mShopStoreSelectedStatus = false;
    protected int isThirdCompany = -1;//自营
    protected int highGross = -1;     // 高毛 : highGross = 1
    private int clickPosition = -1;//当前点击的pos
    protected int source = 1;   // 搜索来源,默认是输入，  1:输入2:历史3:推荐4:联想 5语音  6关键词纠错无结果 7关键词纠错人工映射 8搜索发现

    protected int mJgSource = JG_SOURCE_KEYWORD_SEARCH; // 极光搜索来源（不用上面的了 没人维护那个了）
    protected static final int JG_SOURCE_KEYWORD_SEARCH = 1; //关键词搜索
    protected static final int JG_SOURCE_HISTORY_SEARCH = 2; //历史搜索
    protected static final int JG_SOURCE_VOICE_SEARCH = 3; //语音搜索
    protected static final int JG_SOURCE_SCAN_SEARCH = 4; //扫一扫
    protected static final int JG_SOURCE_SUG_SEARCH = 5; //提示词
    protected static final int JG_SOURCE_FIND_SOURCE = 6; //搜索发现


    protected int selectedSearchPosition = -1;
    private String keyWordHitType = KEY_WORD_HIT_TYPE_DEFAULT;//命中词类型
    private static final String KEY_WORD_HIT_TYPE_DEFAULT = "1";

    private float startY;//手指上下滑动的距离
    private int moveDistance;//购物车动画移动的距离
    private boolean isShowFloatImage = true;//悬浮购物车是否显示
    private Timer timer;//悬浮购物车计时器
    private long upTime;//记录抬起的时间
    private RelativeLayout.LayoutParams rvlayoutParams;

    public static final int SEARCH_TYPE_NORMAL = 1; //正常搜索
    public static final int SEARCH_TYPE_TRUANCATION = 2; //截断召回搜索
    public static final int SEARCH_TYPE_OUT = 3; //兜底推荐
    protected int searchType = SEARCH_TYPE_NORMAL;

    private String recommendKeyWord = ""; //推荐关键字
    private List<String> hotKeyWordList = new ArrayList(); //热词

    private SearchRecommendAdapter searchRecommendAdapter;

    private String preKeyWordTrack;
    private String isOftenBuyFrom = "";
    private String isShowCart = "";

    private int offset_search_recomment = 0;
    protected String prePageSource;
    private int from;
    private String hotKey;
    private String hostPosition;
    protected String masterStandardProductId;
    private String originalShowName;
    private boolean fromH5;
    protected SearchDataViewModel searchViewModel;
    private List<ManufacturersBean> mManufacturerListData;
    protected boolean isSelectedJD, isSelectedSF;
    protected int isFromHome = 0;
    private SuggestListItemLabel finalSelectedLabel;
    protected String nearEffective;//有效期
    protected String nearEffectiveStr;//有效期
    protected String mEntrance = "";

    //动态标签选中结果
    protected Map<String, String> mDynamicLabelSelectedMap = new HashMap<>();
    protected List<SearchDynamicLabelConfig> mDynamicLabelSelectedConfigList = new ArrayList<>();

    protected List<SearchDynamicLabelConfig> mDynamicLabelConfig;
    private boolean isFromCart = false;
    private void resetFilterCondition() {
        boolean isAll = isAvailable || isPromotion
                || !TextUtils.isEmpty(priceRangeFloor) || !TextUtils.isEmpty(priceRangeTop) || isSelectedJD || isSelectedSF
                || isClassA || isClassB || isClassRx || isClassElse || isSpellGroupAndPgby
                || !TextUtils.isEmpty(specStr) || !TextUtils.isEmpty(selectedShopcodes) || (lastNames != null && !lastNames.isEmpty());

    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_search;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        detailAdapter = getAdapter();
        searchViewModel = new ViewModelProvider(this).get(SearchDataViewModel.class);
        id = getIntent().getStringExtra("id");
        voice = getIntent().getStringExtra("voice");
        scanCode = getIntent().getStringExtra("scanCode");
        setMJgspid(getIntent().getStringExtra(IntentCanst.JG_JGSPID));
        try {
            String fromStr = getIntent().getStringExtra("from");
            from = Integer.parseInt(fromStr);
            hotKey = getIntent().getStringExtra("keyword");
            hostPosition = getIntent().getStringExtra("hostposition");
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            isFromHome = Integer.parseInt(getIntent().getStringExtra("isFromHome"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            String fromH5Str = getIntent().getStringExtra("fromH5");
            fromH5 = Boolean.valueOf(fromH5Str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            isOftenBuyFrom = getIntent().getStringExtra("isOftenBuyFrom");
            isShowCart = getIntent().getStringExtra("isShowCart");
            masterStandardProductId = getIntent().getStringExtra("masterStandardProductId");
            originalShowName = getIntent().getStringExtra("originalShowName");
        } catch (Exception e) {
            e.printStackTrace();
        }
//        try {
//            selectedShopcodes = getIntent().getStringExtra("shopCodes");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        prePageSource = getIntent().getStringExtra("pageSource");
        if (TextUtils.isEmpty(id) && TextUtils.isEmpty(voice) && TextUtils.isEmpty(scanCode) && !isFromOftenBuy()) {
            //控制输入法弹出状态
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN | WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
        } else {
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN | WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        }

        try {
            spFrom = getIntent().getStringExtra("spFrom");
            if (spFrom == null) spFrom = "1";
        } catch (Exception e) {
            e.printStackTrace();
        }

        super.onCreate(savedInstanceState);
    }

    @Override
    protected void initViewBefore() {
        super.initViewBefore();
        searchStartRecommendView.setMEntrance(mEntrance);
        searchFindView.setMEntrance(mEntrance);
        flexBoxHistory.setMEntrance(mEntrance);
    }

    @Override
    protected void initData() {
        super.initData();
        if (TextUtils.equals(spFrom, "7")) {
            rbSpellGroup.setVisibility(View.GONE);
            mRbDpby.setVisibility(View.GONE);
        }
        initObserver();
        tvSynthesize.setVisibility(View.VISIBLE);
        tvSynthesize.setActivated(true);
        tvDefault.setVisibility(View.GONE);

        tv_specification.setVisibility(View.VISIBLE);
        rbBrandRg01New.setVisibility(View.GONE);

        tvShopStore.setVisibility(View.VISIBLE);
        mTvManufacturer.setVisibility(View.GONE);

        mTvManufacturer.setVisibility(View.VISIBLE);
        if (!isFromOftenBuy()) {
            showSoftInput();
        }
        trackHotKey();
        // 搜索启动页初始化
        initSearchLauchPage();
        // 搜索下拉弹框初始化
        initSearchDropdown();
        // 搜索结果相关初始化
        initSearchResultPage();
        // 初始化配送时效引导组件
        initDeliveryGuide();

        setNewData(getIntent());
        findViewById(R.id.iv_a_magnifying_glass).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                RoutersUtils.open("ybmpage://captureactivity?"+ CaptureActivity.INTENT_FROM_SEARCH+"=1"+"&"+IntentCanst.JG_JGSPID+"="+getMJgspid());

                scanClickReport(mEntrance);
            }
        });

        boolean pushEnabled = NotificationManagerCompat.from(this).areNotificationsEnabled();
        HashMap<String, String> content = new HashMap<String, String>();
        content.put("status", pushEnabled ? "0" : "1");
        XyyIoUtil.track("notification_Switch", content);
        showSearchListForOftenBuy(isFromOftenBuy());
    }

    /**
     * 初始化配送时效引导组件
     */
    private void initDeliveryGuide() {
        deliveryGuideView = new SearchDeliveryGuideView(this);

        // 获取Activity的根视图（DecorView）来确保全屏覆盖
        ViewGroup rootView = (ViewGroup) getWindow().getDecorView();

        // 设置全屏布局参数
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        );

        // 添加到DecorView，确保覆盖整个屏幕包括状态栏
        rootView.addView(deliveryGuideView, layoutParams);

        // 设置引导点击监听
        deliveryGuideView.setOnGuideClickListener(new SearchDeliveryGuideView.OnGuideClickListener() {
            @Override
            public void onGuideClick() {
                // 标记引导已显示
                SearchGuideManager.INSTANCE.markDeliveryGuideShown(BaseSearchProductActivity.this);
                // 可以在这里添加其他点击后的逻辑，比如埋点
                XyyIoUtil.track("delivery_guide_clicked");
            }
        });
    }

    private void initObserver() {
        searchViewModel.getSearchFindLiveData().observe(this, searchFindBeanBaseBean -> {
            if (searchFindBeanBaseBean.isSuccess()) {
                searchFindView.setData(searchFindBeanBaseBean.data.getList());
                searchFindView.setItemClickListener((searchFindItemBean, index) -> {
                    setMJgspid(replacePartJgspid(getMJgspid(),"_s","_s106"));
                    isSuggest = false;
//                    source = 8;
                    selectedSearchPosition = index + 1;
                    titleEt.setText(searchFindItemBean.getSearchKeyword());
                    titleEt.setSelection(titleEt.getText().length());
                    keyword = searchFindItemBean.getSearchKeyword();
                    clearOptionAndSearchNewData(JG_SOURCE_FIND_SOURCE);

//                    HashMap<String, String> trackParams = new HashMap<>();
//                    trackParams.put("keyword", keyword);
//                    if (!TextUtils.isEmpty(preKeyWordTrack)) {
//                        trackParams.put("pkw", preKeyWordTrack);
//                    }
//                    trackParams.put("history", "history_" + index);
//                    XyyIoUtil.trackSug(trackParams, getTrackSugFromSpid());
                    return null;
                });
            }
        });
        searchViewModel.getVoucherLiveData().observe(this, EmptyBean -> {
            goToSearchJumpLink();
        });
    }

    private void trackHotKey() {
        if (from != 10 || TextUtils.isEmpty(hotKey)) return;
        HashMap<String, String> trackParams = new HashMap<>();
        trackParams.put("wq", titleEt.getText().toString());
        trackParams.put("keyword", hotKey);
        trackParams.put("hotword", "hotword_" + hostPosition);
        if (!TextUtils.isEmpty(preKeyWordTrack)) {
            trackParams.put("pkw", preKeyWordTrack);
        }
        XyyIoUtil.trackSug(trackParams, getTrackSugFromSpid());
    }

    // 初始化搜索关键词sug下拉选项相关
    private void initSearchDropdown() {

        //region 初始化搜索sug下拉弹框
        suggestPopWindow = new SuggestNewPopWindowNewV2(this, AppNetConfig.SUGGEST_NEW_V2, new RequestParams(), findViewById(R.id.ll_title));
        View clBrandCtl = findViewById(R.id.brand_ctl);
        suggestPopWindow.setBgView(clBrandCtl);
        suggestPopWindow.setPopFocusable(false);
        suggestPopWindow.setPopOutsideTouchable(false);
        suggestPopWindow.setPrePageSource(prePageSource);
        suggestPopWindow.setPageUrl(getIntent().getStringExtra(Routers.KEY_RAW_URL));
        suggestPopWindow.setOnShowCallback(aBoolean -> {
            if (aBoolean) {
                setSearchStartPageVisibility(false);
            }
            return null;
        });
        suggestPopWindow.setMShopClickCallBack((suggestShopBean, position) -> {
            sugClick(position, null, suggestShopBean);
            String clickName = "";
            String clickLink = "";
            String jgspid = replacePartJgspid(getMJgspid(), "_s", "_s103");
            if (suggestShopBean != null && suggestShopBean.getShopName() != null) {
                clickName = suggestShopBean.getShopName();
            }
            if (suggestShopBean != null && suggestShopBean.getSkipUrl() != null) {
                clickLink = suggestShopBean.getSkipUrl();
            }
            sugClickReport(jgspid,clickName, position, titleEt.getText().toString(), clickLink);
            searchReport(jgspid,titleEt.getText().toString(),position+1,clickLink);
            return null;
        });
        suggestPopWindow.setItemClickListener((selectedItem, id, position) -> {
            if (titleEt == null) {
                return;
            }
            if (id != -1 && position == 0) {//跳转到商品详情 这里面的id是传的商品的id 不为-1说明列表有header商品显示
                RoutersUtils.open("ybmpage://productdetail?" + IntentCanst.PRODUCTID + "=" + id);
                return;
            }
            clearOption();
            hideSoftInput();
            String str = "";
            if (selectedItem != null) {
                str = selectedItem.getSuggestion();
            }
            sugClick(position, str, null);
            keyword = str;
            source = 4;
            mJgSource = JG_SOURCE_SUG_SEARCH;
            selectedSearchPosition = position + 1;
            setMJgspid(replacePartJgspid(getMJgspid(),"_s","_s103"));
            sugClickReport(selectedItem,position,titleEt.getText().toString());
            HashMap<String, String> trackParams = new HashMap<>();
            trackParams.put("wq", titleEt.getText().toString());
            trackParams.put("keyword", keyword);
            if (!TextUtils.isEmpty(preKeyWordTrack)) {
                trackParams.put("pkw", preKeyWordTrack);
            }
            trackParams.put("suggest", "suggest_" + position);
            List<Object> allItemList = suggestPopWindow.getAllItemList();
            for (int i = 0; i < 5; i++) {
                if (allItemList.size() >= i + 1) {
                    Object item = allItemList.get(i);
                    if (item == null) continue;
                    if (item instanceof RowsBean) {
                        trackParams.put("sg" + (i + 1), ((RowsBean) item).getShowName());
                    } else if (item instanceof SuggestShopBean) {
                        trackParams.put("sg" + (i + 1), ((SuggestShopBean) item).getShopName());
                    } else if (item instanceof String) {
                        trackParams.put("sg" + (i + 1), (String) item);
                    }
                }
            }
            XyyIoUtil.trackSug(trackParams, getTrackSugFromSpid());
            isSuggest = false;
            //添加sug标签
            SpannableStringBuilder builder = new SpannableStringBuilder(str);
            SuggestListItemLabel selectedLabel = null;
            if (selectedItem.getLeftLabel() != null) {
                selectedLabel = selectedItem.getLeftLabel();
            } else if (selectedItem.getRightLabel() != null) {
                selectedLabel = selectedItem.getRightLabel();
            }
            if (selectedLabel != null && selectedLabel.getName() != null) {
                SpannableStringBuilder labelBuilder = new SpannableStringBuilder();
                String labelStr = selectedLabel.getName() + " ";
                int labelStart = 0;
                int labelEnd = labelStr.length();
//                labelBuilder.insert(0, labelStr);
//                labelBuilder.setSpan(new RoundBackgroundColorSpan(
//                                Color.parseColor("#FFFFFF"),
//                                Color.parseColor("#676773"),
//                                SearchProductActivity.this),
//                        labelStart, labelEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                builder.append(" ").append(labelStr);
                titleEt.setMovementMethod(LinkMovementMethod.getInstance());
                titleEt.setHighlightColor(getResources().getColor(android.R.color.transparent));
                addFilterParamsBySugTag(selectedLabel);
                String finalStr1 = str;
                finalSelectedLabel = selectedLabel;
                ClickableSpan clickableSpan = new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View view) {
                        titleEt.setText(finalStr1);
                        removeFilterParamsBySugTag(finalSelectedLabel);
                        getSearchData();
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        super.updateDrawState(ds);
                        ds.setUnderlineText(false);
                    }
                };
                builder.setSpan(clickableSpan, str.length()+1, str.length()+1 + labelStr.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                builder.setSpan(new RoundBackgroundColorSpan(
                        Color.parseColor("#FFFFFF"),
                        Color.parseColor("#676773"),
                        BaseSearchProductActivity.this), str.length()+1, str.length()+ labelStr.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                builder.append("\u200b");
            }

            titleEt.setText(builder);
            titleEt.setSelection(builder.length());
//            titleEtMask.setVisibility(View.VISIBLE);
            getSearchData();
            isSuggest = false;
//            titleEt.setText(str);
//            titleEt.setSelection(builder.length());
        });
        suggestPopWindow.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                isScroll(event);
                return false;
            }
        });
        //endregion

        //region 输入框相关监听
        titleEt.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_FLAG_CAP_CHARACTERS | InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS);
        titleEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                // 搜索条件变化后 复原年货节选中状态
                resetCbActivityTagStatue();

                if (!TextUtils.isEmpty(s.toString().trim())) {
                    ivClear.setImageResource(R.drawable.clear_sousou);//setVisibility(View.VISIBLE);
                } else {
                    ivClear.setImageResource(R.drawable.icon_home_steady_voice);  //setVisibility(View.INVISIBLE);
                    search_guid_layout_by_layout.setVisibility(View.GONE);
                }
                if (!isSuggest ) {
                    if (selectId) {
                        selectId = false;
                        return;
                    }
                    isSuggest = true;
                    //KEY_WORD_HIT_TYPE_DEFAULT.equals(keyWordHitType) 表示不是选中命中词的情况下。s.length()之所以配合使用是因为如果是命中词的情况下 避免删除输入数据后历史界面不显示
                    if ((TextUtils.isEmpty(id) || TextUtils.isEmpty(categoryName)) && (KEY_WORD_HIT_TYPE_DEFAULT.equals(keyWordHitType) || s.length() == 0)) {
                        showSearchList(false);
                    } else {
                        showSearchList(true);
                    }
                    return;
                }
                suggestStr = s.toString();
                suggestPopWindow.cancelHandler(false);
                suggestPopWindow.suggest(suggestStr);
                if(s.length() == 0) {
                    setSearchStartPageVisibility(true);
                }
            }
        });
        titleEt.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_SEND) {
                    //这种情况不搜索
                    if (mContent!=null && searchJumpLink!=null && TextUtils.isEmpty(titleEt.getText().toString()) && !searchJumpLink.isEmpty()){
                        if (!TextUtils.isEmpty(couponId)) {
                            searchViewModel.getVoucher(couponId);
                            return true;
                        }
                        goToSearchJumpLink();
                        return true;
                    }
                    String strT = titleEt.getText().toString().trim();
                    String strH = mContent;
                    if (TextUtils.isEmpty(strT) && !TextUtils.isEmpty(strH) &&
                            !getResources().getString(R.string.search_hint2).equals(titleEt.getHint().toString())) {
                        keyword = strH;
                    } else {
                        keyword = strT;
                    }
                    if (!TextUtils.isEmpty(mContent)) {
                        titleEt.setHint(mContent);
                    } else {
                        titleEt.setHint(R.string.search_hint2);
                    }
                    titleEt.setText(keyword);
                    titleEt.setSelection(keyword.length());
                    source = 1;
                    goToSearch();
                    return true;
                }
                return false;
            }
        });
        titleEt.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            //当键盘弹出隐藏的时候会 调用此方法。
            @Override
            public void onGlobalLayout() {
                int height = UiUtils.getNavigationBarHeight(BaseSearchProductActivity.this);
                Rect r = new Rect();
                //获取当前界面可视部分
                BaseSearchProductActivity.this.getWindow().getDecorView().getWindowVisibleDisplayFrame(r);
                //获取屏幕的高度
                int screenHeight = BaseSearchProductActivity.this.getWindow().getDecorView().getRootView().getHeight();
                //此处就是用来获取键盘的高度的， 在键盘没有弹出的时候 此高度为0 键盘弹出的时候为一个正数
                int heightDifference = screenHeight - r.bottom - height;
                showAViewOverKeyBoard(heightDifference);
            }
        });
        titleEt.setOnClickListener(v -> {
            if (!TextUtils.isEmpty(titleEt.getText())) {
                suggestPopWindow.cancelHandler(false);
                boolean isContainSugTag = isContainSugTag();
//                if (isContainSugTag) {
//                    titleEt.setText(keyword);
//                    removeFilterParamsBySugTag(finalSelectedLabel);
//                    getSearchData();
//                }
                if (!isContainSugTag) {
                    suggestPopWindow.suggest(titleEt.getText().toString().trim());
                }
            }
        });
        //endregion
    }

    private boolean isContainSugTag() {
        if (keyword == null) return false;
        return titleEt.getText().length() != keyword.length();
    }

    private void addFilterParamsBySugTag(SuggestListItemLabel selectedLabel) {
        mHandler.sendEmptyMessage(20);
        if (selectedLabel.getSearchConditions() == null) return;
        boolean categoryIdsCleared = false;
        boolean specCleard = false;
        for (SuggestSearchConditions searchCondition : selectedLabel.getSearchConditions()) {
            if (searchCondition.getSearchField().equals("spec")) {
                //规格
                if (!specCleard) {
                    specCleard = true;
                    specStr = "";
                }
                if (TextUtils.isEmpty(specStr)) {
                    specStr = searchCondition.getSearchValue();
                } else if (!TextUtils.isEmpty(specStr)) {
                    specStr += ("," + searchCondition.getSearchValue());
                }
                selectedSugSpec = specStr;
                setFilterBtnTextColor();
            } else if (searchCondition.getSearchField().equals("categoryId")) {
                //分类
                if (!categoryIdsCleared) {
                    //分类id为清空则清空并设置状态为清空
                    categoryIdsCleared = true;
                    id = "";
                }
                String categoryIdsStr = searchCondition.getSearchValue();
                if (TextUtils.isEmpty(categoryIdsStr)) continue;
                String[] categoryIds = categoryIdsStr.split(",");
                String categoryId = categoryIds[categoryIds.length - 1];
                if (TextUtils.isEmpty(id)) {
                    id = categoryId;
                } else {
                    id = id + "," + categoryId;
                }
                selectedSugCategory = id;
                mRbAllCategory.setActivated(true);
            }
        }
    }

    private void removeFilterParamsBySugTag(SuggestListItemLabel selectedLabel) {
        mHandler.sendEmptyMessage(20);
        if (selectedLabel == null) return;
        if (selectedLabel.getSearchConditions() == null) return;
        for (SuggestSearchConditions searchCondition : selectedLabel.getSearchConditions()) {
            if (searchCondition.getSearchField().equals("spec")) {
                //规格
                if (TextUtils.isEmpty(specStr)) {
                    return;
                } else if (!TextUtils.isEmpty(specStr)) {
                    String[] specArray = specStr.split(",");
                    List<String> specList = new ArrayList<>(Arrays.asList(specArray));
                    StringBuilder tempStr = new StringBuilder();
                    Iterator<String> iterator = specList.iterator();
                    while (iterator.hasNext()) {
                        String item = iterator.next();
                        if (item.equals(searchCondition.getSearchValue())) {
                            iterator.remove();
                        } else {
                            tempStr.append(item).append(",");
                        }
                    }
                    if (!TextUtils.isEmpty(tempStr.toString())) {
                        specStr = tempStr.substring(0, tempStr.length() - 1);
                    } else specStr = "";
                    String[] split = specStr.split(",");
                    for (int i = 0; i < split.length; i++) {
                        String selectedKey = split[i];
                        for (int j = 0; j < specBeans.size(); j++) {
                            if (specBeans.get(j).key.equals(selectedKey)) {
                                specBeans.get(j).isSelected = true;
                            } else specBeans.get(j).isSelected = false;
                        }
                    }
                    if (specficationPopWindow != null) {
                        specficationPopWindow.setData(specBeans);
                    }
                }
                setFilterBtnTextColor();
            } else if (searchCondition.getSearchField().equals("categoryId")) {
                //分类
                if (TextUtils.isEmpty(id)) {
                    return;
                } else if (!TextUtils.isEmpty(id)) {
                    String[] categoryIdArray = id.split(",");
                    List<String> categoryIdList = new ArrayList<>(Arrays.asList(categoryIdArray));
                    StringBuilder tempStr = new StringBuilder();
                    Iterator<String> iterator = categoryIdList.iterator();
                    while (iterator.hasNext()) {
                        String item = iterator.next();
                        if (item.equals(searchCondition.getSearchValue())) {
                            iterator.remove();
                        } else {
                            tempStr.append(item).append(",");
                        }
                    }
                    if (!TextUtils.isEmpty(tempStr.toString())) {
                        id = tempStr.substring(0, tempStr.length() - 1);
                    } else id = "";
                    mRbAllCategory.setActivated(!TextUtils.isEmpty(id));
                }
            }
        }
    }

    // 搜索条件变化后 复原年货节选中状态
    private void resetCbActivityTagStatue() {
        cbActivityTag.setChecked(false);
        if (!TextUtils.isEmpty(tagList) && !TextUtils.isEmpty(activityScreenTagId) && tagList.contains(activityScreenTagId) && tagList.length() == activityScreenTagId.length()) {
            tagList = "";
        }
        if (!TextUtils.isEmpty(tagList) && !TextUtils.isEmpty(activityScreenTagId) && tagList.contains(activityScreenTagId) && tagList.length() > activityScreenTagId.length()) {
            if (tagList.startsWith(activityScreenTagId)) {
                tagList.replace(activityScreenTagId + ",", "");
            } else {
                tagList.replace("," + activityScreenTagId, "");
            }
        }
    }


    protected void showSearchList(boolean showSearchlist) {

        if (showSearchlist) {
            if (!isSearchProductListViewVisibility()){ //结果页不可见变为可见
                jgTrackPageView(true);
            }
            setSearchStartPageVisibility(false);
            mBrandRg01.setVisibility(View.VISIBLE);
            searchProductListView.setVisibility(View.VISIBLE);
            showFloatCart(true);
        } else {
            if (isSearchProductListViewVisibility()){ //结果页可见变为不可见
                jgTrackPageView(false);
            }
            if (!isFromOftenBuy()) {
                setSearchStartPageVisibility(true);
            }
            mBrandRg01.setVisibility(View.GONE);
            mBrandRg02.setVisibility(View.GONE);
            searchProductListView.setVisibility(View.GONE);
            showFloatCart(false);
        }
    }

    /**
     * 搜索结果页页是否可见
     * @return
     */
    private boolean isSearchProductListViewVisibility(){
        return searchProductListView.getVisibility() == View.VISIBLE;
    }

    private void jgTrackPageView(boolean isResultPage){
        try {
            HashMap<String, Object> properties = new HashMap();
            String pageId = "";
            String title = "";
            String trackUrl = this.getPackageName()+this.getClass().getName();
            if (isResultPage){
                if (this instanceof SearchProductOPActivity){
                    pageId = JGTrackManager.TrackSearchResult.PAGE_ID;
                    title = JGTrackManager.TrackSearchResult.TITLE;
                }else {
                    pageId = JGTrackManager.TrackOldSearchResult.PAGE_ID;
                    title = JGTrackManager.TrackOldSearchResult.TITLE;
                }

            }else {
                if (this instanceof SearchProductOPActivity){
                    pageId = JGTrackManager.TrackSearchIntermediateState.PAGE_ID;
                    title = JGTrackManager.TrackSearchIntermediateState.TITLE;
                }else {
                    pageId = JGTrackManager.TrackOldSearchIntermediateState.PAGE_ID;
                    title = JGTrackManager.TrackOldSearchIntermediateState.TITLE;
                }
            }

            properties.put(JGTrackManager.FIELD.FIELD_PAGE_ID,pageId);
            properties.put(JGTrackManager.FIELD.FIELD_TITLE,title);
            properties.put(JGTrackManager.FIELD.FIELD_URL,trackUrl);
            JGTrackManager.Companion.pageViewTrack(this,title,properties);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 从常购清单跳转进入
     *
     * @param isFromOften
     */
    private void showSearchListForOftenBuy(boolean isFromOften) {
        if (!isFromOften) return;
        setSearchStartPageVisibility(false);
        mBrandRg01.setVisibility(View.GONE);
        mBrandRg02.setVisibility(View.GONE);
        searchProductListView.setVisibility(View.GONE);
        showFloatCart(true);
        ll_title.setVisibility(View.GONE);
        rl_title_from_often.setVisibility(View.VISIBLE);
        tv_title.setText(originalShowName);
        getSearchData();
    }

    // 初始化搜索启动页相关
    private void initSearchLauchPage() {
        // 1. 历史搜索 - 数据请求回来后填充

        // 2. 推荐搜索
        searchRecommendAdapter = new SearchRecommendAdapter(R.layout.item_search_recommend_word, recommend);
        searchRecommendAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> setClick(recommend.get(i), i));
        rv_recommed.setLayoutManager(new WrapGridLayoutManager(this, 2));
        rv_recommed.addItemDecoration(new SearchRecommendItemDecoration());
        rv_recommed.setAdapter(searchRecommendAdapter);
    }


    /**
     * 初始化 搜索结果页 商品列表相关
     */
    private void initSearchResultPage() {
        // 初始化商品列表
        detailAdapter.setOnLoadMoreListener(() -> getLoadMoreResponse());
        detailAdapter.setEmptyView(this, R.layout.layout_search_product_empty_view, R.drawable.icon_empty, "啊哦...\n没有找到相关的商品");
//        detailAdapter.setConfigPreHot(true);
//        detailAdapter.setConfigSpellGroupSellOut(true);
        searchProductListView.setAdapter(detailAdapter);
        searchProductListView.setLayoutManager(new WrapLinearLayoutManager(this));
        searchProductListView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                int y = recyclerView.computeVerticalScrollOffset();
                //imitateHeaderMobileEfect(y);
                setTagFootView();
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                mIvFastScrollSearch.showFastScroll(searchProductListView, appbar);
            }
        });
        ExposureManager.INSTANCE.registerExposureListener(searchProductListView, new ExposureCallback() {
            @Override
            public void callback(@NonNull List<Integer> indexList) {
                StringBuilder indexStr = new StringBuilder();
                for (int index : indexList) {
                    Object itemData = detailAdapter.getItem(index);
                    BaseFlowData flowData = detailAdapter.getFlowData();
                    if (flowData != null && itemData instanceof RowsBean) {
                        flowDataPageListPageExposureNew(flowData, ((RowsBean) itemData).getProductId(),
                                ((RowsBean) itemData).getShowName(), index, -1, "");
                        indexStr.append(index).append(" ");
                    }
                }
            }
        });
        rvlayoutParams = (RelativeLayout.LayoutParams) searchProductListView.getLayoutParams();

        // 搜索结果如果是区间搜索，底部添加显示内容
        if (isTagSearch && tagFootView == null) {
            tagFootView = View.inflate(this, R.layout.layout_search_tag_no_data, null);
            ((TextView) tagFootView.findViewById(R.id.tv_tag_search_no_more)).setText(getString(R.string.str_search_tag_no_more, TextUtils.isEmpty(title) ? "专区" : title));
            tagFootView.findViewById(R.id.rtv_tag_search_all).setOnClickListener(v -> {
                removeTagSetting();
            });
            detailAdapter.addFooterView(tagFootView);
        }

        //region 初始化购物车悬浮按钮
        showFloatCart(true);
        rlCart.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                //动画移动的距离 隐藏购物车，控件默认invisible
                moveDistance = rlCart.getWidth();
                //监听结束之后移除监听事件
                rlCart.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
        //endregion

        // 初始化筛选项浮窗相关
        // 1. 综合
        // 2. 分类
        // 3. 规格
        // 4. 商家
        // 5. 筛选

        // 根据活动设置，动态修改筛选项的主题
        setTabFilterColor();

        // 开启未搜到结果的推荐头部的监听
        //startSearchGuidLayoutDrawListener();

        // 活动，如 年货节 等筛选
        cbActivityTag.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH_BIG_PROMOTION_POPUP);
                cbActivityTag.setText(selectActivityScreenTagTitle);
                if (TextUtils.isEmpty(tagList)) {
                    tagList = "";
                }
                if (!TextUtils.isEmpty(tagList) && !tagList.endsWith(",")) {
                    tagList = tagList.concat(",");
                }
                tagList = tagList.concat(activityScreenTagId);

            } else {
                XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH_BIG_PROMOTION_POPUP);
                cbActivityTag.setText(unSelectActivityScreenTagTitle);
                if (TextUtils.isEmpty(tagList)) {
                    tagList = "";
                }
                if (tagList.contains(activityScreenTagId)) {
                    tagList = tagList.replace(activityScreenTagId, "");
                }
                // 如果 taglsit 中的 activityScreenTagId 处在中间，则会出现 ,, 连起来的情况
                tagList = tagList.replace(",,", ",");
            }
            getSearchData();
        });
    }

    private void setTagFootView() {
        if (isTagSearch) {
            if (searchProductListView == null) return;
            LinearLayoutManager mLinearLayoutManager = (LinearLayoutManager) searchProductListView.getLayoutManager();
            if (mLinearLayoutManager == null) return;
            int visibleItemCount = mLinearLayoutManager.getChildCount();    //得到显示屏幕内的list数量
            int totalItemCount = mLinearLayoutManager.getItemCount();    //得到list的总数量
            int pastVisiblesItems = mLinearLayoutManager.findFirstVisibleItemPosition();//得到显示屏内的第一个list的位置数position

            if ((visibleItemCount + pastVisiblesItems) >= totalItemCount && !detailAdapter.isLoading() && detailAdapter.getLoadMoreViewCount() == 0 && searchType != SEARCH_TYPE_OUT) {
                if (tagFootView != null) {
                    tagFootView.setVisibility(View.VISIBLE);
                }
            } else {
                if (tagFootView != null) {
                    tagFootView.setVisibility(View.GONE);
                }
            }
        }
    }

    private void removeTagSetting() {
        tagList = "";
        isTagSearch = false;
        spFrom = "1";
        tvTagTips.setVisibility(View.GONE);
        llTagNoMore.setVisibility(View.GONE);
        search_guid_layout_by_layout.setVisibility(View.GONE);
        if (tagFootView != null) {
            detailAdapter.removeFooterView(tagFootView);
        }
        detailAdapter.setNewData(null);
        setRecyclerViewTopMargin(0);
        clearOptionAndSearchNewData(JG_SOURCE_KEYWORD_SEARCH);//重新执行搜索
    }


    //开启搜索引导词布局的绘制监听
    public void startSearchGuidLayoutDrawListener() {
        search_guid_layout_by_layout.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                //监听结束之后移除监听事件
                search_guid_layout_by_layout.getViewTreeObserver().removeOnGlobalLayoutListener(this);

                searchGuidLayoutHeight = search_guid_layout_by_layout.getMeasuredHeight();
                if (search_guid_layout_by_layout.getVisibility() == View.VISIBLE) {
                    setRecyclerViewTopMargin(searchGuidLayoutHeight);
                }
            }
        });
    }

    //模仿添加的Header移动效果
    private void imitateHeaderMobileEfect(int scroll) {
        if (search_guid_layout_by_layout.getVisibility() != View.VISIBLE) {
            return;
        }
        search_guid_layout_by_layout.setTranslationY(-scroll * COEFFICIENT);

        int t = searchGuidLayoutHeight - scroll;
        if (t < 0) {
            t = 0;
        }
        setRecyclerViewTopMargin(t);
    }


    private void setRecyclerViewTopMargin(int i) {
        rvlayoutParams.topMargin = i;
        searchProductListView.setLayoutParams(rvlayoutParams);
    }

    private static final float COEFFICIENT = 1.3f;
    int searchGuidLayoutHeight = 0;


    /**
     * 控制悬浮购物车显示和隐藏
     *
     * @param show
     */
    protected void showFloatCart(boolean show) {
        if (rlCart == null) return;
        if (show) {
            rlCart.setVisibility(View.VISIBLE);
        } else {
            rlCart.setVisibility(View.GONE);
        }
    }

    @Override
    protected boolean isRegisterEventBus() {
        return true;
    }

    @Override
    protected void receiveEvent(Event event) {
        if (event.getCode() == C.EventCode.SUBSCRIBE_SUCCEED) {
            boolean isSubscribe = (boolean) event.getData();
            if (isSubscribe) {
                if (detailAdapter != null && clickPosition >= 0) {
                    Object item = detailAdapter.getItem(clickPosition);
                    if (item instanceof RowsBean) {
                        RowsBean rowsBeanItem = (RowsBean) item;
                        rowsBeanItem.setBusinessType(1);//设置已经订阅状态
                        detailAdapter.notifyDataSetChanged();
                    }
                }
            }
        }
    }

    /**
     * 语音搜索
     *
     * @param heightDifference
     */
    private void showAViewOverKeyBoard(int heightDifference) {
        if (rl_root == null) {
            return;
        }
        if (heightDifference > 0) {//显示
            if (view == null) {//第一次显示的时候创建  只创建一次
                view = View.inflate(this, R.layout.search_product_voice, null);
                RelativeLayout.LayoutParams loginlayoutParams = new RelativeLayout.LayoutParams(-1, -2);
                loginlayoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
                loginlayoutParams.bottomMargin = heightDifference;
                rl_root.addView(view, loginlayoutParams);
                view.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //语音搜索
                        hideSoftInput(titleEt);
                        RoutersUtils.open("ybmpage://searchvoiceactivity?"+SearchVoiceActivity.INTENT_FROM_SEARCH+"=1"+"&"+IntentCanst.JG_JGSPID+"="+getMJgspid());
                        finish();
                    }
                });
            }
            if(!titleEt.isFocused()){
                // 此处隐藏也有效果，但暂未确定该页面还有哪些地方会走该回调，暂时不在这里用
//                hideSoftInput();
                return;
            }
            view.setVisibility(View.VISIBLE);
        } else {//隐藏
            if (view != null) {
                view.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 设置动态的主题之后，tab需要跟着变化
     */
    private void setTabFilterColor() {
        if (YBMAppLike.isNewTheme) {
            mRbAllCategory.setBackgroundResource(R.drawable.product_rb3_category_selector);
            mRbAllCategory.setBackgroundResource(R.drawable.product_rb3_category_selector);

            //319活动促销样式
            //            mRbPromotion.setCompoundDrawablesWithIntrinsicBounds(null, null
            //                    , UiUtils.getDrawable(this, R.drawable.icon_319_promotion), null);
            //            mRbPromotion.setPadding(0, 0, ConvertUtils.dp2px(3), 0);
            //            mRbPromotion.setBackgroundResource(R.drawable.product_rb4_category_selector);
            //            mRbPromotion.setTextColor(UiUtils.getColor(R.color.white));
            mTvShop.setTextColor(getResources().getColorStateList(R.color.selector_text_color_category_dynamic));
        }
        YBMAppLike.changeThemeBg(R.drawable.base_header_dynamic_bg, mBrandRg01);
    }

    public void getHotSearch() {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("type", "1"); //大搜
        HttpManager.getInstance().post(AppNetConfig.START_WORD_LIST, params, new BaseResponse<StartWord>() {

            @Override
            public void onSuccess(String content, BaseBean<StartWord> obj, StartWord startWord) {
                if (obj != null) {
                    if (obj.isSuccess()) {
                        setHotSearch(startWord);
                    }
                }
            }
        });
    }

    /**
     * 设置热搜
     */
    private void setHotSearch(StartWord startWord) {
        if (startWord == null) {
            return;
        }
        if (llHistory == null) {
            return;
        }
        if (history == null) {
            return;
        }
        recommend.clear();
        if (startWord.keywordList != null) {
            recommend.addAll(startWord.keywordList);
        }
        searchRecommendAdapter.setNewData(recommend);

        history.clear();
        history.addAll(startWord.historyList);
        llHistory.setVisibility(startWord.historyList.size() == 0 ? View.GONE : View.VISIBLE);

        addHistoryTag(history);

    }

    /**
     * 获取搜索发现数据
     */
    private void getSearchFind() {
        searchViewModel.getSearchFindData();
    }

    /**
     * 搜索启动页 - 初始化历史搜索数据
     *
     * @param history
     */
    private void addHistoryTag(List<HistoryKeyWord> history) {
        flexBoxHistory.setMaxLines(2);
        flexBoxHistory.clear();
        for (int i = 0; i < history.size(); i++) {
            flexBoxHistory.addTagView(createHistoryTag(history.get(i), i));
        }
    }

    private TextView createHistoryTag(HistoryKeyWord history, int i) {

        TextView historyTag = new TextView(this);
        historyTag.setTextSize(13);
        historyTag.setTextColor(UiUtils.getColor(R.color.color_292933));
        GradientDrawable shapeDrawable = new GradientDrawable();
        shapeDrawable.setColor(UiUtils.getColor(R.color.color_f7f7f8));
        shapeDrawable.setCornerRadius(UiUtils.dp2px(2));
        historyTag.setBackground(shapeDrawable);
        historyTag.setMaxLines(1);
        if (history.historyType == 2) {
            historyTag.setMaxEms(12);
        } else {
            historyTag.setMaxEms(10);
        }
        historyTag.setEllipsize(TextUtils.TruncateAt.END);
        historyTag.setGravity(Gravity.CENTER);
        historyTag.setPadding(UiUtils.dp2px(8), 0, UiUtils.dp2px(8), 0);

        historyTag.setOnClickListener(view -> {
            if (history.historyType == 2 && !TextUtils.isEmpty(history.skipUrl)) {
                RoutersUtils.open(history.skipUrl);
            }
            if (history.historyType == 1) {
                isSuggest = false;
                source = 2;
                selectedSearchPosition = i + 1;
                titleEt.setText(history.keyword);
                titleEt.setSelection(titleEt.getText().length());
                keyword = history.keyword;
                setMJgspid(replacePartJgspid(getMJgspid(),"_s","_s102"));
                clearOptionAndSearchNewData(JG_SOURCE_HISTORY_SEARCH);
            }
            HashMap<String, String> trackParams = new HashMap<>();
            trackParams.put("keyword", keyword);
            if (!TextUtils.isEmpty(preKeyWordTrack)) {
                trackParams.put("pkw", preKeyWordTrack);
            }
            trackParams.put("history", "history_" + i);
            XyyIoUtil.trackSug(trackParams, getTrackSugFromSpid());

            HashMap<String, String> historyParams = new HashMap<>();
            historyParams.put("history_word", history.keyword);
            historyParams.put("sequence", i + 1 + "");
            XyyIoUtil.track("action_Search_history_word", historyParams);

            JGTrackTopLevelKt.jgTrackSearchResourceClick(this,history.keyword,i,JGTrackManager.TrackSearchIntermediateState.SEARCH_MODULE_HISTORY,JGTrackManager.TrackSearchResult.TRACK_URL);
        });

        // 增加历史搜索词前缀logo
        historyTag.setText(history.keyword);
        if (history.historyType == 2) {
            Drawable icon = ContextCompat.getDrawable(this, R.drawable.icon_search_history);
            icon.setBounds(0, 0, icon.getMinimumWidth(), icon.getMinimumHeight());
            StringUtils.modifyTextViewDrawable(historyTag, icon, 0);
            historyTag.setCompoundDrawablePadding(UiUtils.dp2px(4));
        }

        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, UiUtils.dp2px(30));
        layoutParams.leftMargin = UiUtils.dp2px(10);
        layoutParams.bottomMargin = UiUtils.dp2px(10);
        historyTag.setLayoutParams(layoutParams);

        return historyTag;
    }

    /**
     * 推荐view设置点击
     *
     * @param recommendKeyWord
     */
    public void setClick(RecommendKeyWord recommendKeyWord, int postion) {
        HashMap<String, String> map = new HashMap<>();
        map.put("name", recommendKeyWord.keyword);
        if (!TextUtils.isEmpty(recommendKeyWord.androidUrl)) {
            RoutersUtils.open(recommendKeyWord.androidUrl);
            map.put("action", recommendKeyWord.androidUrl);
        } else {
            isSuggest = false;
            keyword = recommendKeyWord.keyword;
            source = 3;
            selectedSearchPosition = postion + 1;
            titleEt.setText(keyword);
            titleEt.setSelection(titleEt.getText().length());
            getSearchData();
        }
        XyyIoUtil.track("action_Search_recommendWord", map);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != Activity.RESULT_OK) {
            return;
        }
        switch (requestCode) {//选择全部分类
            case 1000:
                id = data.getStringExtra("id");
                categoryName = data.getStringExtra("name");
                keyword = null;
                titleEt.setText("");
                mRbAllCategory.setText(categoryName);
                getSearchData();
                break;
            case 3000:
                String keywords = data.getStringExtra("keywords");
                if (keywords.equals("clearAll")) {
                    // 点击清空所有历史后返回页面刷新
                    getHotSearch();
                    getSearchFind();
                    offset_search_recomment = 0;
                } else {
                    backSearchData(keywords);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 点击 更多历史中的一个标签 回来搜索
     */
    private void backSearchData(String text) {
        isSuggest = false;
        // 搜索 - 月历史来源
        source = 8;
        selectedSearchPosition = recommend.indexOf(text) + 1;
        keyword = text;
        titleEt.setText(text);
        titleEt.setSelection(titleEt.getText().length());
        getSearchData();
    }

    @Override
    protected String getRawAction() {
        return "ybmpage://searchproduct/";
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setNewData(intent);
    }

    // 极光埋点pageView时，这种情况无需手动埋PageView方法 会自动埋 故这种情况不埋点
    private void setNewData(Intent intent) {
        if (intent == null) {
            return;
        }
        keyword = intent.getStringExtra("keyword");
        mContent = intent.getStringExtra("mContent");
        hotLabel = intent.getStringExtra("hotLabel");
        searchJumpLink = intent.getStringExtra("searchJumpLink");
        couponId = intent.getStringExtra("couponId");
        show = intent.getStringExtra("show");
        voice = intent.getStringExtra("voice");
        scanCode = intent.getStringExtra("scanCode");
        tagList = intent.getStringExtra("tagList");
        if (TextUtils.isEmpty(tagList)) {
            tagList = "";
        }
        title = intent.getStringExtra("title");
        // shopcode 用;分割，用","分割的  android系统会从第一个,截断
        shopCodesFromOut = getIntent().getStringExtra("shopCodes");
        String isFromCartStr = getIntent().getStringExtra("isFromCart");
        if (isFromCartStr != null && isFromCartStr.equals("1")) {
            isFromCart = true;
        }
        isExcludePt = getIntent().getStringExtra("isExcludePt");
        if (!TextUtils.isEmpty(shopCodesFromOut)) {
            shopCodesFromOut = shopCodesFromOut.replace(";", ",");
        }
        isTagSearch = !TextUtils.isEmpty(tagList);
        if (isTagSearch) {
            spFrom = "3";
        }
        if (!TextUtils.isEmpty(voice)) {
            keyword = voice;
            source = 5;
            mJgSource = JG_SOURCE_VOICE_SEARCH;
        }
        if (!TextUtils.isEmpty(scanCode)){
            keyword = scanCode;
            mJgSource = JG_SOURCE_SCAN_SEARCH;
        }
        id = intent.getStringExtra("id");
        categoryName = intent.getStringExtra("name");
        if (TextUtils.isEmpty(id) || TextUtils.isEmpty(categoryName)) {
            mBrandRg01.setVisibility(View.GONE);
            mBrandRg02.setVisibility(View.GONE);
            searchProductListView.setVisibility(View.GONE);
            showFloatCart(false);
        } else {
            mRbAllCategory.setText(categoryName);
            mRbAllCategory.setActivated(true);
            setSearchStartPageVisibility(false);
        }
        if (TextUtils.isEmpty(keyword)) {
            if (TextUtils.isEmpty(id) || TextUtils.isEmpty(categoryName)) {
                getHotSearch();
                getSearchFind();
            } else {
                clearOptionAndSearchNewData(mJgSource);
            }
        } else {
            //如果是"all"就不显示在框内
            if (!keyword.equalsIgnoreCase("all")) {
                titleEt.setText(keyword);
                titleEt.setSelection(keyword.length());
            }
            clearOptionAndSearchNewData(mJgSource);
        }

        if (!TextUtils.isEmpty(show)) {
            titleEt.setHint(show);
        }
        //专区搜索提示展示
        tvTagTips.setVisibility(isTagSearch ? View.VISIBLE : View.GONE);
        tvTagTips.setText(getString(R.string.str_search_tag_tips, TextUtils.isEmpty(title) ? "专区" : title));
        tvTagSearchNoMore.setText(getString(R.string.str_search_tag_no_more, TextUtils.isEmpty(title) ? "专区" : title));

        if (!TextUtils.isEmpty(shopCodesFromOut)) {
            if (!isFromOftenBuy() && !isFromCart) {
                tvTagTips.setVisibility(View.VISIBLE);
            }
            tvTagTips.setText(getString(R.string.str_search_coupon_tips, TextUtils.isEmpty(title) ? " " : title));
        }
        if (mContent!=null && !mContent.isEmpty()){
//            titleEt.setText(mContent);
//            titleEt.setSelection(mContent.length());
            if (!TextUtils.isEmpty(hotLabel)) {
                setHintLabel();
            } else {
                titleEt.setHint(mContent);
            }
        }

    }

    private void setHintLabel() {
        String hintText = mContent + " ";
        HotStyleBean hotStyleBean = new Gson().fromJson(hotLabel, HotStyleBean.class);
        if (hotStyleBean == null || TextUtils.isEmpty(hotStyleBean.getAtmosphereIconText())) {
            return;
        }
        String labelText = hotStyleBean.getAtmosphereIconText();

        SpannableString spannableString = new SpannableString(hintText + labelText);
        int backgroundColor = ContextCompat.getColor(BaseSearchProductActivity.this, R.color.color_FF2121);
        int textColor = ContextCompat.getColor(BaseSearchProductActivity.this, R.color.back_white);
        try {
            if (!TextUtils.isEmpty(hotStyleBean.getAtmosphereBackgroundColor())) {
                backgroundColor = Color.parseColor(hotStyleBean.getAtmosphereBackgroundColor());
            }
        } catch (Exception e) {
        }
        try {
            if (!TextUtils.isEmpty(hotStyleBean.getAtmosphereFontColor())) {
                textColor = Color.parseColor(hotStyleBean.getAtmosphereFontColor());
            }
        } catch (Exception e) {
        }

        // 设置背景颜色、文本颜色、背景透明度、文本透明度、圆角半径、内边距
        float backgroundAlpha = 0.4f; // 背景透明度为40%
        float textAlpha = 0.6f;       // 文本透明度为60%
        float cornerRadius = 8f;     // 圆角半径
        float padding = 10f;           // 内边距

        AlphaBackgroundSpan alphaBackgroundSpan = new AlphaBackgroundSpan(
                backgroundColor, textColor, backgroundAlpha, textAlpha, cornerRadius, padding
        );
        spannableString.setSpan(alphaBackgroundSpan, hintText.length(),
                spannableString.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(11, true); // 24px，true 表示以像素为单位
        spannableString.setSpan(sizeSpan, hintText.length(),
                spannableString.length(),  Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        titleEt.setHint(spannableString);
    }
    private void goToSearchJumpLink() {
        //输入框是带内容跳转进来 并且跳转链接不为空
        String mUrl = searchJumpLink;
        Integer rank = null;
        HashMap<String,Object> mParams = new HashMap<>();
        mParams.put("referrer",getFullClassName(this));
        mParams.put("referrerTitle","搜索页");
        mParams.put("referrerModule","搜索页");
        mParams.put("entrance","首页(搜索框)");
        mUrl = splicingUrlWithParams(mUrl,mParams);
        RoutersUtils.open(mUrl);
        jgAppHomeHotWordClick(mContent);
        setMJgspid(replacePartJgspid(getMJgspid(),"_s","_s101"));
        if (getMJgAppActionTopHotWordClick() != null){
            rank = getMJgAppActionTopHotWordClick().getRank();
        }
        searchReport(getMJgspid(),mContent,rank,searchJumpLink);
    }
    private void goToSearch(){
        HashMap<String, String> trackParams = new HashMap<>();
        trackParams.put("wq", keyword);
        if (from == 10 && keyword != null && keyword.equals(hotKey)) {
            trackParams.put("keyword", hotKey);
            trackParams.put("hotword", "hotword_" + hostPosition);
        } else {
            trackParams.put("keyword", titleEt.getText().toString());
        }
        if (!TextUtils.isEmpty(preKeyWordTrack)) {
            trackParams.put("pkw", preKeyWordTrack);
        }
        XyyIoUtil.trackSug(trackParams, getTrackSugFromSpid());
        setMPreAnalysisKeyword(null);
        clearOptionAndSearchNewData(JG_SOURCE_KEYWORD_SEARCH);
    }
    /**
     * 价格、最新、销售 三者互斥 可与其他并存
     *
     * @param view
     */
    @OnClick({R.id.title_left_search, R.id.title_right_btn, R.id.tv_history_clear, R.id.iv_clear

            , R.id.rb_available, R.id.rb_dpby, R.id.rb_promotion
            , R.id.rb_self_support, R.id.rb_Gross, R.id.rtv_tag_search_all,
            R.id.rb_same_province
            // KA: 默认 + 分类 + 最新 + 厂家 + 筛选;
            // 非KA：综合 +  分类 + 规格 + 商家 + 筛选；
            , R.id.tv_synthesize, R.id.tv_default
            , R.id.rb_all_category
            , R.id.rb_brand_rg_01_new, R.id.tv_specification,R.id.rb_chinese_medicine
            , R.id.tv_shopStore, R.id.tv_manufacturer
            , R.id.tv_shop, R.id.rb_express, R.id.rb_spell_group
            , R.id.iv_back, R.id.titleEtMask, R.id.rb_can_use_coupon, R.id.rb_spell_group_and_pgby})
    public void clickTab(View view) {
        switch (view.getId()) {
            //返回键
            case R.id.title_left_search:
                hideSoftInput();
                if (suggestPopWindow.isPopShow()) {
                    suggestPopWindow.dismiss();
                    return;
                }
                if (clBeforeSearchResult.getVisibility() == View.GONE) {
                    showSearchList(false);
                    getHotSearch();
                    titleEt.setText("");
                } else {
                    finish();
                }
                break;
            case R.id.title_right_btn:
                //这种情况不搜索
                if (mContent!=null && searchJumpLink!=null && TextUtils.isEmpty(titleEt.getText().toString()) && !searchJumpLink.isEmpty()){
                    if (!TextUtils.isEmpty(couponId)) {
                        searchViewModel.getVoucher(couponId);
                        return;
                    }
                    goToSearchJumpLink();
                    return;
                }
                goToSearch();
                break;
            case R.id.tv_history_clear:
                delHistoryData();

                JGTrackTopLevelKt.jgTrackSearchBtnClick(BaseSearchProductActivity.this,JGTrackManager.TrackSearchIntermediateState.SEARCH_BTN_CLEAN,JGTrackManager.TrackSearchIntermediateState.SEARCH_BTN_HISTORY,mEntrance);
                break;
            //清除搜索框
            case R.id.iv_clear:
                suggestPopWindow.dismiss();
                setSearchStartPageVisibility(true);
                if (!TextUtils.isEmpty(titleEt.getText().toString().trim())) {
                    titleEtMask.setVisibility(View.GONE);
                    ivClearPerformClick();
                } else {
                    HashMap<String, String> content = new HashMap<String, String>();
                    content.put("entry", "search_all");
                    content.put("merchantId", SpUtil.getMerchantid());
                    XyyIoUtil.track(XyyIoUtil.ACTION_VOICE_CLICK, content);
                    RoutersUtils.open("ybmpage://searchvoiceactivity?"+ SearchVoiceActivity.INTENT_FROM_SEARCH+"=1"+"&"+IntentCanst.JG_JGSPID+"="+getMJgspid());

                    JGTrackTopLevelKt.jgTrackSearchBtnClick(BaseSearchProductActivity.this,JGTrackManager.TrackSearchIntermediateState.SEARCH_BTN_VOICE,JGTrackManager.TrackSearchIntermediateState.SEARCH_BOX,mEntrance);
                }
                break;
            //默认
            case R.id.tv_default:
                hideSoftInput();
                //分类
                mRbAllCategory.setActivated(false);
                id = "";
                categoryName = "";
                mRbAllCategory.setText("分类");
                //筛选 | 厂家 | 促销 | 有货
                resetShop();
                rbBrandRg01New.setActivated(false);
                //默认综合排序 由高到低
                property = "smsr.sale_num";
                propertyDescOrAsc = "desc";
                //自营
                mRbSelfSupport.setActivated(false);
                isThirdCompany = -1;
                XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH_ORIGINAL);
                getSearchData();
                break;
            // 综合
            case R.id.tv_synthesize:
                initSynthesizePopwindow();
                if (!synthesizePopWindow.isShow()) {
                    setRightDrawable(tvSynthesize, R.drawable.icon_navigation_arrow_up);
                }
                synthesizePopWindow.show(mBrandRg01);
                jgBtnClick(this,"排序","一级筛选");
                break;
            //分类
            case R.id.rb_all_category:
                XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH_CLASSIFICATION);
                hideSoftInput();
                mRbAllCategory.setActivated(true);
                setIconState(mRbAllCategory, R.drawable.manufacturers_checked_green);
                initPopAllProduct();
                mPopWindowProduct.show(mBrandRg01, id);
                jgBtnClick(this,"分类","一级筛选");
                break;
            //最新
            case R.id.rb_brand_rg_01_new:
                XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH_NEW_ARRIVAL);
                boolean newActivated = false;
                newActivated = rbBrandRg01New.isActivated();
                rbBrandRg01New.setActivated(!newActivated);
                if (!newActivated) {
                    property = "s.create_time";
                    propertyName = "最新上架";
                } else {
                    property = "";
                    propertyName = "";
                }
                getSearchData();
                break;
            // 规格
            case R.id.tv_specification:
                initSpecificationPopWindow();
                tv_specification.setActivated(true);
                setIconState(tv_specification, R.drawable.manufacturers_checked_green);
                specficationPopWindow.show(mBrandRg01);
                jgBtnClick(this,"规格","一级筛选");
                break;
            //厂家
            case R.id.tv_manufacturer:
                XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH_FACTORY);
                hideSoftInput();
                boolean activated = mTvManufacturer.isActivated();
                mTvManufacturer.setActivated(true);
                mIsManufacturer = !activated;
                setIconState(mTvManufacturer, R.drawable.manufacturers_checked_green);
                initManufacturerPop();
                mPopManufacturer.setDataType(id, keyword, isAvailable, isPromotion, isClassA
                        , isClassB, isClassRx, isClassElse, priceRangeFloor, priceRangeTop, lastNames);
                mPopManufacturer.show(mTvManufacturer);
                jgBtnClick(this,"厂家","一级筛选");
                break;
            // 商家
            case R.id.tv_shopStore:
                tvShopStore.setActivated(true);
                mShopStoreSelectedStatus = !tvShopStore.isActivated();
                setIconState(tvShopStore, R.drawable.manufacturers_checked_green);
                initShopStorePopWindow();
                shopStorePopWindow.show(mBrandRg01);
                jgBtnClick(this,"商家","一级筛选");
                break;
            //筛选==>一级分类
            case R.id.tv_shop:
                XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH_SCREEN);
                hideSoftInput();
                mTvShop.setActivated(true);
                mTvShop.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                init1Classify();
                mClassifyPop2.setSpec(specBeans, specStr);
                mClassifyPop2.setShops(shopStores, selectedShopcodes);
                mClassifyPop2.setManufacturer(mManufacturerListData, lastNames);
                mClassifyPop2.setExpress(isSelectedJD, isSelectedSF);
                mClassifyPop2.setAvailable(isAvailable);
                mClassifyPop2.setCanUseCoupon(isCanUseCoupon);
                mClassifyPop2.setDpby(isDpby);
                mClassifyPop2.setSpellGroupAndPgby(isSpellGroupAndPgby);
                mClassifyPop2.setDynamicLabelSelectedMap(mDynamicLabelSelectedMap);
                mClassifyPop2.setDynamicLabelExposureAllListener(dynamicLabelConfig -> {
                    dynamicLabelExposureAll(dynamicLabelConfig, DYNAMIC_LABEL_STYLE_GRID);
                });
                mClassifyPop2.setDynamicConfig(mDynamicLabelConfig);
                mClassifyPop2.show();
                jgBtnClick(this,"筛选","一级筛选");
                break;
            //有货
            case R.id.rb_available:
                XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH_AVAILABLE);
                boolean availableActivated = mRbAvailable.isActivated();
                if (!availableActivated) {
                    XyyIoUtil.track("action_Search_In_stock");
                }
                mRbAvailable.setActivated(!availableActivated);
                isAvailable = !availableActivated;
                if (mClassifyPop2 != null) {
                    mClassifyPop2.setAvailable(isAvailable);
                }
                mHandler.sendMessage(mHandler.obtainMessage(20));
                resetTitleEditSugTag();
                getSearchData();
                jgBtnClick(this,"有货","二级筛选");
                break;
            //单品包邮
            case R.id.rb_dpby:
                boolean dpbyActivated = mRbDpby.isActivated();
                mRbDpby.setActivated(!dpbyActivated);
                isDpby = !dpbyActivated;
                if (mClassifyPop2 != null) {
                    mClassifyPop2.setDpby(isDpby);
                }
                mHandler.sendMessage(mHandler.obtainMessage(20));
                resetTitleEditSugTag();
                getSearchData();
                jgBtnClick(this,"单品包邮","二级筛选");
                break;
            //促销
            case R.id.rb_promotion:
                XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH_ON_SALE);
                boolean promotionActivated = mRbPromotion.isActivated();
                mRbPromotion.setActivated(!promotionActivated);
                isPromotion = !promotionActivated;
                if (mClassifyPop2 != null) {
                    mClassifyPop2.setPromotion(isPromotion);
                }
                mHandler.sendMessage(mHandler.obtainMessage(20));
                getSearchData();
                jgBtnClick(this,"促销","二级筛选");
                break;
            //自营
            case R.id.rb_self_support:
                XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH_SELF_SUPPORT);
                boolean selfSupportActivated = mRbSelfSupport.isActivated();
                mRbSelfSupport.setActivated(!selfSupportActivated);
                if (!selfSupportActivated) {
                    isThirdCompany = 0;
                    XyyIoUtil.track("action_Search_Self");
                } else {
                    isThirdCompany = -1;
                }
                resetTitleEditSugTag();
                getSearchData();
                jgBtnClick(this,"自营","二级筛选");
                searchDynamicFilterClick();
                break;
            //高毛
            case R.id.rb_Gross:
                boolean grossActivated = rbGross.isActivated();
                rbGross.setActivated(!grossActivated);
                if (!grossActivated) {
                    highGross = 1;
                } else {
                    highGross = -1;
                }
                resetTitleEditSugTag();
                getSearchData();
                XyyIoUtil.track("action_Search_HighMargin");
                jgBtnClick(this,"优选","二级筛选");
                searchDynamicFilterClick();
                break;

            //京东顺丰
            case R.id.rb_express:
                boolean isExpressSelected = rbExpress.isActivated();
                rbExpress.setActivated(!isExpressSelected);
                if (mClassifyPop2 != null) {
                    mClassifyPop2.setExpressAll(!isExpressSelected);
                }
                BaseSearchProductActivity.this.mustTagList = makeMustTagList(!isExpressSelected, !isExpressSelected);
                isSelectedJD = !isExpressSelected;
                isSelectedSF = !isExpressSelected;
                mHandler.sendMessage(mHandler.obtainMessage(20));
                resetTitleEditSugTag();
                getSearchData();
                XyyIoUtil.track("action_Search_Logistics");
                jgBtnClick(this,"京东/顺丰","二级筛选");
                searchDynamicFilterClick();
                break;


            case R.id.rtv_tag_search_all://全站搜索
                removeTagSetting();
                break;
            case R.id.rb_chinese_medicine://仅看中药
                boolean isTraditionalChineseMedicine = rbChineseMedicine.isActivated();
                rbChineseMedicine.setActivated(!isTraditionalChineseMedicine);
                BaseSearchProductActivity.this.isTraditionalChineseMedicine = !isTraditionalChineseMedicine;
                resetTitleEditSugTag();
                getSearchData();
                XyyIoUtil.track("rb_chinese_medicine");
                jgBtnClick(this,"仅看中药","二级筛选");
                searchDynamicFilterClick();
                break;
            case R.id.rb_same_province://同省
                boolean isSameProvince = rbSameProvince.isActivated();
                rbSameProvince.setActivated(!isSameProvince);
                BaseSearchProductActivity.this.isSameProvince = !isSameProvince;
                resetTitleEditSugTag();
                getSearchData();
                XyyIoUtil.track("rb_same_province");
                jgBtnClick(this,"同省","二级筛选");
                searchDynamicFilterClick();
                break;
            case R.id.rb_spell_group://拼团
                boolean isSpellGroup = rbSpellGroup.isActivated();
                rbSpellGroup.setActivated(!isSpellGroup);
                BaseSearchProductActivity.this.isSpellGroup = !isSpellGroup;
                mHandler.sendMessage(mHandler.obtainMessage(20));
                resetTitleEditSugTag();
                getSearchData();
                XyyIoUtil.track("action_Search_GroupPurchase");
                jgBtnClick(this,"拼团","二级筛选");
                break;
            case R.id.rb_spell_group_and_pgby: //拼团包邮
                isSpellGroupAndPgby = !rbSpellGroupAndPgby.isActivated();
                //设置拼团
                rbSpellGroup.setActivated(isSpellGroupAndPgby);
                BaseSearchProductActivity.this.isSpellGroup = isSpellGroupAndPgby;
                //设置单品包邮
                mRbDpby.setActivated(isSpellGroupAndPgby);
                isDpby = isSpellGroupAndPgby;
                //设置拼团包邮
                rbSpellGroupAndPgby.setActivated(isSpellGroupAndPgby);
                if (mClassifyPop2 != null) {
                    mClassifyPop2.setSpellGroupAndPgby(isSpellGroupAndPgby);
                }
                mHandler.sendMessage(mHandler.obtainMessage(20));
                resetTitleEditSugTag();
                getSearchData();
                jgBtnClick(this,"拼团包邮","二级筛选");
                searchDynamicFilterClick();
                break;
            case R.id.rb_can_use_coupon:
                boolean isCanUseCoupon = mRbCanUseCoupon.isActivated();
                mRbCanUseCoupon.setActivated(!isCanUseCoupon);
                BaseSearchProductActivity.this.isCanUseCoupon = !isCanUseCoupon;
                if (mClassifyPop2 != null) {
                    mClassifyPop2.setCanUseCoupon(!isCanUseCoupon);
                }
                mHandler.sendMessage(mHandler.obtainMessage(20));
                getSearchData();
                XyyIoUtil.track("available_voucher_screening_options");
                jgBtnClick(this,"可用券","二级筛选");
                searchDynamicFilterClick();
                break;
            case R.id.iv_back:
                finish();
                break;
            case R.id.titleEtMask:
                resetTitleEditSugTag();
                removeFilterParamsBySugTag(finalSelectedLabel);
//                getSearchData();
                break;
            default:
                break;
        }
    }

    //region 商家选项下拉框
    protected ShopStorePopWindow shopStorePopWindow;

    protected List<SearchFilterBean> shopStores = new ArrayList<>();
    protected String selectedShopcodes = "";
    protected String selectedShopNames = "";

    private void initShopStorePopWindow() {

        if (shopStorePopWindow == null) {
            shopStorePopWindow = new ShopStorePopWindow(shopStores);
            shopStorePopWindow.setOnSelectListener(new BaseFilterPopWindow.OnSelectListener() {
                @Override
                public void getValue(SearchFilterBean show) {
                }

                @Override
                public void onShopDismiss(String multiSelectStr, String shopNameString) {

                    setFilterBtnTextColor();
                    setIconState(tvShopStore, R.drawable.icon_navigation_arrow_down);
                    if ((TextUtils.isEmpty(selectedShopcodes) && TextUtils.isEmpty(multiSelectStr))
                            || multiSelectStr != null && !multiSelectStr.equals(selectedShopcodes)) {
                        selectedShopcodes = multiSelectStr;
                        selectedShopNames = shopNameString;
                        tvShopStore.setActivated(!TextUtils.isEmpty(selectedShopcodes));
                        // 改变搜索条件，重新发起搜索
                        getSearchData(false);
                        HashMap<String, String> trackParams = new HashMap<>();
                        trackParams.put("shops", matchShopNameByIds(multiSelectStr));
                        XyyIoUtil.track("shop_filter", trackParams);
                        mHandler.sendMessage(mHandler.obtainMessage(20));
                    }
                    searchAppActionSearchFilterClick(getAppActionSearchFilterClick());
                }

                @Override
                public void OnDismiss(String multiSelectStr) {
                    //商家的挪到上面onShopDismiss去处理  这个方法不能去掉 要触发上面的回调
                    //为什么要这样做是因为之前仅传code的String 现在商家名字的string也要传过来 但是这又是复用的 所以这样做
                }
            });
        }
    }

    private String matchShopNameByIds(String multiSelectStr) {
        if (TextUtils.isEmpty(multiSelectStr)) return null;
        String[] ids = multiSelectStr.split(",");
        String shopNameStr = null;
        for (String s : ids) {
            String shopName = matchShopNameById(s);
            if (shopNameStr == null) {
                shopNameStr = shopName;
            } else {
                shopNameStr = shopNameStr + "," + shopName;
            }
        }
        return shopNameStr;
    }

    private String matchShopNameById(String id) {
        if (shopStores == null) return null;
        for (SearchFilterBean shopStore : shopStores) {
            if (TextUtils.equals(id, shopStore.key)) {
                return shopStore.showName;
            }
        }
        return null;
    }

    //商家服务-快递
    protected String mustTagList = "";
    //是否是拼团
    protected boolean isSpellGroup = false;
    //是否是拼团包邮
    protected boolean isSpellGroupAndPgby = false;

    protected boolean isCanUseCoupon = false;

    //region 规格筛选项下拉框
    protected SpecficationPopWindow specficationPopWindow;
    protected String specStr = "";
    protected List<SearchFilterBean> specBeans = new ArrayList<>();

    private void initSpecificationPopWindow() {

        if (specficationPopWindow == null) {
            specficationPopWindow = new SpecficationPopWindow(specBeans);
            specficationPopWindow.setOnSelectListener(new BaseFilterPopWindow.OnSelectListener() {
                @Override
                public void getValue(SearchFilterBean show) {
                    // 此选项为多选，因此在ondissmiss中直接获取最终选择结果
                }

                @Override
                public void onConfirm(String multiSelectStr) {
                    resetTitleEditSugTag();
                    specStr = multiSelectStr;
                    String[] selectedStrArr = multiSelectStr.split(",");
                    List<String> selectedList = Arrays.asList(selectedStrArr);
                    if (!selectedList.contains(selectedSugSpec) && isContainsInSpecFilters()) {
                        resetTitleEditSugTag();
                    }
                    setFilterBtnTextColor();
                    mHandler.sendMessage(mHandler.obtainMessage(20));
                    getSearchData(false);
                    HashMap<String, String> trackParams = new HashMap<>();
                    trackParams.put("specs", multiSelectStr);
                    XyyIoUtil.track("search_spec_filter", trackParams);
                }

                @Override
                public void OnDismiss(String multiSelectStr) {
                    setIconState(tv_specification, R.drawable.icon_navigation_arrow_down);
                    setFilterBtnTextColor();
                    searchAppActionSearchFilterClick(getAppActionSearchFilterClick());
                }
            });
        }else{
            specficationPopWindow.setData(specBeans);
        }
    }
    //endregion

    private boolean isContainsInSpecFilters() {
        for (SearchFilterBean specBean : specBeans) {
            if (specBean.key.equals(selectedSugSpec)) {
                return true;
            }
        }
        return false;
    }

    private void setFilterBtnTextColor() {
        tv_specification.setActivated(!TextUtils.isEmpty(specStr));
        tvShopStore.setActivated(!TextUtils.isEmpty(selectedShopcodes));
    }


    //region 综合搜索下拉框初始化
    protected SearchFilterBean searchFilterSynthesizeBean;
    private SynthesizePopWindow synthesizePopWindow;

    private void initSynthesizePopwindow() {

        if (synthesizePopWindow == null) {
            synthesizePopWindow = new SynthesizePopWindow();
            synthesizePopWindow.setOnSelectListener(new BaseFilterPopWindow.OnSelectListener() {
                @Override
                public void getValue(SearchFilterBean show) {
                    searchFilterSynthesizeBean = show;
                    if (show == null) return;
                    tvSynthesize.setText(show.getShowSynthesizeStr());
                    String trackStr = null;
                    switch (show.selectedSearchOption) {
                        case SearchFilterBean.SYNTHESIZE:
                            //综合
                            trackStr = "comprehensive";
                            break;
                        case SearchFilterBean.SALESVOLUME:
                            //销量
                            trackStr = "sale";
                            break;
                        case SearchFilterBean.PRICE:
                            //价格
                            trackStr = "price";
                            break;
                    }
                    if (trackStr == null) return;
                    HashMap<String, String> trackParams = new HashMap<>();
                    trackParams.put("item", trackStr);
                    XyyIoUtil.track("search_sort", trackParams);
                }

                @Override
                public void OnDismiss(String multiSelectStr) {
                    resetTitleEditSugTag();
                    setRightDrawable(tvSynthesize, R.drawable.icon_navigation_arrow_down);
                    getSearchData(false);
                    searchAppActionSearchFilterClick(getAppActionSearchFilterClick());
                }
            });
        }
    }
    //endregion

    /**
     * 这里虽然是点击搜索， 但是有些逻辑 比如扫一扫 语音搜索 代码逻辑也走的这里
     * 所以不能在此方法中将source = 1 一刀切  要从方法外面传
     *  source也没人维护了 用的mJgSource
     *
     * @param mJgSource
     */
    protected void clearOptionAndSearchNewData(int mJgSource) {//点击搜索
        if (mJgSource == JG_SOURCE_KEYWORD_SEARCH){
            setMJgspid(replacePartJgspid(getMJgspid(),"_s","_s100"));
        }

        String strT = "";
        if (!TextUtils.isEmpty(titleEt.getText())) {
            strT = titleEt.getText().toString().trim();
        }
        String strH = "";
        if (!TextUtils.isEmpty(titleEt.getHint())) {
            strH = mContent;

        }
        if (TextUtils.isEmpty(strT) && !TextUtils.isEmpty(strH) && !getResources().getString(R.string.search_hint2).equals(titleEt.getHint().toString())) {
            keyword = strH;
            //热词搜索
            setMJgspid(replacePartJgspid(getMJgspid(),"_s","_s101"));
        } else {
            keyword = strT;
        }
        if (!TextUtils.isEmpty(mContent)) {
            if (!TextUtils.isEmpty(hotLabel)) {
                setHintLabel();
            } else {
                titleEt.setHint(mContent);
            }
        } else {
            titleEt.setHint(R.string.search_hint2);
        }
        titleEt.setText(keyword);
        titleEt.setSelection(keyword.length());
        source = 1;
        this.mJgSource = mJgSource;

        clearOption();
        getSearchData();
    }

    private void clearOption() {
        // 清空综合、分类、规格、商家、筛选、有货、促销、自营、高毛的筛选条件
        // 综合
        searchFilterSynthesizeBean = null;
        tvSynthesize.setText("排序");
        if (synthesizePopWindow != null) {
            synthesizePopWindow.initView();
        }
        //分类
        mRbAllCategory.setActivated(false);
        id = "";
        categoryName = "";
        mRbAllCategory.setText("分类");
        if (mPopWindowProduct != null) {
            mPopWindowProduct.resetPosition();
        }
        // 规格
        specStr = "";
        tv_specification.setText("规格");
        tv_specification.setActivated(false);
        if (specficationPopWindow != null) {
            specficationPopWindow.clearSelectedOption();
        }
        // 商家
        selectedShopcodes = "";
        selectedShopNames = "";
        tvShopStore.setText("商家");
        tvShopStore.setActivated(false);
        if (shopStorePopWindow != null) {
            shopStorePopWindow.clearSelectedOption();
        }
        // 有货
        isAvailable = false;
        mRbAvailable.setActivated(false);
        // 促销
        isPromotion = false;
        mRbPromotion.setActivated(isPromotion);
        //自营
        isThirdCompany = -1;
        mRbSelfSupport.setActivated(false);
        // 高毛
        highGross = -1;
        rbGross.setActivated(false);
        //京东
        isSelectedJD = false;
        //顺丰
        isSelectedSF = false;
        rbExpress.setActivated(false);
        mTvManufacturer.setActivated(false);
        setIconState(mTvManufacturer, R.drawable.manufacturers_def);
        //拼团
        rbSpellGroup.setActivated(false);
        isSpellGroup = false;
        //可用券
        mRbCanUseCoupon.setActivated(false);
        isCanUseCoupon = false;
        //单品包邮
        mRbDpby.setActivated(false);
        isDpby = false;
        //拼团包邮
        isSpellGroupAndPgby = false;
        rbSpellGroupAndPgby.setActivated(false);
        //只看中药
        isTraditionalChineseMedicine = false;
        rbChineseMedicine.setActivated(false);
        //同省
        isSameProvince = false;
        rbSameProvince.setActivated(false);
        // 筛选
        resetShop();
    }

    private void ivClearPerformClick() {//清除搜索框
        getHotSearch();
        getSearchFind();
        offset_search_recomment = 0;
        titleEt.setText("");
        // 隐藏悬浮购物车
        showFloatCart(false);
        showSoftInput();

        // 如果不是专区搜索，且活动搜索条件不为空，则还原
        if (!isTagSearch && !TextUtils.isEmpty(tagList)) {
            cbActivityTag.setVisibility(View.GONE);
            tagList = "";
        }
    }

    /**
     * 初始化全部药品popWindow
     */
    private void initPopAllProduct() {
        if (mPopWindowProduct == null) {
            mPopWindowProduct = new AllProductPopWindowV2();
            mPopWindowProduct.setOnSelectListener(new AllProductPopWindowV2.OnSelectListener() {

                @Override
                public void getValue(Set<String> categoryIds) {
                    categoryName = "分类";
                    if (categoryIds == null) return;
                    StringBuilder builder = new StringBuilder();
                    for (String categoryId : categoryIds) {
                        builder.append(categoryId).append(",");
                    }
                    id = builder.toString();
                    if (!TextUtils.isEmpty(id)) {
                        id = id.substring(0, id.length() - 1);
                    }
                }

                @Override
                public void OnDismiss(String multiSelectStr) {
                    mRbAllCategory.setText(TextUtils.isEmpty(categoryName) ? "分类" : categoryName);
                    mRbAllCategory.setActivated(!TextUtils.isEmpty(id));
                    setIconState(mRbAllCategory, R.drawable.manufacturers_def);
                    resetTitleEditSugTag();
                    getSearchData(false);
                    searchAppActionSearchFilterClick(getAppActionSearchFilterClick());
                }
            });
            // 分级点击埋点
            mPopWindowProduct.setOnLevelItemClickListener(searchCategorySelectLevelTrack -> {
                if (searchCategorySelectLevelTrack instanceof SearchCategorySelectLevelTrack.FIRST_LEVEL) {
                    //一级
                    searchCategorySelectLevelTrack.track("search_first_classification");
                } else if (searchCategorySelectLevelTrack instanceof SearchCategorySelectLevelTrack.SECOND_LEVEL) {
                    //二级
                    searchCategorySelectLevelTrack.track("search_second_classification");
                } else if (searchCategorySelectLevelTrack instanceof SearchCategorySelectLevelTrack.THIRD_LEVEL) {
                    //三级
                    searchCategorySelectLevelTrack.track("search_third_classification");
                }
            });
        }
    }

    private String makeMustTagList(boolean isShunfeng, boolean isJD) {
        StringBuilder sb = new StringBuilder();
        if (isShunfeng && isJD) {
            return "YBM_ST_SERV_LOG_SF,YBM_ST_SERV_LOG_JD";
        } else if (isShunfeng) {
            return "YBM_ST_SERV_LOG_SF";
        } else if (isJD) {
            return "YBM_ST_SERV_LOG_JD";
        } else {
            return "";
        }
    }

    /**
     * 筛选一级分类
     */
    private void init1Classify() {
        if (mClassifyPop2 == null) {
            mClassifyPop2 = new FiltrateClassifyPop2(this, !TextUtils.equals("7", spFrom), getPageType());
            if (isShowNearEffective()) {
                mClassifyPop2.showNearEffective();
            }
            mClassifyPop2.setSearchViewModel(searchViewModel);
            mClassifyPop2.hideManufactureTips();
            mClassifyPop2.setListener(new LeftPopWindow.Listener<SearchFilterBean>() {

                @Override
                public void onResult(SearchFilterBean bean) {
                    mDynamicLabelSelectedMap = bean.dynamicLabelResultMap;
                    isAvailable = bean.isAvailable;
                    isPromotion = bean.isPromotion;
                    isDpby = bean.isDpby;
                    isSpellGroupAndPgby = bean.isSpellGroupAndPgby;
                    drugClassification = setDrugsClass(bean);
                    drugClassificationName = setDrugsClassName(bean);
                    priceRangeFloor = !TextUtils.isEmpty(bean.priceRangeFloor) ? bean.priceRangeFloor : "";
                    priceRangeTop = !TextUtils.isEmpty(bean.priceRangeTop) ? bean.priceRangeTop : "";
                    //规格
                    specStr = bean.specSelectedStr;
                    String[] selectedStrArr = specStr.split(",");
                    List<String> selectedList = Arrays.asList(selectedStrArr);
                    if (!selectedList.contains(selectedSugSpec) && isContainsInSpecFilters()) {
                        resetTitleEditSugTag();
                    }
                    //商家
                    selectedShopcodes = bean.shopSelectedStr;
                    selectedShopNames = bean.shopNameSelectedStr;
                    //生产厂家
                    setManufacturer(bean);
                    BaseSearchProductActivity.this.mustTagList = makeMustTagList(bean.isShunfeng, bean.isJD);
                    rbExpress.setActivated(bean.isShunfeng && bean.isJD);
                    isSelectedJD = bean.isJD;
                    isSelectedSF = bean.isShunfeng;
                    isCanUseCoupon = bean.isCanUseCoupon;
                    isSpellGroup = isSpellGroupAndPgby;
                    isDpby = isSpellGroupAndPgby;
                    nearEffective = bean.nearEffective;
                    nearEffectiveStr = bean.nearEffectiveStr;
                    mRbCanUseCoupon.setActivated(isCanUseCoupon);
                    mRbAvailable.setActivated(isAvailable);
                    mRbDpby.setActivated(isDpby);
                    rbSpellGroupAndPgby.setActivated(isSpellGroupAndPgby);
                    setFilterBtnTextColor();
                    resetTitleEditSugTag();
                    getSearchData(false);
                    filterTrack(bean);
                    assembleJgFuwuField(bean);
                    dynamicLabelClick(bean.dynamicLabelConfig, nearEffectiveStr, DYNAMIC_LABEL_STYLE_GRID);
                    mHandler.sendMessage(mHandler.obtainMessage(20));
                }

                @Override
                public void onDismiss() {
                    mHandler.sendMessage(mHandler.obtainMessage(20));
                    searchAppActionSearchFilterClick(getAppActionSearchFilterClick());
                }
            });
        }
        if (!TextUtils.isEmpty(id)) {
//            mClassifyPop2.setDataType("categoryId", id);
            mClassifyPop2.setDataType("categoryIdsStr", id);

        }
        if (!TextUtils.isEmpty(keyword)) {
            mClassifyPop2.setDataType("keyword", keyword);
        }
        mClassifyPop2.setSpec(specStr);
        mClassifyPop2.setSelectedShopCodes(selectedShopcodes);
    }

    private void assembleJgFuwuField(SearchFilterBean bean){
        jgFuwu = "";
        if (bean == null){
            return;
        }

        if (bean.isAvailable){
            jgFuwu += "仅看有货";
        }
        if (bean.isPromotion){
            if (!jgFuwu.isEmpty()){
                jgFuwu += ",";
            }
            jgFuwu += "促销";
        }
        if (bean.isSpellGroupAndPgby){
            if (!jgFuwu.isEmpty()){
                jgFuwu += ",";
            }
            jgFuwu += "拼团包邮";
        }
        if (bean.isJD){
            if (!jgFuwu.isEmpty()){
                jgFuwu += ",";
            }
            jgFuwu += "京东快递";
        }
        if (bean.isShunfeng){
            if (!jgFuwu.isEmpty()){
                jgFuwu += ",";
            }
            jgFuwu += "顺丰快递";
        }
        if (bean.isCanUseCoupon){
            if (!jgFuwu.isEmpty()){
                jgFuwu += ",";
            }
            jgFuwu += "可用券";
        }
    }

    private void resetTitleEditSugTag() {
        titleEt.setText(keyword);
        titleEtMask.setVisibility(View.GONE);
    }

    /**
     * 筛选按钮侧滑框埋点
     *
     * @param bean
     */
    private void filterTrack(SearchFilterBean bean) {
        if (bean == null) return;
        StringBuilder paramsStr = new StringBuilder("");
        if (bean.isAvailable) {
            appendFilterTrackStr(paramsStr, "in_stock");
        }
        if (bean.isPromotion) {
            appendFilterTrackStr(paramsStr, "with_promotion");
        }
        if (!TextUtils.isEmpty(bean.priceRangeFloor) || !TextUtils.isEmpty(bean.priceRangeTop)) {
            appendFilterTrackStr(paramsStr, "price_range");
        }
        if (bean.isJD) {
            appendFilterTrackStr(paramsStr, "JD");
        }
        if (bean.isShunfeng) {
            appendFilterTrackStr(paramsStr, "SF");
        }
        if (bean.isClassA) {
            appendFilterTrackStr(paramsStr, "drug_type_A");
        }
        if (bean.isClassB) {
            appendFilterTrackStr(paramsStr, "drug_type_B");
        }
        if (bean.isClassRx) {
            appendFilterTrackStr(paramsStr, "drug_type_RX");
        }
        if (bean.isClassElse) {
            appendFilterTrackStr(paramsStr, "drug_type_other");
        }
        if (!TextUtils.isEmpty(specStr)) {
            appendFilterTrackStr(paramsStr, "specs");
        }
        if (!TextUtils.isEmpty(selectedShopcodes)) {
            appendFilterTrackStr(paramsStr, "shops");
        }
        if (!lastNames.isEmpty()) {
            appendFilterTrackStr(paramsStr, "manufactors");
        }
        if (!TextUtils.isEmpty(paramsStr.toString())) {
            HashMap<String, String> trackParams = new HashMap<>();
            trackParams.put("item", paramsStr.toString());
            XyyIoUtil.track("search_quick_filter", trackParams);
        }
    }

    private void appendFilterTrackStr(StringBuilder acc, String appendStr) {
        if (acc == null) return;
        if (!TextUtils.isEmpty(acc.toString())) {
            acc.append(",");
        }
        acc.append(appendStr);
    }

    /**
     * 厂家(非ka)弹出pop
     */
    private void initManufacturerPop() {
        if (mPopManufacturer == null) {
            mPopManufacturer = new Manufacturers2Pop(masterStandardProductId, getManufacturersUrl(), getManufacturersParams());
            mPopManufacturer.setSearchScene(getSearchScene());
            mPopManufacturer.setOnSelectListener(new BaseFilterPopWindow.OnSelectListener() {
                @Override
                public void getValue(final SearchFilterBean bean) {
                    resetTitleEditSugTag();
                    setManufacturer(bean);
                    getSearchData(false);
                    String trackStr = null;
                    if (bean != null && bean.lastNames != null) {
                        for (String name : bean.lastNames) {
                            if (trackStr == null) {
                                trackStr = name;
                            } else {
                                trackStr = trackStr + "," + name;
                            }
                        }
                    }
                    if (!TextUtils.isEmpty(trackStr)) {
                        HashMap<String, String> trackParams = new HashMap<>();
                        trackParams.put("manufactors", trackStr);
                        XyyIoUtil.track("search_manufactor_filter", trackParams);
                    }
                    mHandler.sendMessage(mHandler.obtainMessage(20));
                }

                @Override
                public void OnDismiss(String multiSelectStr) {
                    setManufacturersState();
                    searchAppActionSearchFilterClick(getAppActionSearchFilterClick());
                }
            });
        }
    }

    /**
     * 设置厂家筛选后各个按钮的状态
     */
    private void setManufacturersState() {
        mIsManufacturer = lastNames != null && lastNames.size() > 0;
        mTvManufacturer.setActivated(mIsManufacturer);
        setIconState(mTvManufacturer, R.drawable.manufacturers_def);
        mHandler.sendMessage(mHandler.obtainMessage(20));
    }

    /**
     * 设置箭头指示状态 →，→
     *
     * @param tv
     * @param id
     */
    private void setIconState(TextView tv, int id) {
        try {
            if (tv == null) return;
            Drawable nav_up = getResources().getDrawable(id);
            nav_up.setBounds(0, 0, nav_up.getMinimumWidth(), nav_up.getMinimumHeight());
            tv.setCompoundDrawables(null, null, nav_up, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置可见
     *
     * @param id
     */
    public void setActivated(int id) {
        mRbAllCategory.setActivated(R.id.rb_all_category == id);
//        mRbComprehensiveRanking.setActivated(R.id.rb_comprehensive_ranking == id);
    }

    /**
     * 设置厂家过来的数据
     *
     * @param bean
     */
    private void setManufacturer(SearchFilterBean bean) {
        if (lastNames == null) {
            lastNames = new ArrayList<>();
        }
        lastNames.clear();
        lastNames.addAll(bean.lastNames);
        if (lastNames.isEmpty()) {
            mTvManufacturer.setActivated(false);
            setIconState(mTvManufacturer, R.drawable.manufacturers_def);
        }
//        if (mClassifyPop != null) {
//            mClassifyPop.setLastNames(lastNames);
//        }
        StringBuilder sb = new StringBuilder();
        if (lastNames != null && lastNames.size() > 0) {
            for (int i = 0; i < lastNames.size(); i++) {
                sb.append(lastNames.get(i)).append("*");
            }
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }
        }
        manufacturer = sb.toString();
        mIsManufacturer = lastNames != null && lastNames.size() > 0;
        mTvManufacturer.setActivated(mIsManufacturer);
        setIconState(mTvManufacturer, R.drawable.manufacturers_def);
    }

    /**
     * 拼接药品经营类型
     *
     * @param bean
     * @return
     */
    private String setDrugsClass(SearchFilterBean bean) {
        isClassA = bean.isClassA;
        isClassB = bean.isClassB;
        isClassRx = bean.isClassRx;
        isClassElse = bean.isClassElse;
        StringBuilder sb = new StringBuilder();
        if (isClassA) {
            sb.append("1").append(",");
        }
        if (isClassB) {
            sb.append("2").append(",");
        }
        if (isClassRx) {
            sb.append("3").append(",");
        }
        if (isClassElse) {
            sb.append("4").append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * 拼接药品经营类型
     *
     * @param bean
     * @return
     */
    private String setDrugsClassName(SearchFilterBean bean) {
        isClassA = bean.isClassA;
        isClassB = bean.isClassB;
        isClassRx = bean.isClassRx;
        isClassElse = bean.isClassElse;
        StringBuilder sb = new StringBuilder();
        if (isClassA) {
            sb.append("甲类OTC").append(",");
        }
        if (isClassB) {
            sb.append("乙类OTC").append(",");
        }
        if (isClassRx) {
            sb.append("处方药RX").append(",");
        }
        if (isClassElse) {
            sb.append("其他").append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * 搜索状态发生变化重置
     */
    private void resetShop() {
        if (mClassifyPop2 == null) {
            return;
        }
        mClassifyPop2.reset(true);
        if (lastNames == null) {
            lastNames = new ArrayList<>();
        }
        lastNames.clear();
        manufacturer = "";

        isClassA = false;
        isClassB = false;
        isClassRx = false;
        isClassElse = false;

        isAvailable = false;
        isPromotion = false;

        mIsManufacturer = false;

        nearEffective = "";
        nearEffectiveStr = "";
        drugClassification = "";
        drugClassificationName = "";
        priceRangeFloor = "";
        priceRangeTop = "";
        jgFuwu = "";
        //        id = "";
        //        categoryName = "";
        //        mRbAllCategory.setText("全部分类");
        mTvShop.setActivated(false);
        mTvShop.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        mRbAvailable.setActivated(isAvailable);
        mRbPromotion.setActivated(isPromotion);
    }

    @SuppressLint("HandlerLeak")
    Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == 20) {
                if (mTvShop == null) {
                    return;
                }

                boolean isAll = isAvailable || isPromotion
                        || !TextUtils.isEmpty(priceRangeFloor) || !TextUtils.isEmpty(priceRangeTop) || isSelectedJD || isSelectedSF || isCanUseCoupon
                        || isClassA || isClassB || isClassRx || isClassElse || isSpellGroupAndPgby
                        || !TextUtils.isEmpty(specStr) || !TextUtils.isEmpty(selectedShopcodes) || (lastNames != null && !lastNames.isEmpty()) || !TextUtils.isEmpty(nearEffective) || isSelectedItemInDynamicLabel();
                mTvShop.setActivated(isAll);
                mTvShop.setTypeface(isAll ? Typeface.defaultFromStyle(Typeface.BOLD) : Typeface.defaultFromStyle(Typeface.NORMAL));
            }
        }
    };

    /**
     * 清空历史记录
     */
    private void delHistoryData() {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        HttpManager.getInstance().post(AppNetConfig.DEL_HISTORY_HOT_SEARCH_LIST, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean emptyBean) {
                if (obj != null) {
                    if (obj.isSuccess()) {
                        history.clear();
                        flexBoxHistory.clear();
                        llHistory.setVisibility(history.size() == 0 ? View.GONE : View.VISIBLE);
                    }
                }
            }
        });
    }


    protected RequestParams searchMoreParams;


    /**
     * 请求搜索数据
     */
    protected void getSearchData() {
        getSearchData(true);
    }

    private void getSearchData(boolean needSearchReport){
        if (isDpby) {
            XyyIoUtil.track("action_Search_FreeShipping_outside");
        }
        titleEt.clearFocus();

        if (mRbAllCategory == null) {
            return;
        }
        if (TextUtils.isEmpty(keyword) && TextUtils.isEmpty(id) && !isFromOftenBuy() && !isOrderBundling()) {
            ToastUtils.showShort("搜索内容不能为空");
            return;
        }
        popDismiss();
        suggestPopWindow.cancelHandler(true);

        String filter = id + keyword;
        if (!TextUtils.isEmpty(lastFilter) && !TextUtils.equals(filter, lastFilter)) {//搜索条件变化重置厂家
            resetShop();
        }
        lastFilter = filter;
        //隐藏软键盘
        hideSoftInput();
        showProgress();
        preKeyWordTrack = keyword;
        getSearchDataPost();
        getAggsDataPost();
        initManufacturerPop();
        mPopManufacturer.setDataType(id, keyword, isAvailable, isPromotion, isClassA
                , isClassB, isClassRx, isClassElse, priceRangeFloor, priceRangeTop, lastNames);

        mPopManufacturer.setLoadedDataCallback(new Manufacturers2Pop.OnLoadedDataCallback() {
            @Override
            public void onLoadedData(List<ManufacturersBean> data) {
                mManufacturerListData = data;
            }
        });
        mPopManufacturer.setRequestParamsV2(getManufacturersParams());
        mPopManufacturer.getData(false);

        //请求搜索如果是从中间页过来到结果页就埋点pageView
        if (!isSearchProductListViewVisibility()){
            jgTrackPageView(true);
        }
        jgFilterTrack();
        if (needSearchReport) {
            if (selectedSearchPosition != -1){
                searchReport(getMJgspid(),keyword,selectedSearchPosition,null);
            }else {
                searchReport(getMJgspid(),keyword,null,null);
            }
        }
        selectedSearchPosition = -1;
    }

    // 选中的活动 tagid
    private String activityScreenTagId;
    // 选中活动时的按钮文案
    private String selectActivityScreenTagTitle;
    // 未选中活动时的按钮文案
    private String unSelectActivityScreenTagTitle;

    /**
     * 根据返回数据设置活动筛选项
     *
     * @param rowsBeans
     */
    protected void handleActivityShowType(BaseSearchResultBean rowsBeans) {
        if (TextUtils.isEmpty(rowsBeans.activityScreenTagId) || TextUtils.isEmpty(rowsBeans.selectActivityScreenTagTitle) || TextUtils.isEmpty(rowsBeans.unSelectActivityScreenTagTitle)) {
            cbActivityTag.setVisibility(View.GONE);
        } else {
            XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH_BIG_PROMOTION_CLICK);
            if (!isFromOftenBuy()) {
                cbActivityTag.setVisibility(View.VISIBLE);
            }
            activityScreenTagId = rowsBeans.activityScreenTagId;
            selectActivityScreenTagTitle = rowsBeans.selectActivityScreenTagTitle;
            unSelectActivityScreenTagTitle = rowsBeans.unSelectActivityScreenTagTitle;

            if (!TextUtils.isEmpty(tagList) && !TextUtils.isEmpty(rowsBeans.activityScreenTagId)) {
                cbActivityTag.setChecked(true);
            } else {
                cbActivityTag.setChecked(false);
            }


            if (!cbActivityTag.isChecked()) {
                cbActivityTag.setText(unSelectActivityScreenTagTitle);
            }
        }
    }

    protected void updateSearchData(boolean isFirst, BaseSearchResultBean rowsBeans, Boolean isFromSearchProductOPActivity) {
        updateSearchData(isFirst, rowsBeans, isFromSearchProductOPActivity,false);
    }

    /**
     * 请求搜索后的数据更新
     *
     * @param needLimitFullDiscountActInfo 是否需要限时加补信息（专区搜索也会返对应信息 但是不显示）
     */
    protected void updateSearchData(boolean isFirst, BaseSearchResultBean rowsBeans, Boolean isFromSearchProductOPActivity,Boolean needLimitFullDiscountActInfo) {
        searchMoreParams = rowsBeans.getRequestParams();

        setJGPageListCommon(isFirst, rowsBeans, searchMoreParams, isFromSearchProductOPActivity);
        searchProductListBuild(getPageListBuild(isFirst, rowsBeans, searchMoreParams, isFromSearchProductOPActivity));
        // notifyDataChanged 之前告知Adapter是否为加载更多
        if(detailAdapter instanceof SearchAdapter){
            ((SearchAdapter)detailAdapter).setLoadMoreFlag(!isFirst);
        }
        if (rowsBeans instanceof SearchResultBean) {
            AdapterUtils.INSTANCE.addLocalTimeForRows(((SearchResultBean) rowsBeans).rows);
            AdapterUtils.INSTANCE.notifyAndControlLoadmoreStatus(((SearchResultBean) rowsBeans).rows, detailAdapter, isFirst, rowsBeans.isEnd);
            // 请求并更新折后价
            AdapterUtils.INSTANCE.getAfterDiscountPrice(((SearchResultBean) rowsBeans).rows, detailAdapter);
        } else if (rowsBeans instanceof SearchResultOPBean) {
            //带运营位的
            AdapterUtils.INSTANCE.notifyAndControlLoadmoreStatus(((SearchResultOPBean) rowsBeans).rows, detailAdapter, isFirst, rowsBeans.isEnd);
            // 请求并更新折后价
            try {
                List<RowsBean> rows = new ArrayList<>();
                for (SearchRowsBean row : ((SearchResultOPBean) rowsBeans).rows) {
                    if (needLimitFullDiscountActInfo){
                        row.setNeedLimitFullDiscountActInfo(true);
                    }
                    if (row.getProductInfo()!=null) {
                        if (row.getCardType() == SEARCH_LIST_CARD_TYPE_GOODS) {
                            RowsBean productInfo = row.getProductInfo();
                            productInfo.searchKeyword = keyword;
                            productInfo.searchSortStrategyCode = rowsBeans.searchSortStrategyCode;
                            rows.add(productInfo);
                        }
                        if (row.getCardType() == SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS) {
                            try {
                                RowsBean rowsBean = row.getOperationInfo().getProducts().get(0);
                                rowsBean.searchKeyword = keyword;
//                                rowsBean.isOPSingleGoods = true;
                                rowsBean.activityPageId = row.getOperationInfo().getActivityPageId();
                                rows.add(rowsBean);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    row.setKeyword(keyword);
                }
                AdapterUtils.INSTANCE.getAfterDiscountPrice(rows, detailAdapter);
            } catch (Exception e) {
                e.printStackTrace();
            }

            for (SearchRowsBean row : ((SearchResultOPBean) rowsBeans).rows) {
                if (row.getCardType() == SEARCH_LIST_CARD_TYPE_OPERATION_POSITION && row.getOperationInfo()!=null && row.getOperationInfo().getProducts()!=null) {
                    List<RowsBean> products = row.getOperationInfo().getProducts();
                    for (RowsBean product : products) {
                        product.searchKeyword = keyword;
                    }
                    AdapterUtils.INSTANCE.addLocalTimeForRows(products);
                } else if (row.getCardType() == SEARCH_LIST_CARD_TYPE_GOODS && row.getProductInfo()!=null) {
                    List<RowsBean> list = new ArrayList<>();
                    RowsBean productInfo = row.getProductInfo();
                    productInfo.searchKeyword = keyword;
                    list.add(productInfo);
                    AdapterUtils.INSTANCE.addLocalTimeForRows(list);
                }
                else if(SEARCH_LIST_CARD_TYPE_COMBINATIONBUY_SINGLE == row.getCardType()){
                    if(row.getGroupPurchaseInfo() != null){
                        GroupPurchaseInfoKt.initGroup(row.getGroupPurchaseInfo());
                        AdapterUtils.INSTANCE.getGroupPurchaseInfo(row.getGroupPurchaseInfo(),0,detailAdapter,true,-1,true,null);
                    }
                }else if(SEARCH_LIST_CARD_TYPE_COMBINATIONBUY_MULTI == row.getCardType()){
                    if(row.getAdditionalPurchaseInfo()!= null){
                        GroupPurchaseInfoKt.initAdditonal(row.getAdditionalPurchaseInfo());
                        AdapterUtils.INSTANCE.getAdditionalPurchaseInfo(row.getAdditionalPurchaseInfo(),detailAdapter,true,-1,true,null);
                    }
                }
            }
        }

        // 检查是否需要显示配送时效引导
        if (isFirst) {
            checkAndShowDeliveryGuide();
        }
    }

    /**
     * 检查并显示配送时效引导
     */
    private void checkAndShowDeliveryGuide() {
        // 检查是否有配送时效相关的筛选选项（京东/顺丰按钮是否可见）
        boolean hasDeliveryOptions = rbExpress != null && rbExpress.getVisibility() == View.VISIBLE;

        // 检查是否应该显示引导
//        if (SearchGuideManager.INSTANCE.shouldShowDeliveryGuide(this, hasDeliveryOptions)) {
            // 延迟显示引导，确保布局已完成
            mBrandRg02.post(() -> {
                if (deliveryGuideView != null && mBrandRg02 != null) {
                    deliveryGuideView.showGuide(mBrandRg02);
                }
            });
//        }
    }

    private void setJGPageListCommon(boolean isFirst, BaseSearchResultBean rowsBeans, RequestParams requestParams, Boolean isFromSearchProductOPActivity) {
        int resultCnt = rowsBeans.totalCount;
        int pageNum = 0;
        int pageSize = 0;
        if (isFromSearchProductOPActivity) { //新搜索页
            pageNum = rowsBeans.pageNo;
            pageSize = rowsBeans.pageSize;
        } else {
            try {
                pageNum = Math.max(1,Integer.parseInt(requestParams.getParamsMap().get("pageNum"))-1); //这是下一页的页码 所以要减一
                pageSize = Integer.parseInt(requestParams.getParamsMap().get("pageSize"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        JGPageListCommonBean jgPageListCommonBean = new JGPageListCommonBean();
        jgPageListCommonBean.setSptype(rowsBeans.sptype);
        jgPageListCommonBean.setJgspid(getMJgspid());
        jgPageListCommonBean.setSid(rowsBeans.sid);
        jgPageListCommonBean.setResult_cnt(resultCnt);
        jgPageListCommonBean.setPage_no(pageNum);
        jgPageListCommonBean.setPage_size(pageSize);
        jgPageListCommonBean.setTotal_page(rowsBeans.totalPage);
        jgPageListCommonBean.setKey_word(keyword);

        if (detailAdapter instanceof SearchAdapter) {
            SearchAdapter adapter =  (SearchAdapter) detailAdapter;
            adapter.setJGPageListCommonBean(jgPageListCommonBean);
        } else if (detailAdapter instanceof GoodListAdapterNew) {
            GoodListAdapterNew adapter = (GoodListAdapterNew) detailAdapter;
            adapter.setJGPageListCommonBean(jgPageListCommonBean);
        }
    }

    private PageListBuild getPageListBuild(boolean isFirst, BaseSearchResultBean rowsBeans, RequestParams requestParams, Boolean isFromSearchProductOPActivity) {

        try {
            int resultCnt = rowsBeans.totalCount;
            int pageNum = 0;
            int pageSize = 0;
            if (isFromSearchProductOPActivity) { //新搜索页
                pageNum = rowsBeans.pageNo;
                pageSize = rowsBeans.pageSize;
            } else {
                try {
                    pageNum = Math.max(1,Integer.parseInt(requestParams.getParamsMap().get("pageNum"))-1); //这是下一页的页码 所以要减一
                    pageSize = Integer.parseInt(requestParams.getParamsMap().get("pageSize"));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            String paixu = "默认";
            if (searchFilterSynthesizeBean != null) {
                switch (searchFilterSynthesizeBean.selectedSearchOption) {
                    case SearchFilterBean.SALESVOLUME:
                        paixu = "销量由高到低";
                        break;
                    case SearchFilterBean.PRICE:
                        paixu = "价格由低到高";
                        break;
                    default:
                        paixu = "默认";
                        break;
                }
            }
            ArrayList<String> manufacturerArr = new ArrayList<>();
            ArrayList<String> selectedShopcodesArr = new ArrayList<>();
            ArrayList<String> specStrArr = new ArrayList<>();
            ArrayList<String> drugClassificationArr = new ArrayList<>();
            ArrayList<String> categoryIdsArr = new ArrayList<>();
            if (manufacturer!=null){
                manufacturerArr = new ArrayList<>(Arrays.asList(manufacturer.split("\\*")));
            }
            if (selectedShopNames != null){
                selectedShopcodesArr = new ArrayList<>(Arrays.asList(selectedShopNames.split(",")));
            }
            if (specStr != null){
                specStrArr = new ArrayList<>(Arrays.asList(specStr.split(",")));
            }
            if (drugClassificationName != null){
                drugClassificationArr = new ArrayList<>(Arrays.asList(drugClassificationName.split(",")));
            }
            if (id != null ){
                categoryIdsArr = new ArrayList<>(Arrays.asList(id.split(",")));
            }
            Double minPrice = null;
            Double maxPrice = null;
            try {
                minPrice = Double.parseDouble(priceRangeFloor);
                maxPrice = Double.parseDouble(priceRangeTop);
            }catch (Exception e){
                e.printStackTrace();
            }

            ArrayList<String> tagFilterArr = new ArrayList<>();
            ArrayList<String> tagFilterTypeArr = new ArrayList<>();
            for (SearchDynamicLabelConfig searchDynamicLabelConfig:dynamicLabelView.getMDataList()) {
                if (searchDynamicLabelConfig.getSelectedStatus()){
                    tagFilterArr.add(searchDynamicLabelConfig.getLabelName());
                    tagFilterTypeArr.add(searchDynamicLabelConfig.getLabelGroupKey());
                }
            }

            int isFilter = 0;
            //没数据时 集合会有一个空串的
            if ((manufacturerArr.size()>0 && !manufacturerArr.get(0).isEmpty())
                    || (selectedShopcodesArr.size()>0 && !selectedShopcodesArr.get(0).isEmpty())
                    || (specStrArr.size()>0 && !specStrArr.get(0).isEmpty())
                    || (drugClassificationArr.size()>0 &&  !drugClassificationArr.get(0).isEmpty())
                    || (categoryIdsArr.size()>0 && !categoryIdsArr.get(0).isEmpty())
                    || (nearEffectiveStr!=null &&!nearEffectiveStr.isEmpty())
                    || (minPrice != null)
                    || (maxPrice != null)
                    || (tagFilterArr.size()>0 && !tagFilterArr.get(0).isEmpty())
                    || (tagFilterTypeArr.size()>0 && !tagFilterTypeArr.get(0).isEmpty())){
                isFilter = 1;
            }

            PageListBuild pageListBuild = new PageListBuild();
            pageListBuild.setUrl(JGTrackManager.TrackSearchResult.TRACK_URL);
            pageListBuild.setTitle(JGTrackManager.TrackSearchResult.TITLE);
            pageListBuild.setReferrer(JGTrackManager.TrackSearchResult.TRACK_URL);
            pageListBuild.setSptype(rowsBeans.sptype);
            pageListBuild.setJgspid(getMJgspid());
            pageListBuild.setSid(rowsBeans.sid);
            pageListBuild.setResult_cnt(resultCnt);
            pageListBuild.setPage_no(pageNum);
            pageListBuild.setPage_size(pageSize);
            pageListBuild.setTotal_page(rowsBeans.totalPage);
            pageListBuild.setKey_word(keyword);
            pageListBuild.setSearch_sort_strategy_id(rowsBeans.searchSortStrategyCode);
            pageListBuild.setPaixu(paixu);
            pageListBuild.setFilter(isFilter); //没有过滤
            pageListBuild.setChangjia(manufacturerArr);
            pageListBuild.setShangjia(selectedShopcodesArr);
            pageListBuild.setGuige(specStrArr);
            pageListBuild.setYplx(drugClassificationArr);
            pageListBuild.setPeriod_validity(nearEffectiveStr);
            pageListBuild.setFenlei(categoryIdsArr);
            pageListBuild.setMinprice(minPrice);
            pageListBuild.setMaxprice(maxPrice);
            pageListBuild.setTag_filter(tagFilterArr);
            pageListBuild.setTag_filter_type(tagFilterTypeArr);
            return pageListBuild;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    protected String getIsFilter(){
        ArrayList<String> manufacturerArr = new ArrayList<>();
        ArrayList<String> selectedShopcodesArr = new ArrayList<>();
        ArrayList<String> specStrArr = new ArrayList<>();
        ArrayList<String> drugClassificationArr = new ArrayList<>();
        ArrayList<String> categoryIdsArr = new ArrayList<>();
        if (manufacturer!=null){
            manufacturerArr = new ArrayList<>(Arrays.asList(manufacturer.split("\\*")));
        }
        if (selectedShopNames != null){
            selectedShopcodesArr = new ArrayList<>(Arrays.asList(selectedShopNames.split(",")));
        }
        if (specStr != null){
            specStrArr = new ArrayList<>(Arrays.asList(specStr.split(",")));
        }
        if (drugClassificationName != null){
            drugClassificationArr = new ArrayList<>(Arrays.asList(drugClassificationName.split(",")));
        }
        if (id != null ){
            categoryIdsArr = new ArrayList<>(Arrays.asList(id.split(",")));
        }
        Double minPrice = null;
        Double maxPrice = null;
        try {
            minPrice = Double.parseDouble(priceRangeFloor);
            maxPrice = Double.parseDouble(priceRangeTop);
        }catch (Exception e){
            e.printStackTrace();
        }

        ArrayList<String> tagFilterArr = new ArrayList<>();
        ArrayList<String> tagFilterTypeArr = new ArrayList<>();
        for (SearchDynamicLabelConfig searchDynamicLabelConfig:dynamicLabelView.getMDataList()) {
            if (searchDynamicLabelConfig.getSelectedStatus()){
                tagFilterArr.add(searchDynamicLabelConfig.getLabelName());
                tagFilterTypeArr.add(searchDynamicLabelConfig.getLabelGroupKey());
            }
        }

        int isFilter = 0;
        //没数据时 集合会有一个空串的
        if ((manufacturerArr.size()>0 && !manufacturerArr.get(0).isEmpty())
                || (selectedShopcodesArr.size()>0 && !selectedShopcodesArr.get(0).isEmpty())
                || (specStrArr.size()>0 && !specStrArr.get(0).isEmpty())
                || (drugClassificationArr.size()>0 &&  !drugClassificationArr.get(0).isEmpty())
                || (categoryIdsArr.size()>0 && !categoryIdsArr.get(0).isEmpty())
                || (nearEffectiveStr!=null &&!nearEffectiveStr.isEmpty())
                || (minPrice != null)
                || (maxPrice != null)
                || (tagFilterArr.size()>0 && !tagFilterArr.get(0).isEmpty())
                || (tagFilterTypeArr.size()>0 && !tagFilterTypeArr.get(0).isEmpty())){
            isFilter = 1;
        }
        return String.valueOf(isFilter);
    }

    private AppActionSearchFilterClick getAppActionSearchFilterClick(){
        try {
            String paixu = "默认";
            if (searchFilterSynthesizeBean != null){
                switch (searchFilterSynthesizeBean.selectedSearchOption){
                    case SearchFilterBean.SALESVOLUME:
                        paixu = "销量由高到低";
                        break;
                    case SearchFilterBean.PRICE:
                        paixu = "价格由低到高";
                        break;
                    default:
                        paixu = "默认";
                        break;
                }
            }

            ArrayList<String> manufacturerArr = new ArrayList<>();
            ArrayList<String> selectedShopcodesArr = new ArrayList<>();
            ArrayList<String> specStrArr = new ArrayList<>();
            ArrayList<String> drugClassificationArr = new ArrayList<>();
            ArrayList<String> categoryIdsArr = new ArrayList<>();
            if (manufacturer != null){
                manufacturerArr = new ArrayList<>(Arrays.asList(manufacturer.split("\\*")));
            }
            if (selectedShopNames != null){
                selectedShopcodesArr = new ArrayList<>(Arrays.asList(selectedShopNames.split(",")));
            }
            if (specStr != null){
                specStrArr = new ArrayList<>(Arrays.asList(specStr.split(",")));
            }
            if (drugClassificationName != null){
                drugClassificationArr = new ArrayList<>(Arrays.asList(drugClassificationName.split(",")));
            }
            if (id != null){
                categoryIdsArr = new ArrayList<>(Arrays.asList(id.split(",")));
            }

            Double minPrice = null;
            Double maxPrice = null;
            try {
                minPrice = Double.parseDouble(priceRangeFloor);
                maxPrice = Double.parseDouble(priceRangeTop);
            }catch (Exception e){
                e.printStackTrace();
            }

            AppActionSearchFilterClick appActionSearchFilterClick = new AppActionSearchFilterClick();
            appActionSearchFilterClick.setUrl(JGTrackManager.TrackSearchResult.TRACK_URL);
            appActionSearchFilterClick.setTitle(JGTrackManager.TrackSearchResult.TITLE);
            appActionSearchFilterClick.setReferrer(JGTrackManager.TrackSearchResult.TRACK_URL);
            appActionSearchFilterClick.setKey_word(keyword);
            appActionSearchFilterClick.setPaixu(paixu);
            appActionSearchFilterClick.setChangjia(manufacturerArr);
            appActionSearchFilterClick.setShangjia(selectedShopcodesArr);
            appActionSearchFilterClick.setGuige(specStrArr);
            appActionSearchFilterClick.setYplx(drugClassificationArr);
            appActionSearchFilterClick.setPeriod_validity(nearEffectiveStr);
            appActionSearchFilterClick.setFenlei(categoryIdsArr);
            appActionSearchFilterClick.setMinprice(minPrice);
            appActionSearchFilterClick.setMaxprice(maxPrice);

            return appActionSearchFilterClick;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 隐藏推荐搜索和历史记录
     */
    protected void popDismiss() {
        if (suggestPopWindow != null) {
            suggestPopWindow.dismiss();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (mClassifyPop2 != null && mClassifyPop2.isShow()) {
            mClassifyPop2.dismissPop();
        }
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        isScroll(event);
        // 购物车显示时再判断动画是否执行
        if (rlCart != null && rlCart.getVisibility() == View.VISIBLE) {
            // 监听触摸事件控制悬浮购物车
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN://手指按下
                    if (System.currentTimeMillis() - upTime < 1000) {
                        //本次按下距离上次的抬起小于1s时，取消Timer
                        timer.cancel();
                    }
                    startY = event.getY();
                    break;
                case MotionEvent.ACTION_MOVE://手指滑动
                    if (Math.abs(startY - event.getY()) > 10) {
                        if (isShowFloatImage) {
                            hideFloatImage(moveDistance);
                        }
                    }
                    startY = event.getY();
                    break;
                case MotionEvent.ACTION_UP://手指抬起
                    if (!isShowFloatImage) {
                        //抬起手指1s后再显示悬浮按钮
                        //开始1s倒计时
                        upTime = System.currentTimeMillis();
                        timer = new Timer();
                        timer.schedule(new TimerTask() {
                            @Override
                            public void run() {
                                runOnUiThread(() -> showFloatImage(moveDistance));
                            }
                        }, 1000);
                    }
                    break;
                default:
                    break;
            }
        }
        return super.dispatchTouchEvent(event);
    }

    /**
     * 隐藏悬浮购物车
     *
     * @param distance
     */
    private void hideFloatImage(int distance) {
        isShowFloatImage = false;
        //位移动画
        TranslateAnimation ta = new TranslateAnimation(0, distance, 0, 0);
        ta.setDuration(300);
        //渐变动画
        AlphaAnimation al = new AlphaAnimation(1f, 0f);
        al.setDuration(300);
        AnimationSet set = new AnimationSet(true);
        //动画完成后不回到原位
        set.setFillAfter(true);
        set.addAnimation(ta);
        set.addAnimation(al);
        if (rlCart != null) {
            rlCart.startAnimation(set);
        }
    }

    private String guidKey = "";//记录命中词的关键字

    /**
     * 显示悬浮购物车
     *
     * @param distance
     */
    private void showFloatImage(int distance) {
        isShowFloatImage = true;
        //位移动画
        TranslateAnimation ta = new TranslateAnimation(distance, 0, 0, 0);
        ta.setDuration(300);
        //渐变动画
        AlphaAnimation al = new AlphaAnimation(0f, 1f);
        al.setDuration(300);
        AnimationSet set = new AnimationSet(true);
        //动画完成后不回到原位
        set.setFillAfter(true);
        set.addAnimation(ta);
        set.addAnimation(al);
        if (rlCart != null) {
            rlCart.startAnimation(set);
        }
    }

    private void setRightDrawable(TextView textView, int drawableRes) {
        Drawable drawable = getResources().getDrawable(drawableRes);
        // 这一步必须要做,否则不会显示.
        drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
        textView.setCompoundDrawables(null, null, drawable, null);
    }

    @Override
    public void showSoftInput() {
        super.showSoftInput();
        titleEt.requestFocus();
        titleEt.setFocusable(true);
        titleEt.setFocusableInTouchMode(true);
    }

    @Override
    public void handleLicenseStatusChange(int status) {
        getSearchData();
    }

    @Override
    public boolean onLicenseStatusEnable() {
        return true;
    }

    /**
     * 处理无结果query截断召回和截断后仍无结果兜底召
     */
    protected void handleNoQuery(BaseSearchResultBean rowsBeans) {
        List<String> hotKeyWordListAll = rowsBeans.wordList;
        setIsRecommendList(false);
        int type = rowsBeans.type;
        if (type == SEARCH_TYPE_NORMAL) {
            search_guid_layout_by_layout.setVisibility(View.GONE);
            return;
        } else {//处理没有搜到数据时 专区搜索和全站搜索的显示
            if (!isFromOftenBuy()) {
                search_guid_layout_by_layout.setVisibility(View.VISIBLE);
            }
            llTagNoMore.setVisibility(isTagSearch ? View.VISIBLE : View.GONE);
            tvHightLightKeyword.setVisibility(!isTagSearch ? View.VISIBLE : View.GONE);
        }
        hotKeyWordList.clear();
        if (type == SEARCH_TYPE_TRUANCATION) {
            clRecommentHeader.setVisibility(View.GONE);
            if (hotKeyWordListAll != null && !hotKeyWordListAll.isEmpty()) {
                recommendKeyWord = hotKeyWordListAll.get(0);
            } else {
                recommendKeyWord = "";
            }
            ForegroundColorSpan colorSpan = new ForegroundColorSpan(getResources().getColor(R.color.color_00b377));
            String preStr = "抱歉，没有找到商品，为您推荐";
            SpannableStringBuilder builder = new SpannableStringBuilder(preStr).append("“").append(recommendKeyWord).append("”")
                    .append("下搜索结果");
            builder.setSpan(colorSpan, preStr.length(), preStr.length() + recommendKeyWord.length() + 2, Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
            tvHightLightKeyword.setText(builder);
            tv_hot_keyword_list_head.setText("试试：");
            if (hotKeyWordListAll != null && hotKeyWordListAll.size() >= 2)
                hotKeyWordList.addAll(hotKeyWordListAll.subList(1, hotKeyWordListAll.size()));
        } else if (type == SEARCH_TYPE_OUT) {
            clRecommentHeader.setVisibility(View.VISIBLE);
            setIsRecommendList(true);
            if ((!TextUtils.isEmpty(keyword) && keyword.matches("69\\d{11,}"))) {
                tvHightLightKeyword.setText("抱歉，没有找到商品，请尝试搜索商品名称");
            } else {
                tvHightLightKeyword.setText("抱歉，没有找到商品");
            }
            tv_hot_keyword_list_head.setText("您试试热搜词：");
            if (hotKeyWordListAll != null) hotKeyWordList.addAll(hotKeyWordListAll);
        }
        if (hotKeyWordList.isEmpty()) {
            llHotKey.setVisibility(View.GONE);
            return;
        } else {
            llHotKey.setVisibility(!isTagSearch ? View.VISIBLE : View.GONE);
        }
        crvHotKeyword.setTags(hotKeyWordList);
        crvHotKeyword.setOnTagClickListener(new TagView.OnTagClickListener() {
            @Override
            public void onTagClick(int position, String text) {
                keyword = text;
                titleEt.setText(keyword);
                if (type == SEARCH_TYPE_TRUANCATION) {
                    getSearchData();
                } else if (type == SEARCH_TYPE_OUT) {
                    getSearchData();
                }
            }

            @Override
            public void onTagLongClick(int position, String text) {

            }

            @Override
            public void onSelectedTagDrag(int position, String text) {

            }

            @Override
            public void onTagCrossClick(int position, String text) {

            }
        });
        if (isFromOftenBuy()) {
            llFromOftenBuy.setVisibility(View.VISIBLE);
        }
    }

    protected boolean isFromOftenBuy() {
        return TextUtils.equals("1", isOftenBuyFrom);
    }

    protected boolean isShowCart() {
        return TextUtils.equals("1", isShowCart);
    }
    private String getTrackSugFromSpid() {
        return fromH5 ? "3" : "1";
    }

    protected RequestParams getAggsParams(boolean isLoadMore) {
        RequestParams aggParams = getParams(false);
        aggParams.getParamsMap().remove("spec");
        aggParams.getParamsMap().remove("specs");
        aggParams.getParamsMap().remove("shopCodes");
        aggParams.getParamsMap().remove("nearEffect");
        if (isCanUseCoupon) {
            aggParams.put("isAvailableCoupons", "1");
        }
//        if (isDpby) {
//            HashMap<String, String> mapParams = new HashMap<>();
//            mapParams.put("tag", "单品包邮");
//            XyyIoUtil.track("action_Search_FreeShipping", mapParams);
//            aggParams.put("isWholesale", "1");
//        }
        return aggParams;
    }

    /**
     * 条件聚合数据（反聚搜索条件）
     */
    protected void getAggsDataPost() {
        HttpManager.getInstance().post(getAggsUrl(), getAggsParams(false), new BaseResponse<SearchAggsBean>() {
            @Override
            public void onSuccess(String content, BaseBean<SearchAggsBean> obj, SearchAggsBean searchAggsBean) {
                if (searchAggsBean != null && searchAggsBean.getAggregations() != null) {
                    AggregationsBean aggregations = searchAggsBean.getAggregations();

                    // 规格弹窗的数据更新
                    specBeans.clear();
                    if (aggregations != null && aggregations.getSpecStats() != null) {
                        specBeans.addAll(aggregations.getSpecStats());
                    }
                    String[] split = specStr.split(",");
                    for (SearchFilterBean specBean : specBeans) {
                        boolean isContains = false;
                        for (String s : split) {
                            if (specBean.key.equals(s)) {
                                isContains = true;
                                break;
                            }
                        }
                        specBean.isSelected = isContains;
                    }

                    if (specficationPopWindow != null) {
                        specficationPopWindow.setData(specBeans);
                    }

                    // 店铺弹窗的数据更新
                    if (aggregations != null) {
                        shopStores.clear();
                        if (aggregations.getShopStats() != null) {
                            shopStores.addAll(aggregations.getShopStats());
                        }
                    }
                    String[] shopselected = selectedShopcodes.split(",");
                    for (int i = 0; i < shopselected.length; i++) {
                        String selectedKey = shopselected[i];
                        for (int j = 0; j < shopStores.size(); j++) {
                            if (shopStores.get(j).key.equals(selectedKey)) {
                                shopStores.get(j).isSelected = true;
                            }
                        }
                    }
                    if (shopStorePopWindow != null) {
                        shopStorePopWindow.setShopStoreDatas(shopStores);
                    }
                    handleSearchLabelConfig(aggregations.getDynamicLabelConfig());
                }
            }

            @Override
            public void onFailure(NetError error) {

            }
        });
    }

    /**
     * 更新动态标签
     * @param dynamicLabelConfig
     */
    private void handleSearchLabelConfig(List<SearchDynamicLabelConfig> dynamicLabelConfig) {
        if (dynamicLabelConfig!= null && !dynamicLabelConfig.isEmpty()) {
            searchViewModel.updateSearchDynamicLabelConfig(dynamicLabelConfig);
            dynamicLabelExposureAll(dynamicLabelConfig, DYNAMIC_LABEL_STYLE_HORIZONTAL_LINEAR);
            mDynamicLabelConfig = dynamicLabelConfig;
            dynamicLabelView.setVisibility(View.VISIBLE);
        } else {
            dynamicLabelView.setVisibility(View.GONE);
        }
    }

    /**
     * 获取adapter
     * @return
     */
    protected abstract YBMBaseListAdapter getAdapter();

    /**
     * 获取请求参数
     * @param isLoadMore
     * @return
     */
    protected abstract RequestParams getParams(boolean isLoadMore);

    /**
     * 请求搜索接口
     */
    protected abstract void getSearchDataPost();

    /**
     * 加载更多
     */
    protected abstract void getLoadMoreResponse();

    /**
     * 获取反聚数据的url
     * @return
     */
    protected abstract String getAggsUrl();

    //是否是专区搜索
    @Override
    protected boolean isSortnetSection(){
        return false;
    }

    //是否是来自订单凑单搜索
    protected boolean isOrderBundling(){
        return false;
    }

    /**
     * 获取厂家接口url
     * @return
     */
    protected abstract String getManufacturersUrl();

    protected abstract RequestParams getManufacturersParams();

    /**
     * 搜索场景 大搜V2：1
     *
     * @return
     */
    protected abstract String getSearchScene();

    protected void jgFilterTrack() {
        try {
            HashMap<String, Object> properties = new HashMap();
            String paixu = ""; //排序
            List<String> guige = new ArrayList<>(); //规格
            List<String> changjia = new ArrayList<>(); //厂家
            List<String> shangjia = new ArrayList<>(); //商家
            List<String> fenlei = new ArrayList<>(); //分类
            String fuwu = ""; //服务
            Double minPrice = 0.0; //最低价格
            Double maxPrice = 0.0; //最高价格
            List<String> yplx = new ArrayList<>(); //药品类型
            boolean keyongquan = false; //可用券
            boolean jingshun = false; //京东/顺丰
            boolean youxuan = false; //优选
            boolean ziying = false; //自营
            boolean onlyZhongyao = false; //只看中药
            boolean pintuanbaoyou = false; //拼团包邮
            boolean tongsheng = false; //同省

            // 综合、销量、价格 排序
            if (searchFilterSynthesizeBean != null) {
                switch (searchFilterSynthesizeBean.selectedSearchOption) {
                    case SearchFilterBean.SALESVOLUME:
                        paixu = "销量由高到低";
                        break;
                    case SearchFilterBean.PRICE:
                        paixu = "价格由低到高";
                        break;
                    default:
                        paixu = "默认";
                        break;
                }
            } else {
                paixu = "默认";
            }


            // 规格
            if (!TextUtils.isEmpty(specStr)) {
                String[] selectedStrArr = specStr.split(",");
                guige = Arrays.asList(selectedStrArr);
            }

            //厂家
            if (!TextUtils.isEmpty(manufacturer)) {
                String[] selectedStrArr = manufacturer.split("\\*");
                changjia = Arrays.asList(selectedStrArr);
            }

            // 商家
            if (!TextUtils.isEmpty(selectedShopcodes)) {
                String[] selectedStrArr = selectedShopcodes.split(",");
                shangjia = Arrays.asList(selectedStrArr);
            }

            // 分类
            if (!TextUtils.isEmpty(id)) {
                String[] selectedStrArr = id.split(",");
                fenlei = Arrays.asList(selectedStrArr);
            }

            //服务
            fuwu = jgFuwu;

            //价格区间-最低价
            if (!TextUtils.isEmpty(priceRangeFloor)) {
                try {
                     minPrice = Double.parseDouble(priceRangeFloor);
                } catch (Exception e){
                    e.printStackTrace();
                }
            }
            //价格区间-最高价
            if (!TextUtils.isEmpty(priceRangeTop)) {
                try {
                    maxPrice = Double.parseDouble(priceRangeTop);
                } catch (Exception e){
                    e.printStackTrace();
                }
            }

            //药品类型
            if (isClassA){
                yplx.add("甲类OTC");
            }
            if (isClassB){
                yplx.add("乙类OTC");
            }
            if (isClassRx){
                yplx.add("处方药RX");
            }
            if (isClassElse){
                yplx.add("其他");
            }

            //可用券
            if (isCanUseCoupon){
                keyongquan = true;
            }

            //京东/顺丰
            if (isSelectedJD && isSelectedSF){
                jingshun = true;
            }

            //优选
            if (highGross == 1){
                youxuan = true;
            }

            //自营
            if (isThirdCompany == 0) {
                ziying = true;
            }

            //只看中药
            if (isTraditionalChineseMedicine) {
                onlyZhongyao = true;
            }

            //同省
            if (isSameProvince) {
                tongsheng = true;
            }

            // 拼团包邮
            if (isDpby && isSpellGroup) {
                pintuanbaoyou = true;
            }

            properties.put(JGTrackManager.FIELD.FIELD_PAGE_ID, JGTrackManager.TrackSearchResult.PAGE_ID);
            properties.put(JGTrackManager.FIELD.FIELD_TITLE, JGTrackManager.TrackSearchResult.TITLE);
            properties.put(JGTrackManager.FIELD.FIELD_MODULE, "筛选");
            properties.put(JGTrackManager.FIELD.FIELD_KEY_WORD, keyword);
            properties.put(JGTrackManager.FIELD.FIELD_PAIXU, paixu);
            properties.put(JGTrackManager.FIELD.FIELD_GUIGE, guige);
            properties.put(JGTrackManager.FIELD.FIELD_USE_CHANGJIA, changjia);
            properties.put(JGTrackManager.FIELD.FIELD_SHANGJIA, shangjia);
            properties.put(JGTrackManager.FIELD.FIELD_FENLEI,fenlei);
            properties.put(JGTrackManager.FIELD.FIELD_FUWU, fuwu);
            properties.put(JGTrackManager.FIELD.FIELD_MINPRICE, minPrice);
            properties.put(JGTrackManager.FIELD.FIELD_MAXPRICE, maxPrice);
            properties.put(JGTrackManager.FIELD.FIELD_YPLX, yplx);
            properties.put(JGTrackManager.FIELD.FIELD_KEYONGQUAN, keyongquan);
            properties.put(JGTrackManager.FIELD.FIELD_JINGSHUN, jingshun);
            properties.put(JGTrackManager.FIELD.FIELD_YOUXUAN, youxuan);
            properties.put(JGTrackManager.FIELD.FIELD_ZIYING, ziying);
            properties.put(JGTrackManager.FIELD.FIELD_ONLYZHONGYAO, onlyZhongyao);
            properties.put(JGTrackManager.FIELD.FIELD_PINTUANBAOYOU, pintuanbaoyou);
            properties.put(JGTrackManager.FIELD.FIELD_TONGSHENG, tongsheng);

            JGTrackManager.Companion.eventTrack(this, JGTrackManager.TrackSearchResult.EVENT_FILTER, properties);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void jgBtnClick(Context mContext,String btnName,String module){
        HashMap<String, Object> properties = new HashMap<>();
        properties.put(JGTrackManager.FIELD.FIELD_PAGE_ID, JGTrackManager.TrackSearchResult.PAGE_ID);
        properties.put(JGTrackManager.FIELD.FIELD_TITLE, JGTrackManager.TrackSearchResult.TITLE);
        properties.put(JGTrackManager.FIELD.FIELD_URL, JGTrackManager.TrackSearchResult.TRACK_URL);
        properties.put(JGTrackManager.FIELD.FIELD_URL_DOMAIN,JGTrackManager.TrackSearchResult.TRACK_URL);
        properties.put(JGTrackManager.FIELD.FIELD_REFERRER, JGTrackManager.TrackSearchResult.TRACK_URL);
        properties.put(JGTrackManager.FIELD.FIELD_MODULE, module);
        properties.put(JGTrackManager.FIELD.FIELD_BTN_NAME, btnName);
        JGTrackManager.Companion.eventTrack(mContext,JGTrackManager.TrackSearchResult.EVENT_BTN_CLICK,
                properties);
    }

    protected void dynamicLabelExposureAll(List<SearchDynamicLabelConfig> dynamicLabelConfig, int dlStyle) {}
    protected void dynamicLabelClick(List<SearchDynamicLabelConfig> dynamicLabelConfig, String periodValidity, int dlStyle) {}
    /**
     * 获取页面类型
     * @return
     */
    abstract int getPageType();

    abstract JgTrackBean getJgTrackBean();

    abstract Boolean isShowNearEffective();

    abstract String getSearchUrl();

    private boolean isSelectedItemInDynamicLabel() {
        return dynamicLabelView.isSelectedItem();
    }

    /**
     * 设置搜索启动页显隐
     * @param isVisibility
     */
    public void setSearchStartPageVisibility(boolean isVisibility) {
        if (clBeforeSearchResult.getVisibility() == View.GONE && isVisibility) {
            setMAnalysisKeyword(null);
            setMPreAnalysisKeyword(null);
        }
        clBeforeSearchResult.setVisibility(isVisibility? View.VISIBLE: View.GONE);
        setPageAnalysisType(isVisibility);
    }

}
