package com.ybmmarket20.utils

import android.content.Context
import android.content.SharedPreferences

/**
 * 搜索引导管理器
 * 用于管理搜索页面各种引导的显示状态
 */
object SearchGuideManager {
    
    private const val PREF_NAME = "search_guide_prefs"
    private const val KEY_DELIVERY_GUIDE_SHOWN = "delivery_guide_shown"
    
    private fun getPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 检查配送时效引导是否已经显示过
     */
    fun isDeliveryGuideShown(context: Context): Boolean {
        return getPreferences(context).getBoolean(KEY_DELIVERY_GUIDE_SHOWN, false)
    }
    
    /**
     * 标记配送时效引导已显示
     */
    fun markDeliveryGuideShown(context: Context) {
        getPreferences(context).edit()
            .putBoolean(KEY_DELIVERY_GUIDE_SHOWN, true)
            .apply()
    }
    
    /**
     * 重置配送时效引导状态（用于测试）
     */
    fun resetDeliveryGuideState(context: Context) {
        getPreferences(context).edit()
            .putBoolean(KEY_DELIVERY_GUIDE_SHOWN, false)
            .apply()
    }
    
    /**
     * 检查是否应该显示配送时效引导
     * @param context 上下文
     * @param hasDeliveryOptions 搜索结果是否包含配送时效选项
     * @return 是否应该显示引导
     */
    fun shouldShowDeliveryGuide(context: Context, hasDeliveryOptions: Boolean): Boolean {
        return hasDeliveryOptions && !isDeliveryGuideShown(context)
    }
}
