package com.ybmmarket20.utils

import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn

class ClickDelayUtil {

    private var job: Job? = null
    private var count: Int = 0
    private val channel = Channel<() -> Unit>()

    init {
        GlobalScope.launch {
            while (true) {
                val block = channel.receive()
                withContext(Dispatchers.Main) {
                    block()
                }
            }
        }
    }

    /**
     * 启动计时
     */
    private fun startInterval(block: (Int, Boolean) -> Unit) {
        job = GlobalScope.launch(Dispatchers.Main) {
            flow {
                delay(200)
                emit(Unit)
            }.flowOn(Dispatchers.Default)
             .collect {
                 block(count, true)
                 count = 0
                 job?.cancel()
             }
        }
    }

    /**
     * 检查点击计时
     * @param isAdd 是否是添加
     * @param callback Int: 添加或减少的次数 Boolean：是否完成
     */
    fun checkClick(isAdd: <PERSON><PERSON><PERSON>, callback: (Int, Boolean) -> Unit) {
        if (job?.isActive == true) {
            job?.cancel()
        }
        when {
            count <= 0 -> count = 0
            isAdd -> count ++
            else -> count --
        }
        callback(count, false)
        startInterval(callback)
    }

    fun pushTask(block: () -> Unit) {
        GlobalScope.launch {
            channel.send(block)
        }
    }
}