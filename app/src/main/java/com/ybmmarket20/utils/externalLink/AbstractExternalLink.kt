package com.ybmmarket20.utils.externalLink

import android.content.Context
import android.content.Intent
import android.net.Uri
import com.apkfuns.logutils.LogUtils
import com.ybm.app.utils.BugUtil
import com.ybmmarket20.activity.SplashActivity
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.constant.ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE
import com.ybmmarket20.utils.RoutersUtils

abstract class AbstractExternalLink(open val baseActivity: BaseActivity): IExternalLink {

    var schema: String = ""
    var host: String = ""
    var path: String = ""
    var query: String = ""

    override fun handleUri(uri: Uri) {
        schema = uri.scheme?:""
        host = uri.host?:""
        path = uri.path?:""
        query = uri.query?:""
        if(preHandleRouter(uri)) printLog(getWrapperHandleRouter(uri), uri)
    }

    /**
     * 在处理路由前调用
     * @return true：处理路由。 false：不处理路由。
     */
    protected open fun preHandleRouter(uri: Uri): Boolean {
        return true
    }

    /**
     * 日志输出路由结果
     */
    private fun printLog(success: Boolean, uri: Uri) {
        if (success) {
            LogUtils.d("启动成功：$uri")
        } else {
            LogUtils.d("启动失败：$uri")
        }
    }

    /**
     * 防止异常
     */
    private fun getWrapperHandleRouter(uri: Uri): Boolean = try {
        handleRouter(uri)
    } catch (e: Exception) {
        BugUtil.sendBug(e)
        RoutersUtils.open(uri.toString())
    }

    /**
     * 开始路由
     */
    protected fun startRouter(context: Context, url: String): Boolean {
        val intent = Intent(baseActivity, SplashActivity::class.java)
        intent.putExtra("router", url)
        baseActivity.startActivity(intent)
        return true;
    }
}