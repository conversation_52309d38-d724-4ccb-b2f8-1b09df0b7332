package com.ybmmarket20.utils;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.BuildConfig;


/**
 * 跳转系统自启动页面
 */

public class JumpPermission {
    /**
     * Build.MANUFACTURER
     */
    private static final String MANUFACTURER_HUAWEI = "huawei";//华为
    private static final String MANUFACTURER_MEIZU = "meizu";//魅族
    private static final String MANUFACTURER_XIAOMI = "xiaomi";//小米
    private static final String MANUFACTURER_VIVO = "vivo";
    private static final String MANUFACTURER_OPPO = "oppo";
    private static final String MANUFACTURER_SAMSUNG = "samsung";//三星
    private static final String MANUFACTURER_SONY = "sony";//索尼
    private static final String MANUFACTURER_LG = "lg";
    private static final String MANUFACTURER_LETV = "letv";//乐视
    private static final String MANUFACTURER_ZTE = "zte";//中兴
    private static final String MANUFACTURER_YULONG = "yulong";//酷派
    private static final String MANUFACTURER_LENOVO = "lenovo";//联想

    /**
     * 此函数可以自己定义
     * @param activity
     */
    public static boolean goToSetting(Activity activity){
        Log.d("ld-",Build.MANUFACTURER.toLowerCase());
        switch (Build.MANUFACTURER.toLowerCase()){
            case MANUFACTURER_HUAWEI:
                return Huawei(activity);
            case MANUFACTURER_MEIZU:
                return Meizu(activity);
            case MANUFACTURER_XIAOMI:
                return Xiaomi(activity);
            case MANUFACTURER_SONY:
                return Sony(activity);
            case MANUFACTURER_OPPO:
                return OPPO(activity);
            case MANUFACTURER_VIVO:
                return vivo(activity);
            case MANUFACTURER_LG:
                return LG(activity);
            case MANUFACTURER_LETV:
               return Letv(activity);
            default:
                LogUtils.d("目前暂不支持此系统");
                return ApplicationInfo(activity);
        }
    }

    private static boolean Huawei(Activity activity) {//可以使用
        Intent intent = new Intent();
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra("packageName", BuildConfig.APPLICATION_ID);
        ComponentName comp = new ComponentName("com.huawei.systemmanager","com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity");
        intent.setComponent(comp);
        return goSetting(activity,intent);
    }

    private static boolean Meizu(Activity activity) {//可以使用
        Intent intent = new Intent("com.meizu.safe.security.SHOW_APPSEC");
        ComponentName componentName = new ComponentName("com.meizu.safe", "com.meizu.safe.permission.SmartBGActivity");
        intent.setComponent(componentName);
        intent.addCategory(Intent.CATEGORY_DEFAULT);
        intent.putExtra("packageName", BuildConfig.APPLICATION_ID);
        return goSetting(activity,intent);
    }

    private static boolean Xiaomi(Activity activity) {//可以使用
        Intent intent = new Intent("miui.intent.action.APP_PERM_AUTOSTAR");
        ComponentName componentName = new ComponentName("com.miui.securitycenter", "com.miui.permcenter.autostart.AutoStartManagementActivity");
        intent.setComponent(componentName);
        intent.putExtra("extra_pkgname", BuildConfig.APPLICATION_ID);
        return goSetting(activity,intent);
    }

    private static boolean vivo(Activity activity) {//可以使用
        Intent intent = new Intent();
        ComponentName componentName = new ComponentName("com.iqoo.secure", "com.iqoo.secure.ui.phoneoptimize.AddWhiteListActivity");
        intent.setComponent(componentName);
        intent.putExtra("extra_pkgname", BuildConfig.APPLICATION_ID);
        return goSetting(activity,intent);
    }

    public static boolean Sony(Activity activity) {
        Intent intent = new Intent();
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra("packageName", BuildConfig.APPLICATION_ID);
        ComponentName comp = new ComponentName("com.sonymobile.cta", "com.sonymobile.cta.SomcCTAMainActivity");
        intent.setComponent(comp);
        return goSetting(activity,intent);
    }

    private static boolean OPPO(Activity activity) {
        Intent intent = new Intent();
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra("packageName", BuildConfig.APPLICATION_ID);
        ComponentName comp = new ComponentName("com.coloros.oppoguardelf", "com.coloros.powermanager.fuelgaue.PowerUsageModelActivity");
        intent.setComponent(comp);
        return goSetting(activity,intent);
    }

    private static boolean LG(Activity activity) {
        Intent intent = new Intent("android.intent.action.MAIN");
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra("packageName", BuildConfig.APPLICATION_ID);
        ComponentName comp = new ComponentName("com.android.settings", "com.android.settings.Settings$AccessLockSummaryActivity");
        intent.setComponent(comp);
        return goSetting(activity,intent);
    }

    private static boolean Letv(Activity activity) {
        Intent intent = new Intent();
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra("packageName", BuildConfig.APPLICATION_ID);
        ComponentName comp = new ComponentName("com.letv.android.letvsafe", "com.letv.android.letvsafe.PermissionAndApps");
        intent.setComponent(comp);
        return goSetting(activity,intent);
    }

    /**
     * 只能打开到自带安全软件
     * @param activity
     */
    private static boolean _360(Activity activity) {
        Intent intent = new Intent("android.intent.action.MAIN");
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra("packageName", BuildConfig.APPLICATION_ID);
        ComponentName comp = new ComponentName("com.qihoo360.mobilesafe", "com.qihoo360.mobilesafe.ui.index.AppEnterActivity");
        intent.setComponent(comp);
        return goSetting(activity,intent);
    }

    /**
     * 应用信息界面
     * @param activity
     */
    private static boolean ApplicationInfo(Activity activity){
        Intent intent = new Intent();
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setAction("android.settings.APPLICATION_DETAILS_SETTINGS");
        intent.setData(Uri.fromParts("package", activity.getPackageName(), null));
        return goSetting(activity,intent);
    }

    /**
     * 系统设置界面
     * @param activity
     */
    private static boolean SystemConfig(Activity activity) {
        Intent intent = new Intent(Settings.ACTION_SETTINGS);
        return goSetting(activity,intent);
    }

    private static boolean goSetting(Activity activity,Intent intent){
        try {
            if(activity !=null){
                activity.startActivity(intent);
                return true;
            }
        }catch (Throwable e){
            BugUtil.sendBug(e);
        }
        return false;
    }
}
