package com.ybmmarket20.utils.analysis;

/**
 * <AUTHOR>
 * @date 2020/3/20
 * @description 埋点常量
 */
public interface AnalysisConst {

    /**
     * 流量数据获取
     */
    class FlowDataChain {
        //列表打开事件
        public static final String FLOWDATACHAIN_PAGE_COMMIODITYSEARCH = "page_CommodityList";
        //列表页商品曝光
        public static final String FLOWDATACHAIN_PAGE_LISTPAGE_EXPOSURE = "page_ListPage_Exposure";
        //列表页运营位商品曝光
        public static final String FLOWDATACHAIN_PAGE_LISTPAGE_OP_EXPOSURE = "search_Active_Exposure";
        // 列表页商品可见性曝光
        public static final String FLOWDATACHAIN_PAGE_LISTPAGE_EXPOSURE_NEW = "page_product_exposure";
        //到达商品详情页
        public static final String FLOWDATACHAIN_PAGE_COMMODITYDETAILS = "page_CommodityDetails_o";

        //Feed流列表页商品曝光
        public static final String FLOWDATACHAIN_PAGE_LISTPAGE_EXPOSURE_FOR_FEED = "order_exposure";
        //Feed流到达商品详情页
        public static final String FLOWDATACHAIN_PAGE_COMMODITYDETAILS_FOR_FEED = "order_detail";
        //Feed流列表店铺曝光
        public static final String FLOWDATACHAIN_PAGE_LISTPAGE_SHOP_EXPOSURE_FOR_FEED = "page_SelectedShops_Exposure";
        //Feed流列表店铺商品曝光
        public static final String FLOWDATACHAIN_PAGE_LISTPAGE_SHOP_GOODS_EXPOSURE_FOR_FEED = "page_SelectedShopsDrug_Exposure";
        //Feed流列表店铺点击
        public static final String FLOWDATACHAIN_PAGE_LISTPAGE_SHOP_CLICK_FOR_FEED = "page_SelectedShops_Click";
        //Feed流列表店铺商品点击
        public static final String FLOWDATACHAIN_PAGE_LISTPAGE_SHOP_GOODS_CLICK_FOR_FEED = "page_SelectedShopsDrug_Click";
        //Feed流列表标品曝光
        public static final String FLOWDATACHAIN_PAGE_LISTPAGE_STANDARD_GOODS_EXPOSURE_FOR_FEED = "page_ListPage_Standard_Exposure";
        //Feed流列表标品点击
        public static final String FLOWDATACHAIN_PAGE_LISTPAGE_STANDARD_GOODS_CLICK_FOR_FEED = "page_ListPage_Standard_Click";


        //其他加购
        public static final String FLOWDATACHAIN_TAG_ADDCART_OTHER = "0";
        //列表页加购(接口使用)
        public static final String FLOWDATACHAIN_TAG_ADDCART_LIST = "1";
        //详情页加购(接口使用)
        public static final String FLOWDATACHAIN_TAG_ADDCART_DETAIL = "2";
        //购物车加购(接口使用)
        public static final String FLOWDATACHAIN_TAG_ADDCART_CART = "3";
        // 再次购买
        public static final String FLOWDATACHAIN_TAG_ADDCART_REBUY = "4";
        //未进入商品详情的加购(埋点sdk使用)
        public static final String FLOWDATACHAIN_TAG_INTOPRODUCTDETAIL_NOREAL = "1";
        //进入商品详情的加购(埋点sdk使用)
        public static final String FLOWDATACHAIN_TAG_INTOPRODUCTDETAIL_REAL = "2";
        //spid 再次购买
        public static final String FLOWDATAPARAMS_SPTYPE_REBUY = "5";
        //埋点参数来源首页常购清单
        public static final String FLOWDATACHAIN_FROM_HOME_OFTEN_BUY_LIST = "1";
        //埋点参数来源发现常购清单
        public static final String FLOWDATACHAIN_FROM_FIND_OFTEN_BUY_LIST = "2";
    }

    /**
     * 首页埋点
     */
    class HomeSteady {
        // 首页V1 banner点击
        public static final String ACTION_HOME_BANNER_CLICK_V1 = "action_Home_Banner";
        // 首页V2 banner点击
        public static final String ACTION_HOME_BANNER_CLICK_V2 = "action_Home_newBanner";
        // 首页V1 快捷入口点击
        public static final String ACTION_HOME_FASTENTRY_CLICK_V1 = "action_Home_Shortcut";
        // 首页V2 快捷入口点击
        public static final String ACTION_HOME_FASTENTRY_CLICK_V2 = "action_Home_newShortcut";
        // 首页V1 胶囊位点击
        public static final String ACTION_HOME_STREAMER_CLICK_V1 = "action_Home_Image";
        // 首页V2 胶囊位点击(第一个)
        public static final String ACTION_HOME_STREAMER_CLICK_V2_FIRST = "action_Home_Streamer1";
        // 首页V2 胶囊位点击(第二个)
        public static final String ACTION_HOME_STREAMER_CLICK_V2_SECOND = "action_Home_Streamer2";
        // 首页V1 导购入口点击
        public static final String ACTION_HOME_SHOPPING_GUIDE_CLICK_V1 = "action_Home_Card";
        // 首页V1 导购入口点击
        public static final String ACTION_HOME_SHOPPING_GUIDE_CLICK_V2 = "action_Home_newCard";
        // 首页定时活动顶栏点击
        public static final String ACTION_HOME_SECKILL_TOP_CLICK_V2 = "action_Home_timingActivity";
        // 首页定时活动更多点击
        public static final String ACTION_HOME_SECKILL_MORE_CLICK_V2 = "action_Home_timingActivitymore_click";
        // 首页定时活动商品曝光
        public static final String ACTION_HOME_SECKILL_GOODS_EXPOSURE_V2 = "action_Home_timingActivity_Exposure";
        // 首页定时活动商品点击
        public static final String ACTION_HOME_SECKILL_GOODS_CLICK_V2 = "action_Home_timingActivity_click";
        // 首页精选店铺顶栏点击
        public static final String ACTION_HOME_SHOP_TOP_CLICK_V2 = "action_Home_carefullySelectedShops_click";
        // 首页精选店铺店铺点击
        public static final String ACTION_HOME_SHOP_SHOP_CLICK_V2 = "action_Home_carefullySelectedShops";
        // 首页精选店铺商品曝光
        public static final String ACTION_HOME_SHOP_GOODS_EXPOSURE_V2 = "action_Home_carefullySelectedShops_Exposure";
        // 首页商品流tab点击
        public static final String ACTION_HOME_GOODS_FEED_TAB_CLICK_V2 = "action_Home_recommendTab";
        //首页人气店铺-更多按钮点击
        public static final String ACTION_HOME_RECOMMEND_SHOP_MORE_CLICK = "action_Home_newCarefullySelectedShops_click";
        //首页人气店铺-店铺点击
        public static final String ACTION_HOME_RECOMMEND_SHOP_ITEM_CLICK = "action_Home_newCarefullySelectedShops";
        //首页人气店铺-店铺曝光
        public static final String ACTION_HOME_RECOMMEND_SHOP_EXPOSURE = "action_Home_newCarefullySelectedShops_Exposure";
    }

    class PayResultSteady {
        // 支付成功页横幅广告点击
        public static final String ACTION_PAY_RESULT_STREAMER_CLICK = "pay_Image_Click";
        // 支付成功页优惠券点击
        public static final String ACTION_PAY_RESULT_COUPON_CLICK = "pay_Coupon_Click";

        // 支付成功页优惠券曝光
        public static final String PAY_COUPON_EXPOSURE = "pay_Coupon_Exposure";
        // 支付成功页横幅广告曝光
        public static final String PAY_IMAGE_EXPOSURE = "pay_Image_Exposure";


    }

    /**
     * 店铺列表
     */
    class ShopList {
        // 店铺列表底部tab点击
        public static final String SHOP_LIST_BOTTOM_CLICK = "_anavigation_bstoreList";
        // 店铺列表浏览量
        public static final String SHOP_LIST_PV = "page_ShopsList_pv";
        // 店铺列表店铺点击
        public static final String SHOP_LIST_SHOP_CLICK = "pgae_ShopsList_click";
        // 店铺列表-“自营”tab点击
        public static final String SHOP_LIST_PROPRIETARY_CLICK = "pgae_proprietary_click";
        // 店铺列表-“合作商家”tab点击
        public static final String SHOP_LIST_BUSNESS_PARTNER_CLICK = "pgae_businessPartner_click";
        // 店铺列表-“默认”筛选点击
        public static final String SHOP_LIST_DEFAULT_CLICK = "pgae_default_click";
        // 店铺列表-“合作商家”tab点击
        public static final String SHOP_LIST_NEWEST_CLICK = "pgae_newest_click";
        // 店铺列表-店铺曝光
        public static final String SHOP_LIST_SHOP_EXPOSURE = "page_ShopsList_Exposure";
    }

    class Cart {
        // 购物车跨店券入口-点击事件
        public static final String ACTION_SHOPPINGCART_PLATFORMCOUPON_CLICK = "action_ShoppingCart_PlatformCoupon_click";
        // 跨店券引导去凑单-点击事件
        public static final String ACTION_SHOPPINGCART_PLATFORMCOUPONITEM_CLICK = "action_ShoppingCart_PlatformCouponItem_click";
        // 总计明细-点击事件
        public static final String ACTION_SHOPPINGCART_DETAILS_CLICK = "action_ShoppingCart_details_Click";
        // 去结算-点击事件
        public static final String ACTION_SHOPPINGCART_SETTLEMENT_CLICK = "action_ShoppingCart_Settlement_Click";

        // 购物车店铺入口-点击事件
        public static final String ACTION_SHOPPINGCART_SHOP_CLICK = "action_ShoppingCart_Shop_Click";
        // 购物车店铺优惠券入口-点击事件
        public static final String ACTION_SHOPPINGCART_COUPON_CLICK = "action_ShoppingCart_Coupon_Click";
        // 收起/展开-点击事件
        public static final String ACTION_SHOPPINGCART_PACKUP_CLICK = "action_ShoppingCart_PackUp_Click";
        // 包邮注解-点击
        public static final String ACTION_SHOPPINGCART_POST_CLICK = "action_ShoppingCart_Post_Click";
        // 包邮去凑单-点击
        public static final String ACTION_SHOPPINGCART_POSTITEM_CLICK = "action_ShoppingCart_PostItem_Click";
        // 满返活动去凑单-曝光
        public static final String ACTION_SHOPPINGCART_RETURNITEM_EXPOSURE = "action_ShoppingCart_ReturnItem_Exposure";
        // 满返活动去凑单-点击
        public static final String ACTION_SHOPPINGCART_RETURNITEM_CLICK = "action_ShoppingCart_ReturnItem_Click";
        // 满类活动去凑单-曝光
        public static final String ACTION_SHOPPINGCART_REDUCTIONITEM_EXPOSURE = "action_ShoppingCart_ReductionItem_Exposure";
        // 满类活动去凑单-点击
        public static final String ACTION_SHOPPINGCART_REDUCTIONITEM_CLICK = "action_ShoppingCart_ReductionItem_Click";
        //更多优惠-赠品卡片曝光
        public static final String ACTION_MOREDISCOUNT_GIFTCARD_EXPOSURE = "action_moreDiscount_giftCard_Exposure";
//        更多优惠-赠品卡片点击
        public static final String ACTION_MOREDISCOUNT_GIFTCARD_CLICK = "action_moreDiscount_giftCard_Click";
        // 拼团-赠品卡片曝光
        public static final String ACTION_GROUPPURCHASE_GIFTCARD_EXPOSURE = "action_groupPurchase_giftCard_Exposure";
        //拼团-赠品卡片点击
        public static final String ACTION_GROUPPURCHASE_GIFTCARD_CLICK = "action_groupPurchase_giftCard_Click";
        //商详-赠品卡片曝光
        public static final String PAGE_COMMODITYDETAILS_GIFTCARD_EXPOSURE = "page_CommodityDetails_giftCard_Exposure";
        //商详-赠品卡片点击
        public static final String PAGE_COMMODITYDETAILS_GIFTCARD_CLICK = "page_CommodityDetails_giftCard_Click";

    }

}
