package com.ybmmarket20.utils;

import com.ybmmarket20.constant.ConstantData;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-01-20
 * @description 审核状态同步工具
 * licenseStatus
 * 1：资质未提交
 * 2：资质已提交
 * 3：资质已过期
 * 4：资质已通过
 * 5：资质审核中
 * 6：首营一审通过
 */
public class AuditStatusSyncUtil {
    private static final AuditStatusSyncUtil instance = new AuditStatusSyncUtil();
    private int mLicenseStatus = ConstantData.LICENSE_STATUS_DEFAULT;

    private List<AuditStatusSyncListener> listenerList = new ArrayList<>();

    private AuditStatusSyncUtil() {
    }

    public static AuditStatusSyncUtil getInstance() {
        return instance;
    }

    public int getLicenseStatus() {
        mLicenseStatus = SpUtil.readInt(ConstantData.LICENSE_STATUS, ConstantData.LICENSE_STATUS_DEFAULT);
        return mLicenseStatus;
    }


    /**
     * 仅设置审核状态
     *
     * @param licenseStatus
     */
    public void setLicenseStatusOnly(int licenseStatus) {
        mLicenseStatus = licenseStatus;
        SpUtil.writeInt(ConstantData.LICENSE_STATUS, licenseStatus);
    }

    /**
     * 更新状态
     *
     * @param licenseStatus 资质审核状态
     */
    public void updateLicenseStatus(int licenseStatus) {
        updateLicenseStatus(licenseStatus, null);
    }

    /**
     * 更新状态,当前页面不更新
     *
     * @param licenseStatus   资质审核状态
     * @param currentListener 当前页面监听器
     */
    public void updateLicenseStatus(int licenseStatus, AuditStatusSyncListener currentListener, boolean force) {
        //判断mLicenseStatus == LICENSE_STATUS_DEFAULT防止第一次设置状态时调用监听逻辑
        if (licenseStatus == ConstantData.LICENSE_STATUS_DEFAULT || licenseStatus == mLicenseStatus)
            return;
        mLicenseStatus = licenseStatus;
        SpUtil.writeInt(ConstantData.LICENSE_STATUS, licenseStatus);
        //如果已经通过一审并且不强制就不继续执行
        if (isAuditFirstPassed() && !force) return;
        for (int i = 0; i < listenerList.size(); i++) {
            AuditStatusSyncListener listener = listenerList.get(i);
            if (currentListener != listener && listener != null) {
                listener.syncCallback(licenseStatus);
            }
        }
    }

    /**
     * 更新状态,当前页面不更新
     *
     * @param licenseStatus   资质审核状态
     * @param currentListener 当前页面监听器
     */
    public void updateLicenseStatus(int licenseStatus, AuditStatusSyncListener currentListener) {
        updateLicenseStatus(licenseStatus, currentListener, false);
    }

    /**
     * 强制更新状态,当前页面不更新
     *
     * @param licenseStatus   资质审核状态
     * @param currentListener 当前页面监听器
     */
    public void updateLicenseStatusForce(int licenseStatus, AuditStatusSyncListener currentListener) {
        updateLicenseStatus(licenseStatus, currentListener, true);
    }

    /**
     * 添加监听器
     *
     * @param statusSyncListener 添加的监听器
     */
    public void addLicenseStatusListener(AuditStatusSyncListener statusSyncListener) {
        if (statusSyncListener != null) listenerList.add(statusSyncListener);
    }

    /**
     * 移除监听器
     *
     * @param statusSyncListener 移除的监听器
     */
    public void removeLicenseStatusListener(AuditStatusSyncListener statusSyncListener) {
        if (statusSyncListener != null) listenerList.remove(statusSyncListener);
    }

    /**
     * 是否已经通过审核
     *
     * @return true：已经通过
     */
    public boolean isAuditFirstPassed() {
        return getLicenseStatus() != ConstantData.LICENSE_STATUS_UNCOMMITTED
                && getLicenseStatus() != ConstantData.LICENSE_STATUS_SY_AUDITING
                && getLicenseStatus() != ConstantData.LICENSE_STATUS_DEFAULT;
    }

//    /**
//     * 是否通过审核
//     * @param status
//     * @return
//     */
//    public boolean isAuditFirstPassed(int status) {
//        return status != ConstantData.LICENSE_STATUS_UNCOMMITTED
//                && status != ConstantData.LICENSE_STATUS_SY_AUDITING
//                && status != ConstantData.LICENSE_STATUS_DEFAULT;
//    }


    public interface AuditStatusSyncListener {
        void syncCallback(int licenseStatus);
    }
}
