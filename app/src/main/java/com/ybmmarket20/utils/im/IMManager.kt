package com.ybmmarket20.utils.im

import android.content.Context
import com.apkfuns.logutils.LogUtils
import com.ybmmarket20.utils.im.core.IMCoreStrategy
import com.ybmmarket20.utils.im.core.callback.IMCoreCallback
import com.ybmmarket20.utils.im.core.callback.IMCoreSimpleMessageCallback

/**
 * <AUTHOR>
 * IM
 */

class IMManager(private val strategy: IMCoreStrategy?) : IMCoreStrategy {

    override fun initStrategy(context: Context?, sdkAppId: Int, logLevel: Int, callback: IMCoreCallback?) {
        strategy?.initStrategy(context, sdkAppId, logLevel, callback)
    }

    override fun unInitStrategy(callback: IMCoreCallback?) {
        strategy?.unInitStrategy(callback)
    }

    override fun login(userId: String?, userSig: String?, callback: IMCoreCallback?) {
        strategy?.login(userId, userSig, callback)
    }

    override fun logout(userId: String?, callback: IMCoreCallback?) {
        strategy?.logout(userId, callback)
    }

    override fun getLoginStatus(userId: String?): Int = strategy?.getLoginStatus(userId)?: -1

    override fun joinGroup(userId: String?, groupId: String?, message: String?, callback: IMCoreCallback?) {
        strategy?.joinGroup(userId, groupId, message, callback)
    }

    override fun quiteGroup(userId: String?, groupId: String?, callback: IMCoreCallback?) {
        strategy?.quiteGroup(userId, groupId, callback)
    }

    override fun sendGroupTextMessage(text: String?, userId: String?, groupId: String?, priority: Int, callback: IMCoreCallback?) {
        strategy?.sendGroupTextMessage(text, userId, groupId, priority, callback)
    }

    override fun sendGroupCustomMessage(customData: ByteArray?, userId: String?, groupId: String?, priority: Int, callback: IMCoreCallback?) {
        strategy?.sendGroupCustomMessage(customData, userId, groupId, priority, callback)
    }

    override fun setReceiveMessageListener(callBack: IMCoreSimpleMessageCallback?) {
        strategy?.setReceiveMessageListener(callBack)
    }

    override fun setReceiveGroupListener(callback: IMCoreCallback?) {
        strategy?.setReceiveGroupListener(callback)
    }

    override fun removeSimpleGroupMessageListener(listener: IMCoreSimpleMessageCallback?) {
        strategy?.removeSimpleGroupMessageListener(listener)
    }

    override fun removeAllSimpleGroupMessageListener() {
        strategy?.removeAllSimpleGroupMessageListener()
    }
}