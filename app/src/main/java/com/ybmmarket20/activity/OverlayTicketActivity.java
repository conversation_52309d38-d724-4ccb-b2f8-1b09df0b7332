package com.ybmmarket20.activity;

import android.graphics.Rect;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.CouponMemberAdapter;
import com.ybmmarket20.bean.VoucherListBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.IntentCanst;

import java.util.List;

import butterknife.Bind;

/**
 * 叠加券二级页面
 */
@Router({"overlayticket", "overlayticket/:overlay_ticket_list"})
public class OverlayTicketActivity extends BaseActivity {

    @Bind(R.id.list)
    CommonRecyclerView mList;

    private List<VoucherListBean> mDjList;
    private CouponMemberAdapter mAdapter;
    private String mEmptyStr;
    private int bottom = ConvertUtils.dp2px(8);

    @Override
    protected void initData() {
        setTitle("双十大促叠加券");
        mEmptyStr = "您还没有优惠券哦!";
        mDjList = (List<VoucherListBean>) getIntent().getSerializableExtra(IntentCanst.OVERLAY_TICKET_LIST);
        if (mDjList == null || mDjList.size() <= 0) {
            finish();
            ToastUtils.showShort("参数错误");
            return;
        }

        mAdapter = new CouponMemberAdapter(R.layout.coupon_list_item_v2, false, mDjList);
        mList.setAdapter(mAdapter);
        mList.setEnabled(false);
        mList.setShowAutoRefresh(false);
        mAdapter.openLoadMore(1000, true);
        mList.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, mEmptyStr);
        mList.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                outRect.bottom = bottom;
            }
        });

    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_overlay_ticket;
    }

}
