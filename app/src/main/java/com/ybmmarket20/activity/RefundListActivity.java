package com.ybmmarket20.activity;

import android.content.Intent;
import android.graphics.Rect;
import android.net.Uri;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.RefundBean;
import com.ybmmarket20.bean.RefundOrderBean;
import com.ybmmarket20.bean.RefundOrderListBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarketkotlin.utils.TextViewKt;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import butterknife.Bind;

/**
 * 订单退货列表
 */
@Router({"refundlist/:order_id", "refundlist"})
public class RefundListActivity extends BaseActivity {

    @Bind(R.id.crv_list)
    CommonRecyclerView crvList;
    private YBMBaseAdapter orderAdapter;
    private List<RefundBean> rows = new ArrayList<>();
    private int page = 1;
    private int pageSize = 20;
    private int bottom = ConvertUtils.dp2px(6);
    protected SimpleDateFormat dateFormat;
    protected String orderId;
    private String billType;
    private boolean isFrist;

    @Override
    protected void initData() {
        setTitle("退款单列表");
        isFrist = true;
        orderId = getIntent().getStringExtra("order_id");
        billType = getIntent().getStringExtra("billType");
        if (TextUtils.isEmpty(orderId)) {
            finish();
            ToastUtils.showShort("没有订单id");
            return;
        }
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        orderAdapter = new YBMBaseAdapter<RefundBean>(R.layout.refund_list_item, rows) {

            @Override
            protected void bindItemView(YBMBaseHolder baseViewHolder, final RefundBean bean) {
                TextView tvRefund = baseViewHolder.getView(R.id.tv_refund);
                tvRefund.setText(bean.auditStatusName);
                tvRefund.setActivated(false);
                tvRefund.setEnabled(false);
                if (bean.auditState == -1) {
                    tvRefund.setActivated(true);
                } else if (bean.auditState == 1) {
                    tvRefund.setEnabled(true);
                } else {
                    tvRefund.setActivated(false);
                    tvRefund.setEnabled(false);
                }
                baseViewHolder.setImageUrl(R.id.iv_order, AppNetConfig.LORD_IMAGE + bean.imageUrl, R.drawable.jiazaitu_min)
                        .setText(R.id.tv_order_total, "退款金额：¥" + bean.cashPayAmount)
                        .setText(R.id.tv_order_no, "退货单号:" + bean.refundOrderNo)
                        .setText(R.id.tv_order_number, bean.refundVarietyNum + "件商品")
                        .setText(R.id.tv_order_time, "申请时间：" + bean.applyRefundTime)
                        .setOnClickListener(R.id.btn_detail, v -> RoutersUtils.open("ybmpage://refunddetail/" + bean.id + "/" + bean.refundOrderNo+ "?billType="+billType))
                        .setOnClickListener(R.id.ll_root, v -> RoutersUtils.open("ybmpage://refunddetail/" + bean.id + "/" + bean.refundOrderNo+ "?billType="+billType))
                        .setOnClickListener(R.id.btn_cancel_the_refund, v -> {
                            //等于0表示可以取消，反之不可以取消
                            if (bean.audit_process_state == 0) {
                                showSaveDialog((dialog, button) -> cancelRefundOrder(bean.refundOrderNo));
                            } else {
                                showHintDialog((dialog, button) -> {
                                    Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + RoutersUtils.kefuPhone));
                                    BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
                                });
                            }
                        })
                        .setGone(R.id.btn_cancel_the_refund, bean.auditState == 0 && bean.refundChannel == 1 && bean.auditProcessState == 0);
                boolean isShowTime = bean.toBeConfirmedCountDownTime > 0;
                baseViewHolder.setVisible(R.id.tv_refund_list_time_layout, isShowTime);
                baseViewHolder.setText(R.id.tv_refund_list_time_title, isShowTime? bean.auditStatusName: "");
                if (isShowTime) {
                    TextView tvTimeCount = baseViewHolder.getView(R.id.tv_refund_list_time_count);
                    long currentRealTime = SystemClock.elapsedRealtime();
                    long realCountdownTime = bean.toBeConfirmedCountDownTime * 1000 - (currentRealTime - bean.localTime);
                    TextViewKt.addCountDownWithUnit(tvTimeCount, realCountdownTime, null, null, () -> {
                        getRefundList(0);
                        return null;
                    });
                }
            }
        };
        crvList.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getRefundList(0);
            }

            @Override
            public void onLoadMore() {
            }
        });
        crvList.setAdapter(orderAdapter);
        crvList.setEmptyView(R.layout.empty_view);
        orderAdapter.setEnableLoadMore(false);
        crvList.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                outRect.bottom = bottom;
            }
        });
    }

    /*
     * 取消退款单
     * */
    private void cancelRefundOrder(String refundOrderNo) {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("refundOrderNo", refundOrderNo);
        HttpManager.getInstance().post(AppNetConfig.CANCELREFUNDORDER, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> data, EmptyBean obj) {
                if (crvList == null) {
                    return;
                }
                crvList.setRefreshing(false);
                if (data != null && data.isSuccess()) {

                    if (!TextUtils.isEmpty(data.msg)) {
                        ToastUtils.showShort(data.msg);
                    }
                    getRefundList(0);

                }
            }

            @Override
            public void onFailure(NetError error) {
                if (crvList == null) {
                    return;
                }
                crvList.setRefreshing(false);
            }
        });

    }

    private void showSaveDialog(AlertDialogEx.OnClickListener listener) {
        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setMessage("确认取消当前退款单吗？").setCancelButton("再想想", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
            }
        }).setCancelable(false).setConfirmButton("确认取消", listener).setCanceledOnTouchOutside(false).setTitle(null).show();
    }

    private void showHintDialog(AlertDialogEx.OnClickListener listener) {
        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setMessage("当前退款单客服已受理，如需取消请联系客服人员！").setCancelButton("我知道了", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
            }
        }).setCancelable(false).setConfirmButton("联系客服", listener).setCanceledOnTouchOutside(false).setTitle(null).show();
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_list;
    }

    public void getRefundList(final int page) {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("limit", pageSize + "");
        params.put("orderId", orderId);
        params.put("offset", String.valueOf(0));
        if (page >= 1) {
            params.put("offset", String.valueOf(page));
        }
        HttpManager.getInstance().post(AppNetConfig.REFUNDLIST, params, new BaseResponse<RefundOrderListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<RefundOrderListBean> data, RefundOrderListBean obj) {
                if (crvList == null) {
                    return;
                }
                crvList.setRefreshing(false);
                if (data != null) {
                    if (data.isSuccess() && obj != null) {
                        RefundOrderBean orderList = obj.refundOrderList;
                        if (page < 1) {//下拉刷新
                            RefundListActivity.this.page = 1;
                            if (rows == null) {
                                rows = new ArrayList<>();
                            }
                            rows.clear();
                            if (orderList != null && orderList.getRows() != null) {
                                rows.addAll(orderList.getRows());
                                for (RefundBean row : rows) {
                                    row.localTime = SystemClock.elapsedRealtime();
                                }
                            }
                            orderAdapter.notifyDataSetChanged();
                        } else {
                            if (orderList != null && orderList.getRows() != null) {
                                for (RefundBean bean : orderList.getRows()) {
                                    rows.remove(bean);
                                }
                                rows.addAll(orderList.getRows());
                                if (orderList.getRows().size() >= pageSize) {
                                    RefundListActivity.this.page++;
                                    for (RefundBean row : rows) {
                                        row.localTime = SystemClock.elapsedRealtime();
                                    }
                                }
                            }
                            orderAdapter.notifyDataChangedAfterLoadMore(orderList.getRows().size() >= pageSize);
                        }

                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                if (crvList == null) {
                    return;
                }
                crvList.setRefreshing(false);
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (isFrist) {
            isFrist = false;
        } else {//刷新订单列表
            getRefundList(0);
        }
    }
}
