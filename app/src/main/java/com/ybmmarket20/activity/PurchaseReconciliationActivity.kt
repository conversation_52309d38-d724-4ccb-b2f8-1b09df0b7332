package com.ybmmarket20.activity

import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.LinearLayout
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.PurchaseReconciliationAdapter
import com.ybmmarket20.bean.PurchaseRowsBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.PurchaseDialog
import com.ybmmarket20.viewmodel.PurchaseViewModel
import kotlinx.android.synthetic.main.activity_purchase_reconciliation.*

/**
 * 采购对账单
 */
const val LIMIT_PURCHASE = "7";

@Router("purchasereconciliation")
class PurchaseReconciliationActivity: PurchaseReconciliationAnalysisActivity() {

    val mViewModel: PurchaseViewModel by viewModels()
    private val purchaseDialog: PurchaseDialog by lazy { PurchaseDialog(this) }
    private var mList: MutableList<PurchaseRowsBean> = mutableListOf()
    private var mYear = ""
    private var mMonth = ""
    private var mOffset = 1
    private var mAdapter = PurchaseReconciliationAdapter(mList)

    override fun getContentViewId(): Int = R.layout.activity_purchase_reconciliation

    override fun initData() {
        super.initData()
        XyyIoUtil.track("action_purchaseOrder", hashMapOf("pageName" to "采购对账单"))
        mViewModel.purchaseDataLiveDate.observe(this, Observer {
            tv_time.text = "${it.year}.${handleMonth(it.month)}"
            mYear = it.year
            mMonth = it.month
            mOffset = 1
            purchaseDialog.setItemData(it)
            showProgress()
            mViewModel.getPurchaseAmount(it.year, handleMonth(it.month))
            mViewModel.getPurchaseList(it.year, handleMonth(it.month), "$mOffset", LIMIT_PURCHASE)
        })

        mViewModel.purchaseAmountLiveData.observe(this, Observer {
            dismissProgress()
            if (it != null && it.isSuccess && it.data != null) {
                tv_purchase_total_amount.text = UiUtils.transform(it.data.totalAmount)
                tv_actual_purchase_total_amount.text = UiUtils.transform(it.data.actualTotalAmount)
                tv_actual_refund_total_amount.text = UiUtils.transform(it.data.actualRefundAmount)
                val savedAmount = UiUtils.transform(it.data.totalDiscountAmount)
                val amountSpannable = SpannableStringBuilder("¥${savedAmount}").also {spannable ->
                    spannable.setSpan(AbsoluteSizeSpan(7, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                    spannable.setSpan(AbsoluteSizeSpan(13, true), 1, spannable.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                    spannable.setSpan(ForegroundColorSpan(Color.parseColor("#01B377")), 0, spannable.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                }
                val titleSpannable = SpannableStringBuilder("已节省: ").append(amountSpannable)
                tv_saved_amount.text = titleSpannable
            }
        })

        mViewModel.purchaseListLiveData.observe(this, Observer {
            if (it.data != null && it.isSuccess) {
                if (mOffset == 1) {
                    if (it.data.rows == null) {
                        ll_empty_data.visibility = View.VISIBLE
                        return@Observer
                    }
                    mList.clear()
                    mList.addAll(it.data.rows!!)
                    setList()
                    if (mList.isEmpty()) {
                        ll_empty_data.visibility = View.VISIBLE
                    } else {
                        ll_empty_data.visibility = View.GONE
                    }
                } else {
                    mList.addAll(it.data.rows!!)
                }
                mAdapter.notifyDataChangedAfterLoadMore(it.data.total > mList.size)
                mOffset++
            } else {
                if(mOffset == 1) {
                    mList.clear()
                    setList()
                }
            }
        })

        tv_time.setOnClickListener {
            if (mYear.isNotEmpty() && mMonth.isNotEmpty()) {
                purchaseDialog.show()
            }
        }

        tv_download_to_email.setOnClickListener {
            XyyIoUtil.track("action_purchaseOrder_downloadToMailbox", hashMapOf("text" to "下载对账单到邮箱"))
            RoutersUtils.open("ybmpage://aptitudexyyemail?isForm=2&year=$mYear&month=${handleMonth(mMonth)}")
        }

        purchaseDialog.setOnConfirmListener { yearIndex, monthIndex ->
            val purchaseDate = mViewModel.purchaseDataLiveDate.value
            val year = purchaseDate?.yearList?.get(yearIndex) ?: ""
            val month = purchaseDate?.monthList?.get(monthIndex)?: ""
            mYear = year
            mMonth = month
            tv_time.text = "$year.${handleMonth(month)}"
            mOffset = 1
            mViewModel.getPurchaseAmount(year, handleMonth(month))
            mViewModel.getPurchaseList(year, handleMonth(month), "$mOffset", LIMIT_PURCHASE)
        }
        //设置列表
        setList()
        //获取时间
        mViewModel.getPurchaseDate()
    }

    private fun handleMonth(month: String): String {
        return if (month.length == 1) {
            "0$month"
        } else {
            month
        }
    }

    private fun setList() {
        mAdapter = PurchaseReconciliationAdapter(mList)
        rv.layoutManager = WrapLinearLayoutManager(this, LinearLayout.VERTICAL, false)
        rv.adapter = mAdapter
        mAdapter.setEnableLoadMore(true)
        mAdapter.setOnLoadMoreListener {
            mViewModel.getPurchaseList(mYear, handleMonth(mMonth), "$mOffset", LIMIT_PURCHASE)
        }
    }
}