package com.ybmmarket20.activity;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.AptitudeLogAdapter;
import com.ybmmarket20.bean.AptitudeLogDataBean;
import com.ybmmarket20.bean.AptitudeLogListBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.statusview.StatusViewLayout;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.DateTimeUtil;
import com.ybmmarket20.utils.UiUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import butterknife.Bind;

@Router({"aptitudelog/:merchantId/:licenseAuditId/:type"})
public class AptitudeLogActivity extends BaseActivity {
    String id;
    @Bind(R.id.receipts_number)
    TextView mReceiptsNumber;
    @Bind(R.id.receipts_status)
    TextView mReceiptsStatus;
    @Bind(R.id.tv_up_time)
    TextView mReceiptsTime;
    @Bind(R.id.receipts_list)
    RecyclerView mList;
    @Bind(R.id.svl_examine_log)
    StatusViewLayout mStatus;
    @Bind(R.id.cl_document_status)
    ConstraintLayout mStatusC;
    private AptitudeLogAdapter adapter;
    private List<AptitudeLogListBean> list = new ArrayList<>();
    private SimpleDateFormat dateFormat;
    private String merchantId;
    private String licenseAuditId;
    private String type;


    @Override
    protected int getContentViewId() {
        return R.layout.activity_aptitude_log;
    }

    @Override
    protected void initData() {
        setTitle("审批日志");
        merchantId = getIntent().getStringExtra("merchantId");
        licenseAuditId = getIntent().getStringExtra("licenseAuditId");
        type=getIntent().getStringExtra("type");
        initRecyclerView();
        getAptitudeLog();
    }

    private void getAptitudeLog() {
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("applicationNumber", licenseAuditId);
        params.put("type",type);
        HttpManager.getInstance().post(AppNetConfig.QUERY_LICENSE_AUDIT_LOG_LIST, params, new BaseResponse<AptitudeLogDataBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<AptitudeLogDataBean> baseBean, AptitudeLogDataBean data) {
                dismissProgress();
                if (baseBean != null && baseBean.isSuccess()) {
                    if (data != null) {
                        if (data.list != null && data.list.size() > 0) {
                            if (list == null) {
                                list = new ArrayList<>();
                            }
                            list.clear();
                            list.addAll(data.list);
                            adapter.setNewData(list);
                        }
                        mStatusC.setVisibility(View.VISIBLE);
                        if (data.licenseAudit != null) {//审批状态
                            mReceiptsStatus.setText(data.licenseAudit.auditStatusName);
                            mReceiptsNumber.setText(data.licenseAudit.applicationNumber);
                            mReceiptsTime.setText(DateTimeUtil.getLogDateTime(data.licenseAudit.createTime));
                            mReceiptsStatus.setTextColor(UiUtils.getColorFromAptitudeStatus(data.licenseAudit.auditStatus));
                        }
                    }
                }
            }
        });
    }

    private void initRecyclerView() {
        dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        //日志列表
        adapter = new AptitudeLogAdapter(R.layout.item_aptitude_log, list);
        mList.setNestedScrollingEnabled(false);
        mList.setAdapter(adapter);
        mList.setLayoutManager(new WrapLinearLayoutManager(getMySelf()));
        mList.setEnabled(false);
        View emptyView = getLayoutInflater().inflate(R.layout.layout_empty_view, null);
        emptyView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        adapter.setEmptyView(emptyView);
    }

}
