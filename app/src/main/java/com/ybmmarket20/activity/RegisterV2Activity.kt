package com.ybmmarket20.activity

import android.text.TextUtils
import android.view.View
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.analysys.ANSAutoPageTracker
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.github.mzule.activityrouter.annotation.Router
import com.google.gson.Gson
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.jgTrackRegisterBtnClick
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.AccountStatus.updateStatus
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.CheckPrivacyListener
import com.ybmmarket20.viewmodel.RegisterV2ViewModel
import kotlinx.android.synthetic.main.activity_register.checkPrivacy
import kotlinx.android.synthetic.main.activity_register.register_btn
import kotlinx.android.synthetic.main.activity_register.register_img_code
import kotlinx.android.synthetic.main.activity_register.register_name
import kotlinx.android.synthetic.main.activity_register.register_password
import kotlinx.android.synthetic.main.activity_register.register_phone
import kotlinx.android.synthetic.main.activity_register.register_request_img_code
import kotlinx.android.synthetic.main.activity_register.register_request_sms_code
import kotlinx.android.synthetic.main.activity_register.register_sms_code
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import java.text.SimpleDateFormat
import java.util.Date


/**
 * 注册账户
 */
@Router("register")
class RegisterV2Activity : BaseActivity(), View.OnClickListener, ANSAutoPageTracker {

    private val mViewModel: RegisterV2ViewModel by viewModels()
    private var imgCodeId: String = ""
    private var checkTime: String = ""
    override fun getContentViewId(): Int = R.layout.activity_register

    override fun initData() {
        setTitle("注册")
        initListener()
        initObserver()
        editChangeListener()
        getImageCode()
        setPrivacy()
    }

    override fun onResume() {
        super.onResume()
        updateStatus(0)
        SpUtil.setAccountId("")
        SpUtil.setToken("")
        SpUtil.setMerchantid("")
    }

    private fun initListener() {
        register_request_img_code.setOnClickListener(this)
        register_request_sms_code.setOnClickListener(this)
        register_btn.setOnClickListener(this)
    }

    /**
     * 设置隐私协议与服务协议
     */
    private fun setPrivacy() {
        checkPrivacy.setCheckPrivacyTextForLogin({
            mViewModel.getLoginAgreement(2)
        }, {
            mViewModel.getLoginAgreement(1)
        })
        mViewModel.getLoginAgreement(0)
        checkPrivacy.setCheck(false)
        checkPrivacy.setCheckBoxListener(object : CheckPrivacyListener {
            override fun onChanged(isChecked: Boolean) {
                // 获取当前时间戳并转换为 yyyy-MM-dd HH:mm:ss 格式
                val currentTimeMillis = System.currentTimeMillis()
                val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                checkTime = sdf.format(Date(currentTimeMillis))
                register_btn.checkButtonStatus()
                jgTrackRegisterBtnClick("勾选协议")
            }
        })
    }

    private fun saveLoginAgreement() {
        val phone = register_phone.text?.trim().toString()
        val map = HashMap<String, String>()
        val json = Gson().toJson(mViewModel.agreementsList)
        map["agreements"] = json
        map["mobile"] = phone
        map["checkTime"] = checkTime
        map["operateType"] = "1"
        mViewModel.saveLoginAgreement(map)
    }

    private fun editChangeListener() {
        register_btn.observer(
            register_phone,
            register_img_code,
            register_sms_code,
            register_password,
            register_name
        )
        register_btn.setOnItemClickListener { isFlag ->
            register_btn.isEnabled = isFlag && checkPrivacy.isChecked()
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            // 注册
            R.id.register_btn -> {
                jgTrackRegisterBtnClick("注册")
                XyyIoUtil.track("action_register_registerBtn_click")
                postRegisterContent()
            }
            //获取验证码
            R.id.register_request_sms_code -> {
                val phone: String = register_phone.text.toString().trim()
                if (TextUtils.isEmpty(phone) || !UiUtils.isMobileNO(phone)) {
                    ToastUtils.showShort(R.string.validate_mobile_error)
                    register_phone.requestFocus()
                    return
                } else if (TextUtils.isEmpty(register_phone.text) || (register_phone.text?.length
                        ?: 0) <= 2
                ) {
                    ToastUtils.showShort("请输入图形验证码")
                    register_phone.requestFocus()
                    return
                } else {
                    getPhoneCode()
                }
            }
            //获取图片验证
            R.id.register_request_img_code -> {
                showProgress()
                getImageCode()
            }

        }
    }

    /**
     * 初始化Observer
     */
    private fun initObserver() {
        //注册
        mViewModel.registerV2LiveData.observe(this, Observer {
            dismissProgress()
            if (it.isSuccess) {
                val app = application as YBMAppLike
                ToastUtils.showShort("注册成功")
                app.registerSource = null
                app.organSign = null
                RoutersUtils.open("ybmpage://linkshop")
            }
            saveLoginAgreement()
            val map = HashMap<String, Any>()
            map[JGTrackManager.FIELD.FIELD_IS_SUCCESS] = it.isSuccess
            map[JGTrackManager.FIELD.FIELD_REASON] = (if (it.isSuccess) "" else it.data ?: "")
            JGTrackManager.eventTrack(this, "register", map)
        })

        //获取图片验证码
        mViewModel.imgCodeLiveData.observe(this, Observer {
            dismissProgress()
            register_request_sms_code.isEnabled = true
            register_request_img_code.isEnabled = true
            if (it.isSuccess) {
                showImgCode(it.data.codeImg)
                imgCodeId = it.data.code
            } else showImgCode(null)
        })

        //获取手机验证码
        mViewModel.phoneCodeLiveData.observe(this, Observer {
            dismissProgress()
            if (it.isSuccess) {
                register_request_sms_code.isEnabled = false
                register_request_sms_code.setTextColor(
                    ContextCompat.getColor(
                        this@RegisterV2Activity,
                        R.color.sms_request_unavailable_text
                    )
                )
                register_request_sms_code.text = "已发送"
                sendCodeTimeOut()
            }
        })
    }

    /**
     * 开始倒计时
     */
    private fun sendCodeTimeOut() {
        flow {
            for (i in 60 downTo 0) {
                emit(i)
                delay(1000)
            }
        }
            .flowOn(Dispatchers.Default)
            .onCompletion {
                getImageCode()
                register_request_sms_code.isEnabled = true
                register_request_sms_code.setText(R.string.register_request_sms_code_text)
                register_request_sms_code.setTextColor(
                    ContextCompat.getColor(
                        this@RegisterV2Activity,
                        R.color.sms_request_available_text
                    )
                )
            }
            .onEach {
                register_request_sms_code.text = String.format(
                    applicationContext.resources.getString(R.string.register_request_sms_count_down),
                    it
                )
            }
            .flowOn(Dispatchers.Main)
            .launchIn(lifecycleScope)
    }

    /**
     * 展示图片验证码
     */
    private fun showImgCode(url: String?) {
        val imgUrl = if (!TextUtils.isEmpty(url) && !url!!.startsWith("http")) {
            AppNetConfig.getCDNHost() + url
        } else url
        ImageHelper.with(this).load(imgUrl).error(R.drawable.error_img)
            .diskCacheStrategy(DiskCacheStrategy.NONE).into(register_request_img_code)
    }

    /**
     * 注册
     */
    private fun postRegisterContent() {
        showProgress()
        val userName = register_name.text?.trim().toString()
        if (!checkUserName(userName)) {
            ToastUtils.showLong("姓名不能包含特殊符号，且字数在2至8个汉字之间")
            dismissProgress()
            return
        }
        mViewModel.register(
            register_phone.text?.trim().toString(),
            register_password.text?.trim().toString(),
            register_img_code.text?.trim().toString(),
            register_sms_code.text?.trim().toString(),
            register_name.text?.trim().toString()
        )
    }

    /**
     * 检验名称合法性
     */
    private fun checkUserName(userName: String): Boolean {
        return userName.length in 2..8
    }

    /**
     * 获取图片验证码
     */
    private fun getImageCode() {
        register_request_sms_code.isEnabled = false
        register_request_img_code.isEnabled = false
        register_img_code.setText("")
        mViewModel.getImageCode()
    }

    /**
     * 获取手机验证码
     */
    private fun getPhoneCode() {
        showProgress()
        mViewModel.getPhoneCode(
            register_phone.text?.trim().toString(),
            register_img_code.text?.trim().toString(),
            imgCodeId
        )
        jgTrackRegisterBtnClick("获取验证码")
    }

    override fun registerPageProperties(): MutableMap<String, Any> {
        val properties: HashMap<String, Any> = HashMap()
        properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackRegister.PAGE_ID
        properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackRegister.TITLE
        return properties
    }

    override fun registerPageUrl(): String = this.getFullClassName()


}