package com.ybmmarket20.activity

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.Window
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.phoneSensitive
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.viewmodel.RealNameAuthenticationViewModel
import kotlinx.android.synthetic.main.activity_real_name_authentication.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach

/**
 * 实名认证
 */
@Router("realnameauthnetication")
class RealNameAuthenticationActivity: BaseActivity() {

    private val mViewModel: RealNameAuthenticationViewModel by viewModels()

    private var mCustomDialog: CustomDialog? = null

    private var mPhoneMobile = ""

    override fun getContentViewId(): Int = R.layout.activity_real_name_authentication

    override fun initData() {
        setTitle("实名认证")
        val name = intent.getStringExtra("name")
        val idCardNo = intent.getStringExtra("idCardNo") ?: ""
        tvName.text = name
        tvIdCard.text = "${idCardNo.first()}****************${idCardNo.last()}"

        showProgress(false)
        mViewModel.getPhoneMobile()

        tv_cancel_real_name_registration.setOnClickListener {
            //这里每次点击都重置一下
            mCustomDialog = CustomDialog(this,mPhoneMobile).apply {
                mSubmitClickListener = { s: String ->
                    mViewModel.submitCancel(s)
                }

                mGetCodeClickListener = {
                    mViewModel.getPhoneCode()
                }
            }

            mCustomDialog?.show()
        }

    }

    override fun initObserverBefore() {
        super.initObserverBefore()
        mViewModel.phoneCodeLiveData.observe(this){
            val isSuccess = it.first
            val msg = it.second
            if(isSuccess){
                mCustomDialog?.getCodeTextView()?.let {tv->
                    sendCodeTimeOut(tv)
                }
            }else{
                ToastUtils.showShort(msg)
            }
        }

        mViewModel.submitLiveData.observe(this){
            val isSuccess = it.first
            val msg = it.second
            if (isSuccess){
                ToastUtils.showShort(msg)
                mCustomDialog?.dismiss()
                //跳到首页-我的页面
                RoutersUtils.open("ybmpage://main/4")
                finish()
            }else{
                ToastUtils.showShort(msg)
            }

        }

        mViewModel.phoneMobileLiveData.observe(this){
            dismissProgress()
            mPhoneMobile = it
        }
    }


    /**
     * 开始倒计时
     */
    private fun sendCodeTimeOut(mTextView: TextView) {
        flow {
            for (i in 60 downTo 0) {
                emit(i)
                delay(1000)
            }
        }.flowOn(Dispatchers.Default)
                .onCompletion {
                    mTextView.isClickable = true
                    mTextView.setText(R.string.str_cancel_real_name_authentication_get_code)
                    mTextView.setTextColor(ContextCompat.getColor(this@RealNameAuthenticationActivity, R.color.color_00B955))
                }
                .onEach {
                    mTextView.isClickable = false
                    mTextView.text = String.format(
                            applicationContext.resources.getString(R.string.str_cancel_real_name_authentication_code_count_down),
                            it
                    )
                    mTextView.setTextColor(ContextCompat.getColor(this@RealNameAuthenticationActivity, R.color.text_color_999999))
                }
                .flowOn(Dispatchers.Main)
                .launchIn(lifecycleScope)
    }

    private class CustomDialog(context: Context,val mPhoneMobile:String) : Dialog(context) {

        var mGetCodeClickListener:((TextView)->Unit)? = null
        var mSubmitClickListener: ((String) -> Unit)? = null

        override fun onCreate(savedInstanceState: Bundle?) {
            super.onCreate(savedInstanceState)
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            setContentView(R.layout.dialog_cancel_real_name_authentication) // 设置自定义布局
            setCancelable(false) // 设置点击屏幕Dialog不消失

            // 获取Window对象并设置参数以使其居中显示
            val window = window
            val layoutParams = window?.attributes
            layoutParams?.gravity = Gravity.CENTER // 居中显示
            window?.attributes = layoutParams

            val tvCode = findViewById<TextView>(R.id.tv_code)
            val tvWait = findViewById<TextView>(R.id.tv_wait)
            val etCode = findViewById<EditText>(R.id.et_code)
            val tvTips = findViewById<TextView>(R.id.tv_tips)
            val tvCommit = findViewById<RoundTextView>(R.id.tv_commit)

            tvTips.text = context.getString(R.string.str_cancel_real_name_authentication_tip, mPhoneMobile.phoneSensitive()
                    ?: "")

            tvWait.setOnClickListener {
                dismiss()
            }

            tvCode.setOnClickListener { //倒计时
                mGetCodeClickListener?.invoke(it as TextView)
                //etCode获取焦点弹出键盘
                etCode.requestFocus()
                (context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager).showSoftInput(etCode, InputMethodManager.SHOW_IMPLICIT)
            }

            tvCommit.setOnClickListener {
                mSubmitClickListener?.invoke(etCode.text.toString())
            }

            //默认先不可点击
            setCanCommit(tvCommit,false,"")

            etCode.addTextChangedListener {
                if (!it.isNullOrEmpty()){
                    setCanCommit(tvCommit,true,etCode.text.toString())
                }else{
                    setCanCommit(tvCommit,false,etCode.text.toString())
                }
            }

        }
        private fun setCanCommit(tv:TextView,isCan:Boolean,code:String){
            if (isCan){
                tv.isClickable = true
                tv.setBackgroundColor(ContextCompat.getColor(context,R.color.color_00B955))
            }else{
                tv.isClickable = false
                tv.setBackgroundColor(ContextCompat.getColor(context, R.color.color_9900B955))
            }
        }

        fun getCodeTextView() = findViewById<TextView>(R.id.tv_code)
    }
}