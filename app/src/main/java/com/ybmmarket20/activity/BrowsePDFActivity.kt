package com.ybmmarket20.activity

import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import android.view.View
import com.github.barteksc.pdfviewer.listener.OnErrorListener
import com.github.mzule.activityrouter.annotation.Router
import com.tencent.smtt.sdk.TbsReaderView
import com.ybm.app.utils.BugUtil
import com.ybmmarket20.R
import com.ybmmarket20.common.AppUpdateManagerV2
import com.ybmmarket20.common.AppUpdateManagerV2.OnDownloadListener
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.DialogUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.ShareUtil
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import kotlinx.android.synthetic.main.activity_browse_pdf.*
import java.io.File

/**
 * pdf浏览
 */
const val PDF_DOWNLOAD_STATUS_LOADING = 0 // 正在下载

const val PDF_DOWNLOAD_STATUS_SUCCESS = 1 // 下载成功

const val PDF_DOWNLOAD_STATUS_FAILED = 2 // 下载失败

const val PDF_SHARE_WAIT_DOWNLOAD = 0 // 等待下载

const val PDF_SHARE_FAILED = 1 // 分享失败

const val PDF_SHARE_FINISH = 2 // 分享完成

@Router("browsepdfactivity")
class BrowsePDFActivity: BaseActivity(), TbsReaderView.ReaderCallback {

    //标题
    private var mTitle: String = ""
    //pdf的Url
    private var mFileUrl: String? = null
    //下载类
    private var updateManagerV2: AppUpdateManagerV2? = null
    private var mFileName: String? = null
    private var pdfDownloadStatus = PDF_DOWNLOAD_STATUS_LOADING
    private var shareStatus = PDF_SHARE_FINISH
    private var fileName = ""
    private var pdfFile: File? = null
    private var orderNo: String? = null
    private var formType: String? = null

    override fun getContentViewId(): Int = R.layout.activity_browse_pdf

    override fun initData() {
        setRigthImg(ShareClickListener(), R.drawable.icon_pdf_share)
        mFileUrl = intent.getStringExtra("fileUrl")
        mTitle = intent.getStringExtra("title")?: ""
        orderNo = intent.getStringExtra("orderNo")
        formType = intent.getStringExtra("formType")
        if (TextUtils.isEmpty(mTitle)) {
            setTitle("pdf浏览")
        } else {
            setTitle(mTitle)
        }
        mFileName = generateFileName()
        downloadPdf()
        tv_download_to_email.setOnClickListener {
            RoutersUtils.open("ybmpage://aptitudexyyemail?isForm=1&orderNo=$orderNo")
            if (TextUtils.equals(formType, "1") || TextUtils.equals(formType, "2")) {
                XyyIoUtil.track("action_outOrder_downloadToMailbox", hashMapOf(
                    "text" to "下载到邮箱",
                    "type" to if(formType == "1") "0" else "1"
                ))
            }
        }
        if (TextUtils.equals(formType, "1") || TextUtils.equals(formType, "2")) {
            XyyIoUtil.track("action_outOrder", hashMapOf(
                "text" to mTitle,
                "type" to if(formType == "1") "0" else "1"
            ))
        }
    }

    private fun displayFile(file: File) {
        try {
            pdfView.fromFile(file) //                    .pages(0, 2, 1, 3, 3, 3)    // all pages are displayed by default
                .enableSwipe(true) // allows to block changing pages using swipe
                .defaultPage(0) // allows to draw something on the current page, usually visible in the middle of the screen
                .onError(OnErrorListener { t ->
                    BugUtil.sendBug(Throwable("userid = " + SpUtil.getMerchantid() + ";error = " + t.toString() + ";pdfurl = " + mFileUrl))
                    ToastUtils.showShort("PDF文件加载错误")
                })
                .enableAnnotationRendering(true) // render annotations (such as comments, colors or forms)
                .load()
        } catch (e: Exception) {
            BugUtil.sendBug(e)
            val uri = Uri.parse(mFileUrl)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            startActivity(intent)
            finish()
        }
    }

    /**
     * 生成文件名称
     * @return
     */
    private fun generateFileName(): String {
        fileName = System.currentTimeMillis().toString() + ""
        fileName = "$fileName.pdf"
        return fileName
    }

    /**
     * 开始分享
     */
    private fun startShare() {
        if (pdfDownloadStatus == TbsPdfDisplayActivity.PDF_DOWNLOAD_STATUS_LOADING) {
            shareStatus = TbsPdfDisplayActivity.PDF_SHARE_WAIT_DOWNLOAD
        } else if (pdfDownloadStatus == TbsPdfDisplayActivity.PDF_DOWNLOAD_STATUS_FAILED) {
            shareStatus = TbsPdfDisplayActivity.PDF_SHARE_WAIT_DOWNLOAD
            downloadPdf()
        } else if (pdfDownloadStatus == TbsPdfDisplayActivity.PDF_DOWNLOAD_STATUS_SUCCESS) {
            share()
            shareStatus = TbsPdfDisplayActivity.PDF_SHARE_FINISH
        }
    }

    private fun share() {
        if (pdfFile == null) return
        DialogUtil.shareDialogInvoice(this, { content: String ->
            if ("wx" == content) {
                ShareUtil.sharePdfFileWechatFriend(this, pdfFile)
            }
        }, DialogUtil.SHARE_TYPE_WECHAT)
    }

    private fun downloadPdf() {
        updateManagerV2 = AppUpdateManagerV2()
        updateManagerV2?.setDownloadListener(object : OnDownloadListener {
            override fun onDownloadSuccess(file: File) {
                pdfFile = file
                displayFile(file)
                pdfDownloadStatus = TbsPdfDisplayActivity.PDF_DOWNLOAD_STATUS_SUCCESS
                if (shareStatus == TbsPdfDisplayActivity.PDF_SHARE_WAIT_DOWNLOAD) {
                    startShare()
                }
            }

            override fun onDownloading(progress: Int) {
                pdfDownloadStatus = TbsPdfDisplayActivity.PDF_DOWNLOAD_STATUS_LOADING
            }

            override fun onDownloadFailed(e: java.lang.Exception) {
                BugUtil.sendBug(Throwable("pdf文件下载失败"))
                pdfDownloadStatus = TbsPdfDisplayActivity.PDF_DOWNLOAD_STATUS_FAILED
                if (shareStatus == TbsPdfDisplayActivity.PDF_SHARE_WAIT_DOWNLOAD) {
                    ToastUtils.showShort("文件下载失败")
                }
            }
        })
        updateManagerV2?.downFile(mFileUrl, mFileName)
    }

    inner class ShareClickListener : View.OnClickListener {
        override fun onClick(v: View) {
            startShare()
        }
    }

    override fun onCallBackAction(p0: Int?, p1: Any?, p2: Any?) {

    }
}