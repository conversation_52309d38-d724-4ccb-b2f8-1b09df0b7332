package com.ybmmarket20.activity

import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.github.mzule.activityrouter.annotation.Router
import com.google.gson.reflect.TypeToken
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.view.CommonRecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.adapter.GoodsListAdapter
import com.ybmmarket20.bean.*
import com.ybmmarket20.bean.loadmore.IPage
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.ROUTER_CRM_RECOMMEND_HISTORY
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import kotlinx.android.synthetic.main.activity_crm_recommend.*
import java.lang.reflect.Type
import java.text.SimpleDateFormat
import java.util.*

/**
 * <AUTHOR> Brin
 * @date : 2020/6/28 - 14:35
 * @Description :
 * @version
 */
@Router("crmrecommendactivity", "crmrecommendactivity/:recommendDate")
class CrmRecommendActivity : RefreshActivity<RowsBean, IPage<RowsBean>>() {

    private var adapter: GoodsListAdapter? = null
    private var recommendDate: String? = null
    private var isRecommendHomePage = false

    override fun initData() {
        setTitle("专属推荐")
        recommendDate = intent.getStringExtra("recommendDate")
        takeIf { recommendDate?.length ?: 0 > 0 }?.apply {
            setTitle("推荐商品列表")
            tv_right?.visibility = View.GONE
            isRecommendHomePage = true
        }
        super.initData()
        loadData()
    }

    fun onClick(view: View) {
        when (view) {
            tv_right -> RoutersUtils.open(ROUTER_CRM_RECOMMEND_HISTORY)
        }
    }

    override fun getRequestParams(): RequestParams {
        val params = RequestParams()
        if (isRecommendHomePage) params.put("recommendDate", recommendDate)
        return params
    }

    override fun getAdapter(rows: List<RowsBean>): YBMBaseAdapter<RowsBean> {
        if (adapter == null) {
            adapter = GoodsListAdapter(R.layout.item_goods, rows)
            adapter?.let {
                it.setOnListItemClickListener { row -> RoutersUtils.open("ybmpage://productdetail/" + row.getId()) }
            }
            adapter?.takeIf { isRecommendHomePage }
                    ?.apply { addHeaderView(getHeadView()) }
        }
        return adapter as GoodsListAdapter
    }

    private fun getHeadView(): View? {
        return TextView(this)?.apply {
            layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, UiUtils.dp2px(30))
            setBackgroundResource(R.color.color_f7f7f8)
            setPadding(UiUtils.dp2px(14), 0, UiUtils.dp2px(14), 0)
            setTextColor(resources.getColor(R.color.text_575766))
            setTextSize(15.toFloat())
            gravity = Gravity.CENTER_VERTICAL
//            text = "推荐时间： ${recommendDate}"
            text = "推荐时间： ${SimpleDateFormat("yyyy-MM-dd HH:mm").format(Date(recommendDate?.toLong() ?: -1))}"
        }
    }

    override fun getType(): Type = object : TypeToken<BaseBean<RefreshCrmRecommendBean<RowsBean>>>() {}.type

    override fun getUrl(): String = if (!isRecommendHomePage) AppNetConfig.CRM_RECOMMEND_LIST else AppNetConfig.CRM_RECOMMEND_RECORD_DETAIL_LIST

    override fun getCommonRecyclerView(): CommonRecyclerView = crl_commend

    override fun getContentViewId(): Int = R.layout.activity_crm_recommend

    override fun getStartPage() = 0

    override fun getPageName(): String? = if (!isRecommendHomePage) XyyIoUtil.PAGE_RECOMMENDED_LIST else XyyIoUtil.PAGE_RECOMMENDED_DETAILS

}