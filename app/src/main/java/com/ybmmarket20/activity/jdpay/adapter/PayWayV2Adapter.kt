package com.ybmmarket20.activity.jdpay.adapter

import android.content.Context
import android.graphics.Color
import android.text.Html
import android.text.SpannableStringBuilder
import android.view.View
import android.widget.ImageView
import android.widget.RadioButton
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.PayTypeAddBankCard
import com.ybmmarket20.bean.PayTypeBankCard
import com.ybmmarket20.bean.PayTypeCommon
import com.ybmmarket20.bean.PayTypeEntry
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.StringUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.*
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter

/**
 * 收银台V2
 */
class PayWayV2Adapter(list: MutableList<PayTypeEntry>, private val mPayWayV2ViewModel: PayWayV2ViewModel) :
    YBMBaseMultiItemAdapter<PayTypeEntry>(list) {

    var changeCardCallback: (() -> Unit)? = null
    var selectItemCallback: ((payCode: String, cardId: String?, payItemType: Int) -> Unit)? = null

    init {
        //银行组
        addItemType(PAY_LAYOUT_TYPE_BANK_GROUP, R.layout.item_pay_way_bank_card_goup)
        //银行卡
        addItemType(PAY_LAYOUT_TYPE_BANK_CARD, R.layout.item_pay_way_bank_card_uncheck)
        //支付类型
        addItemType(PAY_LAYOUT_TYPE_BANK_ITEM, R.layout.item_pay_way_type)
        //平安贷
        addItemType(PAY_LAYOUT_TYPE_BANK_ITEM_PINGAN, R.layout.item_pay_way_type)
        //小雨点
        addItemType(PAY_LAYOUT_TYPE_XYD, R.layout.item_pay_way_type)
        //农行链e贷
        addItemType(PAY_LAYOUT_TYPE_NONG, R.layout.item_pay_way_type)
        //金蝶
        addItemType(PAY_LAYOUT_TYPE_JINDIE, R.layout.item_pay_way_type)
        //查看全部银行卡
        addItemType(PAY_LAYOUT_TYPE_BANK_CARD_CHECK_ALL, R.layout.item_pay_way_check_all)
        //添加银行卡
        addItemType(PAY_LAYOUT_TYPE_BANK_CARD_ADD, R.layout.item_pay_way_add_bank_card)
        //展开更多支付方式
        addItemType(PAY_LAYOUT_TYPE_EXPAND_MORE, R.layout.item_pay_way_expand_more)
        //推荐银行卡
        addItemType(PAY_LAYOUT_TYPE_BANK_CARD_RECOMMEND, R.layout.item_pay_way_bank_card_uncheck)
        //银行卡提示
        addItemType(PAY_LAYOUT_TYPE_BANK_CARD_TIPS, R.layout.item_pay_way_bank_card_tips)
        //银行卡头部
        addItemType(PAY_LAYOUT_TYPE_BANK_CARD_HEAD, R.layout.item_pay_way_bank_card_head)
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: PayTypeEntry?) {
        whenAllNotNull(baseViewHolder, t) {holder, bean ->
            val payWayV2ItemState = when(bean.itemType) {
                PAY_LAYOUT_TYPE_BANK_GROUP -> PayWayV2ItemState.PayWayV2ItemBankGroupState(mContext)
                PAY_LAYOUT_TYPE_BANK_CARD -> PayWayV2ItemState.PayWayV2ItemBankCardState(mContext, changeCardCallback)
                PAY_LAYOUT_TYPE_BANK_CARD_RECOMMEND -> PayWayV2ItemState.PayWayV2ItemBankCardRecommendState(mContext)
                PAY_LAYOUT_TYPE_BANK_CARD_TIPS -> PayWayV2ItemState.PayWayV2ItemPayTypeNoneState
                PAY_LAYOUT_TYPE_BANK_CARD_HEAD -> PayWayV2ItemState.PayWayV2ItemPayTypeNoneState
                PAY_LAYOUT_TYPE_BANK_ITEM -> PayWayV2ItemState.PayWayV2ItemPayTypeState(mContext)
                PAY_LAYOUT_TYPE_XYD -> PayWayV2ItemState.PayWayV2XydState(mContext)
                PAY_LAYOUT_TYPE_BANK_ITEM_PINGAN -> PayWayV2ItemState.PayWayV2PingAnState(mContext)
                PAY_LAYOUT_TYPE_NONG -> PayWayV2ItemState.PayWayV2NongState(mContext)
                PAY_LAYOUT_TYPE_JINDIE -> PayWayV2ItemState.PayWayV2JinDieState(mContext)
                PAY_LAYOUT_TYPE_BANK_CARD_CHECK_ALL -> PayWayV2ItemState.PayWayV2CheckAllState(
                    mPayWayV2ViewModel
                )
                PAY_LAYOUT_TYPE_BANK_CARD_ADD -> PayWayV2ItemState.PayWayV2AddBankCardState(
                    mPayWayV2ViewModel
                )
                PAY_LAYOUT_TYPE_EXPAND_MORE -> PayWayV2ItemState.PayWayV2ExpandMoreState(
                    mPayWayV2ViewModel
                )
                else -> null
            }
            payWayV2ItemState?.bindItemView(holder, bean)
//            payWayV2ItemState?.getRadioButton(holder)?.setOnCheckedChangeListener {_, isChecked ->
//                if (isChecked && bean is PayTypeCommon) {
//                    selectItemCallback?.invoke(bean.payCode?: "")
//                }
//            }

            payWayV2ItemState?.getClickView(holder)?.setOnClickListener {
                val isChecked = payWayV2ItemState.getRadioButton(holder)?.isChecked?: false
                if (!isChecked && bean is PayTypeCommon && !bean.isSelected) {
                    if (!bean.canUse) return@setOnClickListener
                    selectItemCallback?.invoke(bean.payCode?: "", bean.payId, bean.itemType)
                }
            }

        }
    }

    sealed class PayWayV2ItemState {

        abstract fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry)

        open fun getRadioButton(holder: YBMBaseHolder): RadioButton? = null

        open fun getClickView(holder: YBMBaseHolder): View? = null

        object PayWayV2ItemPayTypeNoneState: PayWayV2ItemState() {
            override fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {

            }

        }

        /**
         * 支付类型
         */
        open class PayWayV2ItemPayTypeState(val context: Context): PayWayV2ItemState() {
            override fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
                val payBean = bean as PayTypeCommon
                val logoView = holder.getView<ImageView>(R.id.ivPayItem)
                val tvPayItemDes = holder.getView<TextView>(R.id.tvPayItemDes)
                val ivPayItemTag = holder.getView<ImageView>(R.id.ivPayItemTag)
                val tvPayItemTips = holder.getView<TextView>(R.id.tvPayItemTips)
                tvPayItemTips.visibility = if (bean.pingAnTips.isNullOrEmpty()) View.GONE else View.VISIBLE
                ImageUtil.load(context, payBean.logoUrl, logoView)
                ImageUtil.loadNoPlace(context, payBean.mktIcon, ivPayItemTag)
                holder.setText(R.id.tvPayItemTitle, payBean.payName)
                tvPayItemDes.text = payBean.mktTip?: ""
                payBean.mktTipColor?.let {
                    tvPayItemDes.setTextColor(Color.parseColor(it))
                }
                val radio = holder.getView<RadioButton>(R.id.rbPayItem)
                radio.isChecked = payBean.isSelected
                radio.isEnabled = payBean.canUse
                try {
                    val tvPayItemBottomTips = holder.getView<TextView>(R.id.tvPayItemBottomTips)
                    if (bean.tips.isNullOrEmpty()) {
                        tvPayItemBottomTips.visibility = View.GONE
                        tvPayItemDes.visibility = View.VISIBLE
                    } else {
                        tvPayItemBottomTips.visibility = View.VISIBLE
                        tvPayItemBottomTips.text = bean.tips
                        tvPayItemDes.visibility = if (payBean.mktTip.isNullOrEmpty()) View.GONE
                        else View.VISIBLE
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            override fun getRadioButton(holder: YBMBaseHolder): RadioButton? = holder.getView(R.id.rbPayItem)

            override fun getClickView(holder: YBMBaseHolder): View? = holder.getView(R.id.clickView)
        }

        /**
         * 银行组
         */
        class PayWayV2ItemBankGroupState(private val context: Context) : PayWayV2ItemState() {
            override fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
                val payBean = bean as PayTypeCommon
                val logoView = holder.getView<ImageView>(R.id.ivPayItem)
                val ivPayItemTag = holder.getView<ImageView>(R.id.ivPayItemTag)
                val tvTips = holder.getView<TextView>(R.id.tvTips)
                ImageUtil.load(context, payBean.logoUrl, logoView)
                holder.setText(R.id.tvPayItemTitle, payBean.payName)
                ImageUtil.loadNoPlace(context, payBean.mktIcon, ivPayItemTag)
                tvTips.text = payBean.note
            }

            override fun getClickView(holder: YBMBaseHolder): View? = holder.getView(R.id.clickView)
        }

        /**
         * 银行卡
         */
        open class PayWayV2ItemBankCardState(val context: Context, private val changeCardCallback: (() -> Unit)? = null): PayWayV2ItemState() {
            override fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
                val payBean = bean as PayTypeBankCard
                val logoView = holder.getView<ImageView>(R.id.ivLogo)
                val tvBankName = holder.getView<TextView>(R.id.tvBankName)
                val tvChangeBank = holder.getView<TextView>(R.id.tvChangeBank)
                val rtvTips = holder.getView<RoundTextView>(R.id.rtvTips)
                val radio = holder.getView<RadioButton>(R.id.rbPayItem)
                ImageUtil.load(context, payBean.logoUrl, logoView)
                tvBankName.text = payBean.payName
                rtvTips.text = payBean.mktTip
                rtvTips.visibility = if (payBean.mktTip.isNullOrEmpty()) View.GONE else View.VISIBLE
                tvChangeBank.setOnClickListener {
                    changeCardCallback?.invoke()
                }
                tvChangeBank.visibility = if (payBean.cardCount == 1) View.GONE else View.VISIBLE
                radio.isChecked = payBean.isSelected
                radio.isEnabled = payBean.canUse
            }
            override fun getRadioButton(holder: YBMBaseHolder): RadioButton? = holder.getView(R.id.rbCheck)

            override fun getClickView(holder: YBMBaseHolder): View? = holder.getView(R.id.clickView)
        }

        /**
         * 推荐银行卡
         */
        class PayWayV2ItemBankCardRecommendState(val mContext: Context):
            PayWayV2ItemBankCardState(mContext, null) {
            override fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
                super.bindItemView(holder, bean)
                val payBean = bean as PayTypeCommon
                val tvChangeBank = holder.getView<TextView>(R.id.tvChangeBank)
                tvChangeBank.visibility = View.GONE
                val radio = holder.getView<RadioButton>(R.id.rbPayItem)
                radio.isChecked = payBean.isSelected
                radio.isEnabled = payBean.canUse
            }
            override fun getRadioButton(holder: YBMBaseHolder): RadioButton? = holder.getView(R.id.rbPayItem)

            override fun getClickView(holder: YBMBaseHolder): View? = holder.getView(R.id.clickView)
        }

        /**
         * 平安贷
         */
        class PayWayV2PingAnState(context: Context): PayWayV2ItemPayTypeState(context) {
            override fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
                super.bindItemView(holder, bean)
                val payBean = bean as PayTypeCommon
                val radio = holder.getView<RadioButton>(R.id.rbPayItem)
                val tvPayItemDes = holder.getView<TextView>(R.id.tvPayItemDes)
                val tvPayItemTips = holder.getView<TextView>(R.id.tvPayItemTips)
                tvPayItemTips.visibility =
                    if (bean.pingAnTips.isNullOrEmpty()) View.GONE else View.VISIBLE
                if (!bean.pingAnTips.isNullOrEmpty()) {
                    tvPayItemTips.text = Html.fromHtml(bean.pingAnTips)
                }
            }

            override fun getRadioButton(holder: YBMBaseHolder): RadioButton? =
                holder.getView(R.id.rbPayItem)

            override fun getClickView(holder: YBMBaseHolder): View? = holder.getView(R.id.clickView)
        }

        /**
         * 小雨点
         * @constructor
         */
        class PayWayV2XydState(context: Context): PayWayV2ItemPayTypeState(context) {
            override fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
                super.bindItemView(holder, bean)
                bean as PayTypeCommon
                val tvPayItemTips = holder.getView<TextView>(R.id.tvPayItemTips)
                val des = "可用额度 ￥" + StringUtil.DecimalFormat2Double(bean.xydPrice)

                tvPayItemTips.visibility =
                        if (bean.xydPrice.isNullOrEmpty()) View.GONE else View.VISIBLE
                tvPayItemTips.text = des
            }

            override fun getRadioButton(holder: YBMBaseHolder): RadioButton? =
                    holder.getView(R.id.rbPayItem)

            override fun getClickView(holder: YBMBaseHolder): View? = holder.getView(R.id.clickView)
        }
        /**
         * 金蝶
         */
        class PayWayV2JinDieState(context: Context): PayWayV2ItemPayTypeState(context) {
            override fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
                super.bindItemView(holder, bean)
                bean as PayTypeCommon
                val tvPayItemTips = holder.getView<TextView>(R.id.tvPayItemTips)
                val des = "可用额度 ￥" + StringUtil.DecimalFormat2Double(bean.xydPrice)

                tvPayItemTips.visibility =
                        if (bean.xydPrice.isNullOrEmpty()) View.GONE else View.VISIBLE
                tvPayItemTips.text = des
            }

            override fun getRadioButton(holder: YBMBaseHolder): RadioButton? =
                    holder.getView(R.id.rbPayItem)

            override fun getClickView(holder: YBMBaseHolder): View? = holder.getView(R.id.clickView)
        }


        /**
         * 农行链e贷
         */
        class PayWayV2NongState(context: Context) : PayWayV2ItemPayTypeState(context) {
            override fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
                super.bindItemView(holder, bean)
            }

            override fun getRadioButton(holder: YBMBaseHolder): RadioButton? =
                holder.getView(R.id.rbPayItem)

            override fun getClickView(holder: YBMBaseHolder): View? = holder.getView(R.id.clickView)
        }

        /**
         * 查看全部银行卡
         */
        class PayWayV2CheckAllState(private val payWayV2ViewModel: PayWayV2ViewModel) :
            PayWayV2ItemState() {
            override fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
                holder.itemView.setOnClickListener {
                    payWayV2ViewModel.expandBankCardList()
                }
            }
        }

        /**
         * 添加银行卡
         */
        class PayWayV2AddBankCardState(private val payWayV2ViewModel: PayWayV2ViewModel): PayWayV2ItemState() {
            override fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
                val payBean = bean as PayTypeAddBankCard
                holder.itemView.setOnClickListener {
                    payWayV2ViewModel.showLoading()
//                    payWayV2ViewModel.queryPWSettingStatus()
                    payWayV2ViewModel.jumpToAddBankCard()
                    XyyIoUtil.track("cashier_add_bank_card")
                }
                val rtvTips = holder.getView<RoundTextView>(R.id.rtvTips)
                rtvTips.text = payBean.mktTip
                rtvTips.visibility = if (payBean.mktTip.isNullOrEmpty()) View.GONE else View.VISIBLE
                val radio = holder.getView<RadioButton>(R.id.rbPayItem)
                radio.isChecked = payBean.isSelected
                radio.isEnabled = payBean.canUse
            }

            override fun getClickView(holder: YBMBaseHolder): View? = holder.getView(R.id.rbPayItem)
        }

        /**
         * 展开更多支付方式
         */
        class PayWayV2ExpandMoreState(private val payWayV2ViewModel: PayWayV2ViewModel): PayWayV2ItemState() {
            override fun bindItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
                holder.itemView.setOnClickListener {
                    payWayV2ViewModel.expandPayTypeList()
                }
            }
        }
    }
}