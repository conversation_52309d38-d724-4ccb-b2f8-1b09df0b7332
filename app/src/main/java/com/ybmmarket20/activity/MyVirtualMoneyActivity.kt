package com.ybmmarket20.activity

import android.annotation.SuppressLint
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.lifecycle.Observer
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.bean.ShoppingGoldRechargeBean
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.fragments.MyVirtualMoneyFragment
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.ShoppingGoldRechargeDialog
import com.ybmmarket20.viewmodel.VirtualMoneyViewModel
import kotlinx.android.synthetic.main.activity_virtual_money.*

/**
 * 我的购物金
 */
@Router("myvirtualmoney")
class MyVirtualMoneyActivity: BaseActivity() {

    val mViewModel: VirtualMoneyViewModel by viewModels()

    var mBean: ShoppingGoldRechargeBean? = null

    //弹一次就行了
    var showRechargeDialog: Boolean = false

    val fragmentList = listOf(
            MyVirtualMoneyFragment.getInstance("1"),
            MyVirtualMoneyFragment.getInstance("2"),
            MyVirtualMoneyFragment.getInstance("3")
    )

    private lateinit var mAdapter: MyVirtualMoneyAdapter
    override fun getContentViewId(): Int = R.layout.activity_virtual_money

    override fun initData() {
        showRechargeDialog = intent.getStringExtra("showRechargeDialog") == "1"

        XyyIoUtil.track("action_virtualGold", hashMapOf("page_name" to "我的购物金"))
        vp_virtual_money.offscreenPageLimit = 3
        mAdapter = MyVirtualMoneyAdapter(supportFragmentManager)
        vp_virtual_money.adapter = mAdapter
        stl_virtual_money.setViewPager(vp_virtual_money, arrayOf("全部记录", "收入记录", "支出记录"))
        setTitle("我的购物金")
        setRigthText({
            val url = "ybmpage://commonh5activity?url=" + AppNetConfig.SHOPPING_GOLD_DETAIL_INFO
            RoutersUtils.open(url)
        }, "使用说明")
        mViewModel.virtualMoneyLiveData.observe(this, Observer {
            if (it.isSuccess && it.data != null) {
                tv_virtual_money_amount.text = UiUtils.transform(it.data.availableVirtualGold)
                cl_pingan_user.setOnClickListener { _ ->
                    if (it.data.isPingAnAccountOpen()){
                        RoutersUtils.open("ybmpage://commonh5activity?url="+AppNetConfig.OUT_TO_PINGAN_ACCOUNT)
                    }else{
                        val dialogEx = AlertDialogEx(this)
                        dialogEx.setTitle("")
                                .setMessage("您未开通平安商户号，暂不支持转出到平安商户")
                                .setCancelButton("稍后设置", "#9494A5",
                                        AlertDialogEx.OnClickListener { _, _ -> })
                                .setConfirmButton("去开通",
                                        AlertDialogEx.OnClickListener { _, _ ->
                                            RoutersUtils.open("ybmpage://commonh5activity?url="+AppNetConfig.SHOPPING_GOLD_USER_INFO)
                                        })
                                .show()
                    }
                }
            }
        })

        cl_top_up.setOnClickListener {
//            RoutersUtils.open("ybmpage://commonh5activity?url="+AppNetConfig.SHOPPING_GOLD_USER_INFO)

            mBean?.let {
                ShoppingGoldRechargeDialog(this, it).apply {
                    show()
                }
            }
        }

        mViewModel.shoppingGoldRechargeBeanData.observe(this){
            if (it.isSuccess){
                mBean = it.data
                val tips = mBean?.highLevelRedPacketMsg?:""
                tv_top_up.isVisible = tips.isNotEmpty()
                tv_top_up.text = tips
                cl_top_up.isVisible = mBean != null

                if (showRechargeDialog){
                    cl_top_up.performClick()
                    showRechargeDialog = false
                }
            }
        }

    }


    fun getFragments(): List<Fragment> {
        return fragmentList
    }

    @SuppressLint("WrongConstant")
    inner class MyVirtualMoneyAdapter(fm: FragmentManager): FragmentStatePagerAdapter(fm, BEHAVIOR_SET_USER_VISIBLE_HINT) {
        override fun getCount(): Int = getFragments().size

        override fun getItem(position: Int): Fragment = getFragments()[position]

    }

    override fun onResume() {
        super.onResume()
        mViewModel.getVirtualMoney()
        mViewModel.getShoppingGoldRechargeBean()
        for (i in 0 until mAdapter.count) {
            mAdapter.getItem(i).let {
                if (it is MyVirtualMoneyFragment) {
                    it.refreshData()
                }
            }
        }
    }
}