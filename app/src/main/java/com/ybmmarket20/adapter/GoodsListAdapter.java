package com.ybmmarket20.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.util.SparseArray;
import android.util.TypedValue;
import android.view.View;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.BaseProductActivity;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.LabelIconBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.widget.RoundTextView;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.AuditStatusSyncUtil;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.ImageUtil;
import com.ybmmarket20.utils.ImageUtilKt;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.AnalysisConst;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.CommonDialogLayout;
import com.ybmmarket20.view.MyImageSpan;
import com.ybmmarket20.view.ProductEditLayout;
import com.ybmmarket20.view.PromotionTagView;
import com.ybmmarket20.view.ShowPromotionPopWindow;
import com.ybmmarket20.view.TagView;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品列表Adapter
 * 注意  项目中很多界面都复用这一个Adapter，因此修改需要注意啊
 */
public class GoodsListAdapter extends YBMBaseAdapter<RowsBean> {

    private boolean isItemShowMargingBottom = true;//是否显示item布局的marginBottom
    protected boolean isShop = false;
    protected int currPosition = -1;
    protected int pageFrom = ProductEditLayout.FROMPAGE_ACTIVITS;
    protected boolean mIsCollect = false;//是否显示收藏按钮
    private boolean isShowAddEdit = true;//是否显示购买按钮
    private boolean isShowSalesQuantity = false;//是否显示销量
    private boolean isCanDelete = false;//是否显示删除
    private boolean isHaveGoods = true;//是否有货
    private boolean isCanBuy = true;//是否控销可以购买
    private boolean isOEMAndNotSigned = false;//是OEM商品并且没有签署协议
    private boolean isShowRemind = false;//是否显示到货提醒
    private boolean isKaUser;


    private ShowPromotionPopWindow mPopWindowPromotion;
    private boolean animationEnable;


    private SparseArray<String> traceProductData = new SparseArray<>();

    private BaseFlowData mFlowData;

    private boolean isShowOftenBuyTypeName = false;

    public void setFlowData(BaseFlowData flowData) {
        clearTranceData();
        mFlowData = flowData;
    }

    /**
     * 设置是否显示显示销售量
     *
     * @param isShowSalesQuantity
     */
    public void setIsShowSalesQuantity(boolean isShowSalesQuantity) {
        this.isShowSalesQuantity = isShowSalesQuantity;
    }

    /**
     * 设置是否显示删除功能
     *
     * @param isCanDelete
     */
    public void setIsCanDelete(boolean isCanDelete) {
        this.isCanDelete = isCanDelete;
    }

    public GoodsListAdapter(int layoutResId, List<RowsBean> data) {
        super(layoutResId, data);
        if (BaseYBMApp.getApp().getCurrActivity() != null && BaseYBMApp.getApp().getCurrActivity() instanceof BaseProductActivity) {
            pageFrom = ProductEditLayout.FROMPAGE_ACTIVITS;
        } else {
            pageFrom = ProductEditLayout.FROMPAGE_HOME;
        }
    }

    public GoodsListAdapter(int layoutResId, Context context, int pageFrom) {
        super(layoutResId, null);
        this.pageFrom = pageFrom;
        mContext = context;
    }


    /**
     * 为了配合后端加购埋点
     *
     * @param layoutResId
     * @param data
     * @param isBrand
     * @param isCollect
     * @param sourceId    页面跳转入口（如搜索页，全部分类页）
     */
    public GoodsListAdapter(int layoutResId, List<RowsBean> data, boolean isBrand, boolean isCollect, String sourceId, boolean isKaUser) {
        this(layoutResId, data, isBrand, isCollect);
        this.isKaUser = isKaUser;
    }


    public GoodsListAdapter(int layoutResId, List<RowsBean> data, boolean isBrand, boolean isCollect) {
        super(layoutResId, data);
        this.mIsCollect = isCollect;
        if (isBrand) {
            pageFrom = ProductEditLayout.FROMPAGE_ALL_PRODUCT;
        } else {
            pageFrom = ProductEditLayout.FROMPAGE_ACTIVITS;
        }
    }

    public GoodsListAdapter(int layoutResId, List<RowsBean> data, boolean isBrand, boolean isCollect, boolean isItemShowMargingBottom) {
        super(layoutResId, data);
        this.mIsCollect = isCollect;
        this.isItemShowMargingBottom = isItemShowMargingBottom;
        if (isBrand) {
            pageFrom = ProductEditLayout.FROMPAGE_ALL_PRODUCT;
        } else {
            pageFrom = ProductEditLayout.FROMPAGE_ACTIVITS;
        }
    }

    public GoodsListAdapter(int layoutResId, List<RowsBean> data, boolean isShop) {
        super(layoutResId, data);
        this.isShop = isShop;
    }

    @Override
    protected void bindItemView(final YBMBaseHolder baseViewHolder, final RowsBean rowsBean) {
        if (!isItemShowMargingBottom) {
            ConstraintLayout ll_item_root = baseViewHolder.getView(R.id.ll_item_root);
            RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) ll_item_root.getLayoutParams();
            params.bottomMargin = 0;
        }

        baseViewHolder.getConvertView().setOnClickListener(v -> {

            // 商品点击埋点
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("id", rowsBean.getId());
            } catch (JSONException e) {
                e.printStackTrace();
            }
            if (!TextUtils.isEmpty(rowsBean.zhugeEventName)) {
                // 这里的埋点有，首页商品，首页推荐商品, 首页常购清单,诊所专区clinic
                XyyIoUtil.track(rowsBean.zhugeEventName, jsonObject, rowsBean);
            }

            if (mOnItemClickListener != null) {
                currPosition = baseViewHolder.getAdapterPosition();
                rowsBean.rowsFlow = mFlowData;
                mOnItemClickListener.onItemClick(rowsBean);
            }
        });

        // 曝光埋点
        if (mFlowData != null && traceProductData.get(baseViewHolder.getAdapterPosition()) == null) {
            FlowDataEventAnalysisKt.flowDataPageListPageExposure(mFlowData, rowsBean.getProductId(), rowsBean.getShowName(), rowsBean.sourceType, "");
            traceProductData.put(baseViewHolder.getAdapterPosition(), rowsBean.getProductId());
        } else {
            // 曝光埋点(老版本)
            if (!TextUtils.isEmpty(traceId) && traceProductData.get(baseViewHolder.getAdapterPosition()) == null) {
                // 上报
                try {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("attribution", traceId);
                    jsonObject.put("commodityId", rowsBean.getProductId());
                    jsonObject.put("commodityName", rowsBean.getShowName());
                    XyyIoUtil.track(XyyIoUtil.PAGE_LISTPAGE_EXPOSURE, jsonObject,rowsBean);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                traceProductData.put(baseViewHolder.getAdapterPosition(), rowsBean.getProductId());
            }
        }


        isHaveGoods = true;
        isCanBuy = true;
        isOEMAndNotSigned = false;
        isShowRemind = false;

        TextView tv_shop_name = baseViewHolder.getView(R.id.shop_name);
        TextView tvBrandControl = baseViewHolder.getView(R.id.tv_brand_control);
        TextView tv_shop_pack_size = baseViewHolder.getView(R.id.shop_price_tv);
        TextView tv_shop_price = baseViewHolder.getView(R.id.shop_price);
        CheckBox shop_ck = baseViewHolder.getView(R.id.shop_ck);
        LinearLayout shop_ck_ll = baseViewHolder.getView(R.id.shop_ck_ll);
        ImageView ivShopMark = baseViewHolder.getView(R.id.iv_shop_mark);
        ProductEditLayout editLayout = baseViewHolder.getView(R.id.el_edit);
        TextView tvOEM = baseViewHolder.getView(R.id.tv_oem);
        LinearLayout llCompanyName = (LinearLayout) getSafeView(baseViewHolder, R.id.ll_company_name);
        LinearLayout ll_gong = (LinearLayout) getSafeView(baseViewHolder, R.id.ll_gong);
        TextView tv_original_price = baseViewHolder.getView(R.id.tv_original_price);
        TextView shop_no_limit_tv01 = baseViewHolder.getView(R.id.shop_no_limit_tv01);
        TextView tv_ziying = baseViewHolder.getView(R.id.icon_cart_proprietary);
//        View view_line = getSafeView(baseViewHolder, R.id.view_line);
        ImageView iv_remind = (ImageView) getSafeView(baseViewHolder, R.id.iv_remind);//到货提醒按钮，有可能返回null 需注意
        // 删除医保标签
        TextView tv_tag = (TextView) getSafeView(baseViewHolder, R.id.tv_health_insurance);//医保等标签
        ImageView iv_delete = (ImageView) getSafeView(baseViewHolder, R.id.iv_goods_item_delete);//删除整个item
        LinearLayout llShowPromotion = baseViewHolder.getView(R.id.ll_show_promotion);
        TextView ivGrossMargin = baseViewHolder.getView(R.id.icon_gross_margin);

        PromotionTagView mPtv = baseViewHolder.getView(R.id.view_ptv);
        TextView tv_validity_period = baseViewHolder.getView(R.id.tv_validity_period);
        LinearLayout ll_validity_period = baseViewHolder.getView(R.id.ll_validity_period);
        TextView tv_oftenBuyTypeName = baseViewHolder.getView(R.id.tv_oftenBuyTypeName);

        //购买次数
        RoundTextView rtvBuyCount = (RoundTextView) getSafeView(baseViewHolder, R.id.rtv_buy_count);
        //数据标签 标签类型 7, "60天最低价",8, "区域毛利榜" 10, "比上次购买时降xx元",11, "比加入时降xx元",12, "品类点击榜"
        TagView dataTagListView = (TagView) getSafeView(baseViewHolder, R.id.data_tag_list_view);

        setViewVisibility(rtvBuyCount, TextUtils.isEmpty(rowsBean.buyedCountStr) ? View.GONE : View.VISIBLE);
        if (rtvBuyCount != null) {
            rtvBuyCount.setText(rowsBean.buyedCountStr);
        }

        if (dataTagListView != null && rowsBean.dataTagList != null && rowsBean.dataTagList.size() > 0) {
            setViewVisibility(dataTagListView, View.VISIBLE);
            dataTagListView.bindData(rowsBean.dataTagList);
        } else {
            setViewVisibility(dataTagListView, View.GONE);
        }

        if (isShowOftenBuyTypeName && tv_oftenBuyTypeName != null && !TextUtils.isEmpty(rowsBean.oftenBuyTypeName)) {
            tv_oftenBuyTypeName.setVisibility(View.VISIBLE);
            tv_oftenBuyTypeName.setText(rowsBean.oftenBuyTypeName);
        } else {
            if (tv_oftenBuyTypeName != null) {
                tv_oftenBuyTypeName.setVisibility(View.GONE);
            }
        }

        if (null != tv_validity_period && null != ll_validity_period && rowsBean != null) {
            setViewVisibility(ll_validity_period, View.VISIBLE);
            if ((StringUtil.isEmpty(rowsBean.getNearEffect()) || "-".equals(rowsBean.getNearEffect())) || (StringUtil.isEmpty(rowsBean.getFarEffect()) || "-".equals(rowsBean.getFarEffect()))) {
                tv_validity_period.setText("-");
            } else {
                tv_validity_period.setText(rowsBean.getNearEffect() + "/" + rowsBean.getFarEffect());
            }
        }

        editLayout.setOnAddCartListener(new ProductEditLayout.AddCartListener() {
            @Override
            public RequestParams onPreAddCart(RequestParams params) {
                FlowDataAnalysisManagerKt.addAnalysisRequestParams(params, mFlowData, AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_ADDCART_LIST);
                return params;
            }

            @Override
            public void onAddCartSuccess() {
                if (rowsBean != null && mFlowData != null) {
                    FlowDataEventAnalysisKt.flowDataPageCommodityDetails(mFlowData, rowsBean.getId() + "", AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_INTOPRODUCTDETAIL_NOREAL, rowsBean.sourceType, baseViewHolder.getBindingAdapterPosition() + "");
                }
            }
        });


        //是否高毛
        if (ivGrossMargin != null) {
            if (rowsBean != null) {
                ivGrossMargin.setVisibility(rowsBean.getHighGross() == 2 ? View.VISIBLE : View.GONE);
            }
        }


        List<LabelIconBean> mCxTagList = rowsBean.getTagList();
        if (null != llShowPromotion) {
            llShowPromotion.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
//                if (mPopWindowPromotion == null) {
                    mPopWindowPromotion = new ShowPromotionPopWindow(mContext);
                    mPopWindowPromotion.setNewData(mCxTagList);
//                }
                    mPopWindowPromotion.show(llShowPromotion);
                }
            });
        }

        //活动标签
        // setActivityTag(ivActivityTag, llActivityTag, rowsBean);
        mPtv.setShowData(rowsBean.getActivityTag());

        // 店铺

        if (rowsBean.getIsThirdCompany() == 0) {
            //自营
            setViewVisibility(tv_ziying, View.VISIBLE);
            tv_ziying.setText("自营");
        } else {
            setViewVisibility(tv_ziying, View.GONE);
        }
        // 商品一般都有店铺，如果没有跳转链接则不展示
        if (rowsBean.shopUrl == null || rowsBean.shopUrl.isEmpty()) {
            setViewVisibility(ll_gong, View.GONE);
        } else {
            setViewVisibility(llCompanyName, View.VISIBLE);
            setViewVisibility(ll_gong, View.VISIBLE);
            TextView tv_open = baseViewHolder.getView(R.id.tv_open);//供应商
            setViewVisibility(tv_open, View.VISIBLE);
            tv_open.setText(rowsBean.shopName);
        }
        baseViewHolder.setOnClickListener(R.id.ll_gong, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // guanchong 为你推荐（首页底部），为你推荐（个人中心底部），为你推荐（购物车底部）
                if (rowsBean != null && rowsBean.shopUrl != null && !rowsBean.shopUrl.isEmpty()) {
                    RoutersUtils.open(rowsBean.shopUrl);
                }
            }
        });


        baseViewHolder.setText(R.id.tv_chang_name, rowsBean != null ? rowsBean.getManufacturer() : "");

        //活动价
        boolean isShowUrl = rowsBean.isReducePrice() && rowsBean.isMarkerUrl();
        baseViewHolder.setText(R.id.tv_activity_price, String.valueOf("药采节价:" + UiUtils.transform(rowsBean.getReducePrice())));
        baseViewHolder.setGone(R.id.tv_activity_price, isShowUrl);

        // 控销价，毛利
        TextView tvRetailPrice = baseViewHolder.getView(R.id.tv_retail_price);

        setGoodsName(tv_shop_name, rowsBean);
        //商品区间价格-商品列表
        tv_shop_price.setText(UiUtils.showProductPrice(rowsBean));
        // 折后价
        tv_original_price.setText(rowsBean.showPriceAfterDiscount);
        if (!TextUtils.isEmpty(rowsBean.showPriceAfterDiscount)) {
            tv_original_price.setVisibility(View.VISIBLE);
            tv_original_price.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 11);
            tv_original_price.setTextColor(UiUtils.getColor(R.color.color_676773));
        } else {
            tv_original_price.setVisibility(View.GONE);
        }


        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + rowsBean.getImageUrl()).placeholder(R.drawable.jiazaitu_min).diskCacheStrategy(DiskCacheStrategy.SOURCE).into(((ImageView)
                baseViewHolder.getView(R.id.icon)));
        if (TextUtils.isEmpty(rowsBean.getMarkerUrl())) {
            ImageHelper.with(mContext).load(R.drawable.transparent).into(ivShopMark);
        } else {
            String markerUrl = rowsBean.getMarkerUrl();
            if (!markerUrl.startsWith("http")) {
                markerUrl = AppNetConfig.LORD_TAG + markerUrl;
            }
            ImageHelper.with(mContext).load(markerUrl).placeholder(R.drawable.transparent).error(R.drawable.transparent).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform()
                    .into(ivShopMark);
        }
        TagView tagView = baseViewHolder.getView(R.id.rl_icon_type);
        ImageView imageView = baseViewHolder.getView(R.id.iv_service);

        //设置标签图片
        setIcon(tagView, imageView, rowsBean);
        //中包装数量,不可拆零才显示,4.2.0以后所有的商品都显示
        if (null != tv_shop_pack_size) {
            if (!TextUtils.isEmpty(rowsBean.getMediumPackageTitle())) {
                tv_shop_pack_size.setText(rowsBean.getMediumPackageTitle().replace("：", ":"));
            }
            tv_shop_pack_size.setVisibility(!TextUtils.isEmpty(rowsBean.getMediumPackageTitle()) ? View.VISIBLE : View.GONE);
        }
        //如果建议零售价存在或者是控销并且不可购买，控销不显示
        if (TextUtils.isEmpty(rowsBean.getSuggestPrice()) && TextUtils.isEmpty(rowsBean.getUniformPrice())) {
            tvRetailPrice.setVisibility(View.INVISIBLE);
        } else {
            tvRetailPrice.setVisibility(View.VISIBLE);
            //建议零售价
            if (!TextUtils.isEmpty(rowsBean.getSuggestPrice())) {
                tvRetailPrice.setText(String.format(mContext.getResources().getString(R.string.product_retail_price),
                        mContext.getResources().getString(R.string.product_list_lsj_title),
                        StringUtil.getUniformPrice2Double(rowsBean.getSuggestPrice()),
                        rowsBean.getGrossMargin()));
            }
            //控销价
            if (!TextUtils.isEmpty(rowsBean.getUniformPrice())) {
                tvRetailPrice.setText(String.format(mContext.getResources().getString(R.string.product_retail_price),
                        mContext.getResources().getString(R.string.product_list_kxj_title),
                        StringUtil.getUniformPrice2Double(rowsBean.getUniformPrice()),
                        rowsBean.getGrossMargin()));
            }

            /*//建议零售价
            if (isKaUser) {//处理ka用户展示建议零售价和毛利
                shop_price_kxj_title_tv.setText(R.string.product_list_lsj_title);
                setSuggestOrGrossMargin(rowsBean.getGrossMargin(), tv_shop_price_kxj_number, tv_shop_price_ml_number, StringUtil.getUniformPrice2Double(rowsBean.suggestPriceStr));
            } else {

                if (!StringUtil.isEmpty(rowsBean.getSuggestPrice())) {
                    shop_price_kxj_title_tv.setText(R.string.product_list_lsj_title);
                    setSuggestOrGrossMargin(rowsBean.getGrossMargin(), tv_shop_price_kxj_number, tv_shop_price_ml_number, StringUtil.getUniformPrice2Double(rowsBean.getSuggestPrice()));
                }
            }
            //控销价
            if (!StringUtil.isEmpty(rowsBean.getUniformPrice())) {
                shop_price_kxj_title_tv.setText(R.string.product_list_kxj_title);
                setSuggestOrGrossMargin(rowsBean.getGrossMargin(), tv_shop_price_kxj_number, tv_shop_price_ml_number, StringUtil.getUniformPrice2Double(rowsBean.getUniformPrice()));
            }*/
        }

        /**==============控销并且不可购买，控销和毛利率不显示===============
         * 商品设置了控销价，同时设置了价格区间，则商品在用户端显示控销价；
         * 若商品在促销活动中的设置了促销价格，同时该商品设置了价格区间，则商品在用户端显示促销价；
         * 如果是OEM商品，控销优先级高于OEM
         * **/
        boolean isBuy = true;
        if (editLayout != null) {
            setViewVisibility(editLayout, View.VISIBLE);
        }
        setViewVisibility(iv_remind, View.GONE);
        String sellOutStr = mContext.getResources().getString(R.string.text_sell_out);
        String soldOutStr = mContext.getResources().getString(R.string.text_sold_out);
        if (rowsBean.getStatus() == 2 || rowsBean.getStatus() == 4 || rowsBean.getAvailableQty() <= 0) {
            shop_no_limit_tv01.setVisibility(View.VISIBLE);
            setViewVisibility(tv_shop_pack_size, View.GONE);
            isHaveGoods = false;
            if (rowsBean.getStatus() == 2 || rowsBean.getAvailableQty() <= 0) {
                //售罄
                shop_no_limit_tv01.setText(sellOutStr);
            } else {
                //已下架
                shop_no_limit_tv01.setText((rowsBean.getStatus() == 4 ? soldOutStr : ""));
            }
        } else {
            //售罄等字样不显示
            isHaveGoods = true;
            setViewVisibility(shop_no_limit_tv01, View.INVISIBLE);
            setViewVisibility(tv_shop_pack_size, View.VISIBLE);
        }

        setViewVisibility(tv_shop_price, View.VISIBLE);

        //是否促销价格
        if (rowsBean.getStatus() == 3 || rowsBean.getStatus() == 5) {
            tv_shop_price.setText("¥" + UiUtils.transform(rowsBean.getFob()));
            setViewVisibility(tv_shop_price, View.VISIBLE);
        }

        //加减按钮
        if (editLayout != null) {
            editLayout.setAnimationEnable(animationEnable);
            editLayout.bindData(rowsBean.getId(), rowsBean.getStatus(), isBuy, pageFrom, (ImageView) baseViewHolder.getView(R.id.icon), true, rowsBean.getStepNum(), rowsBean.getIsSplit() == 1);
        }

        //收藏
        if (null != shop_ck && null != shop_ck_ll) {
            setViewVisibility(shop_ck_ll, mIsCollect ? View.VISIBLE : View.GONE);
            shop_ck.setChecked(rowsBean.isFavoriteStatus());
            shop_ck_ll.setTag(rowsBean);
            shop_ck_ll.setOnClickListener(new onClickListener(shop_ck));
        }

        if (isShowSalesQuantity) {
            try {
                TextView tv_sales_quantity = baseViewHolder.getView(R.id.tv_sales_quantity);
                if (tv_sales_quantity != null) {
                    setViewVisibility(tv_sales_quantity, View.VISIBLE);
                    tv_sales_quantity.setText("销量 " + UiUtils.getNumber(rowsBean.getThirtyDaysAmount()) + "件");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        /*
         * =============OEM============
         * isOEM true是OEM协议商品，为空或者false为非OEM协议商品 true/false
         * signStatus 协议签署状态(0-未签署,1-已签署)是普通商品或者(OEM协议商品且已签署协议)才会显示价格
         * */

        //是否控销  true:可购买 false:不可买
        if (rowsBean.getIsControl() == 1) {
            //控销可购买
            if (rowsBean.isPurchase()) {
                isCanBuy = true;
                boolean isShow = (!StringUtil.isEmpty(rowsBean.getUniformPrice()) || !StringUtil.isEmpty(rowsBean.getSuggestPrice()));
                tvRetailPrice.setVisibility(isShow ? View.VISIBLE : View.INVISIBLE);
                //商品区间价格
                if (rowsBean.getPriceType() == 1) {
                    tv_shop_price.setText("¥" + UiUtils.transform(rowsBean.getFob()));
                } else {
                    tv_shop_price.setText(UiUtils.showProductPrice(rowsBean));
                }

                //是否是OEM协议商品
                setIsOEM(rowsBean, tvRetailPrice);
            } else {
                //控销不可购买
                isCanBuy = false;
                tv_shop_price.setText("¥--");
                setViewVisibility(tvRetailPrice, View.GONE);
            }
        } else {
            //是否是OEM协议商品
            setIsOEM(rowsBean, tvRetailPrice);
        }

        // 到货提醒
        if (iv_remind != null) {
            if ((rowsBean.isArrivalReminder() || rowsBean.getStatus() == 2) && !isKaUser) {//增加ka用户不显示到货提醒
                //显示到货提醒
                isShowRemind = true;
                setViewVisibility(iv_remind, View.VISIBLE);
                if (rowsBean.getBusinessType() == 1) {//已经订阅了到货提醒
                    iv_remind.setEnabled(false);
                } else {
                    iv_remind.setEnabled(true);
                }
            } else {
                //不显示到货提醒
                isShowRemind = false;
                setViewVisibility(iv_remind, View.GONE);
            }
        } else {
            isShowRemind = false;
        }

        //控制显示加入购物车按钮显示
        if (isShowAddEdit && isCanBuy && isHaveGoods && !isOEMAndNotSigned && !isShowRemind) {
            setViewVisibility(editLayout, View.VISIBLE);
        } else {
            setViewVisibility(editLayout, View.GONE);
        }

        //控制显示价格等布局
        if (!isCanBuy || isOEMAndNotSigned) {
            if (!isCanBuy) {
//                setViewVisibility(tvBrandControl, View.VISIBLE);//显示暂无购买权限
//                setViewVisibility(tvOEM, View.GONE);
            } else if (isOEMAndNotSigned) {
//                setViewVisibility(tvOEM, View.VISIBLE);//显示签署协议可见
//                setViewVisibility(tvBrandControl, View.GONE);
            }
            setViewVisibility(tv_shop_price, View.GONE);
            setViewVisibility(tv_original_price, View.GONE);
            setViewVisibility(tv_shop_pack_size, View.GONE);
            setViewVisibility(tvRetailPrice, View.INVISIBLE);

        } else {
//            setViewVisibility(tvBrandControl, View.GONE);
//            setViewVisibility(tvOEM, View.GONE);
            setViewVisibility(tv_shop_price, View.VISIBLE);
            setViewVisibility(tv_shop_pack_size, View.VISIBLE);
            setViewVisibility(tv_original_price, View.VISIBLE);
        }

        //到货提醒按钮
        if (iv_remind != null) {
            iv_remind.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    setRemindCollect(v.getContext(), rowsBean);
                }
            });
        }
        handleAuditPassedVisible(baseViewHolder, isShowSalesQuantity, rowsBean);
    }


    //可以适配多手机
    public int dp2px(int dp) {
        if (dp == 0) {
            return 0;
        }
        return ConvertUtils.dp2px(dp);
    }


    /**
     * 显示商品name+规格 and head show tag
     *
     * @param tv_shop_name
     * @param rowsBean
     */
    private void setGoodsName(TextView tv_shop_name, RowsBean rowsBean) {

        List<Drawable> list = new ArrayList<>();
        String showContent = rowsBean.getShowName() + "/" + rowsBean.getSpec();//商品名称和规格
        int nameLength = TextUtils.isEmpty(rowsBean.getShowName()) ? 0 : rowsBean.getShowName().length();

        if (rowsBean.getAgent() == 1) {//显示独家
            Drawable drawable = ContextCompat.getDrawable(mContext, R.drawable.icon_dujia);
            drawable.setBounds(0, 0, ConvertUtils.dp2px(28), ConvertUtils.dp2px(15));
            list.add(drawable);
        }

        if (rowsBean.isGift()) {//显示药彩节
            list.add(ContextCompat.getDrawable(mContext, R.drawable.icon_procurement_festival));
        }

        setShowActivityTag(tv_shop_name, showContent, nameLength, list);

        try {

            if (rowsBean.getActivityTag() != null && !TextUtils.isEmpty(rowsBean.getActivityTag().tagUrl)) {
//                ImageView imageView = new ImageView(mContext);
//                imageView.setAdjustViewBounds(true);
//                imageView.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,tv_shop_name.getMeasuredHeight()));
                // 87 ：30
                Bitmap bitmap = ImageHelper.with(mContext).load(ImageUtilKt.getFullPicUrl(rowsBean.getActivityTag().tagUrl)).asBitmap().into(87, 30).get();
                list.add(ImageUtil.getDrawableForHeight(mContext, bitmap, tv_shop_name.getMeasuredHeight()));
                setShowActivityTag(tv_shop_name, showContent, nameLength, list);

            } else {
                setShowActivityTag(tv_shop_name, showContent, nameLength, list);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

    private void setShowActivityTag(TextView textView, String showName, int nameLength, List<Drawable> list) {

        SpannableStringBuilder shopName = getShopNameIcon(textView, showName, nameLength, list);
        textView.setText(shopName);

    }

    private SpannableStringBuilder getShopNameIcon(TextView tvName, String shopName, int nameLength, List<Drawable> icons) {

        SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
        spannableString.setSpan(new AbsoluteSizeSpan(15, true), 0, nameLength + 1, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new AbsoluteSizeSpan(12, true), nameLength + 1, shopName.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        if (icons != null && icons.size() > 0) {

            for (int i = 0; i < icons.size(); i++) {
                Drawable drawable = icons.get(i);
                //适配图标大小问题

                if (drawable != null) {

                    float tvHeight = tvName.getTextSize();
                    float drawableWith = (drawable.getIntrinsicWidth() / (float) drawable.getIntrinsicHeight()) * tvHeight;
                    drawable.setBounds(0, 0, (int) (drawableWith + 0.5), (int) (tvHeight + 0.5));

                    MyImageSpan imageSpan = new MyImageSpan(drawable, 2);
                    //占个位置
                    spannableString.insert(0, "-");
                    spannableString.setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
        }
        return spannableString;
    }

    /**
     * 由于该项目好多页面都公用这个Adpater，有些界面找不到id，会报错，因此捕获异常
     *
     * @param baseViewHolder
     * @return
     */
    private View getSafeView(YBMBaseHolder baseViewHolder, int resId) {
        try {
            return baseViewHolder.getView(resId);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.e(e);
        }
        return null;
    }

    /*
     * =============OEM============
     * isOEM true是OEM协议商品，为空或者false为非OEM协议商品 true/false
     * signStatus 协议签署状态(0-未签署,1-已签署)是普通商品或者(OEM协议商品且已签署协议)才会显示价格
     * */
    private void setIsOEM(RowsBean rowsBean, TextView tvRetailPrice) {
        if (rowsBean.getIsOEM()) {//是OEM协议商品
            //是否签署协议
            if (rowsBean.getSignStatus() == 1) {//已经签署
                isOEMAndNotSigned = false;
                boolean isShow = (!StringUtil.isEmpty(rowsBean.getUniformPrice()) || !StringUtil.isEmpty(rowsBean.getSuggestPrice()));
                setViewVisibility(tvRetailPrice, isShow ? View.VISIBLE : View.INVISIBLE);
            } else {//未签署
                isOEMAndNotSigned = true;
                setViewVisibility(tvRetailPrice, View.INVISIBLE);
            }
        } else {
            isOEMAndNotSigned = false;
            //是否符合协议标准展示价格,1:符合0:不符合
            if (rowsBean.showAgree == 0) {
                isOEMAndNotSigned = true;
                setViewVisibility(tvRetailPrice, View.INVISIBLE);
            }
        }
    }

    /**
     * 设置图标
     *
     * @param rowsBean
     */
    private void setIcon(TagView tagView, ImageView imageView, RowsBean rowsBean) {
        //在这里对标签数量进行处理
        if (rowsBean.getTagList() != null && rowsBean.getTagList().size() > 0) {
            tagView.bindData(rowsBean.getTagList(), 3, true);
            if (null != imageView) {
                imageView.setVisibility(View.VISIBLE);
            }
        } else {
            tagView.setVisibility(View.GONE);
            if (null != imageView) {
                imageView.setVisibility(View.GONE);
            }
        }

    }

    private void setViewVisibility(View view, int visibility) {
        if (view == null) {
            // LogUtils.e("view == null");
            return;
        }
        if (view.getVisibility() != visibility) {
            view.setVisibility(visibility);
        }
    }

    public void setShowAddEdit(boolean showAddEdit) {
        isShowAddEdit = showAddEdit;
    }

    /**
     * 设置控销价或者零售价
     *
     * @param tv_shop_price_kxj_number
     * @param tv_shop_price_ml_number
     * @param uniformPrice2Double
     */
    private void setSuggestOrGrossMargin(String grossMarginStr, TextView
            tv_shop_price_kxj_number, TextView tv_shop_price_ml_number, String uniformPrice2Double) {
        try {
            //建议零售价或者控销价
            tv_shop_price_kxj_number.setText(uniformPrice2Double);
            //毛利率
            if (!TextUtils.isEmpty(uniformPrice2Double) && uniformPrice2Double.length() > 6 && mContext instanceof MainActivity) {
                tv_shop_price_ml_number.setText("...");//只显示两个..
            } else {//正常显示价格
                tv_shop_price_ml_number.setText(grossMarginStr);
            }
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
    }

    private String categoryId;

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    private String traceId;

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    /*
     * 收藏
     * */
    private class onClickListener implements View.OnClickListener {

        private CheckBox cb;

        private onClickListener(CheckBox cb) {
            this.cb = cb;
        }

        @Override
        public void onClick(View v) {

            RowsBean rowsBean = ((RowsBean) v.getTag());
            switch (v.getId()) {
                case R.id.shop_ck_ll:
                    checkCollect(rowsBean, cb);
                    break;
            }
        }
    }

    /*
     *收藏-取消收藏
     * */
    private void checkCollect(final RowsBean rowsBean, final CheckBox checkBox) {

        final long id = rowsBean.getId();
        final String collect_net = checkBox.isChecked() ? AppNetConfig.CANCEL_COLLECT : AppNetConfig.COLLECT;
        final String collect_str = checkBox.isChecked() ? "取消收藏" : "收藏成功";

        RequestParams params = new RequestParams();
        String merchantId = SpUtil.getMerchantid();
        params.put("merchantId", merchantId);
        params.put("skuId", String.valueOf(id));

        HttpManager.getInstance().post(collect_net, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {
                if (null != obj) {
                    if (obj.isSuccess()) {
                        if (checkBox.isChecked()) {
                            checkBox.setChecked(false);
                            DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, collect_str);
                        } else {
                            checkBox.setChecked(true);
                            DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, collect_str);
                        }

                    }
                }

            }
        });
    }

    protected OnListViewItemClickListener mOnItemClickListener = null;

    public interface OnListViewItemClickListener {
        void onItemClick(RowsBean rows);
    }

    public void setOnListItemClickListener(OnListViewItemClickListener listener) {
        this.mOnItemClickListener = listener;
    }


    public int getCurrPosition() {
        return currPosition;
    }

    /**
     * 订阅提醒
     */
    private void setRemindCollect(Context context, RowsBean rowsBean) {
        int businessType = 1;
        requestData(context, rowsBean, businessType, rowsBean.getId());
    }

    /**
     * 设置是否开启飞入动画
     *
     * @param enable
     */
    public void setAnimationEnable(boolean enable) {
        animationEnable = enable;
    }

    /**
     * businessType 收藏类型 默认收藏不传，1: 表示有货提醒业务类型；2：降价提醒
     * merchantId   商品业务相关id
     * skuId        商户id
     * 服务端请求
     */
    private void requestData(Context context, RowsBean rowsBean, final int businessType,
                             final long skuId) {
        ((BaseActivity) context).showProgress();
        RequestParams params = new RequestParams();
        String merchantId = SpUtil.getMerchantid();
        params.put("merchantId", merchantId);
        params.put("skuId", String.valueOf(skuId));
        params.put("businessType", String.valueOf(businessType));

        HttpManager.getInstance().post(AppNetConfig.COLLECT, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {
                ((BaseActivity) context).dismissProgress();
                if (null != obj) {
                    if (obj.isSuccess()) {
                        showRemindDialog(context);
                        rowsBean.setBusinessType(1);
                        notifyDataSetChanged();
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                ((BaseActivity) context).dismissProgress();
            }
        });
    }

    private void showRemindDialog(Context context) {

        String str = "若该商品在45天内到货，药帮忙会提醒您！ 同时您可以在我的收藏夹查看您订阅过的所有商品";
        AlertDialogEx dialogEx = new AlertDialogEx(context);
        dialogEx.setMessage(str).setCancelButton("我知道啦", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
            }
        }).setCancelable(false).setCanceledOnTouchOutside(false).setTitle("订阅成功").show();
    }

    public interface OnDeleteItemClickCallback {
        void onDeleteItemClick(int position);
    }


    public void clearTranceData() {
        traceProductData.clear();
    }


    /**
     * 处理价格认证资质可见
     */
    private void handleAuditPassedVisible(YBMBaseHolder baseViewHolder, boolean isShowSalesQuantity, RowsBean rowsBean) {
//        TextView tvAuditPassedVisible = baseViewHolder.getView(R.id.tv_audit_passed_visible);
//        //审核通过
//        if (AuditStatusSyncUtil.getInstance().isAuditFirstPassed()) {
//            tvAuditPassedVisible.setVisibility(View.GONE);
//            if (isShowSalesQuantity) {
//                try {
//                    TextView tv_sales_quantity = baseViewHolder.getView(R.id.tv_sales_quantity);
//                    if (tv_sales_quantity != null) {
//                        setViewVisibility(tv_sales_quantity, View.VISIBLE);
//                        tv_sales_quantity.setText("销量 " + UiUtils.getNumber(rowsBean.getThirtyDaysAmount()) + "件");
//                    }
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//        } else {
//            //价格不可见
//            tvAuditPassedVisible.setVisibility(View.VISIBLE);
//            tvAuditPassedVisible.setText(mContext.getResources().getString(R.string.audit_passed_visible));
//            //隐藏价格相关的view
//            baseViewHolder.setGone(R.id.tv_brand_control, false);
//            baseViewHolder.setGone(R.id.tv_oem, false);
//            baseViewHolder.setGone(R.id.shop_price, false);
//            baseViewHolder.setGone(R.id.tv_original_price, false);
//            baseViewHolder.setGone(R.id.el_edit, false);
//            try {
//                TextView tv_sales_quantity = baseViewHolder.getView(R.id.tv_sales_quantity);
//                if (tv_sales_quantity != null) {
//                    setViewVisibility(tv_sales_quantity, View.GONE);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
        TextView tv = baseViewHolder.getView(R.id.tv_audit_passed_visible);
        if (TextUtils.isEmpty(rowsBean.controlTitle)) {
            tv.setVisibility(View.GONE);
            //隐藏价格相关的view
            baseViewHolder.setGone(R.id.shop_price, true);
            baseViewHolder.setGone(R.id.tv_original_price, true);
            baseViewHolder.setGone(R.id.el_edit, true);
            try {
                TextView tv_sales_quantity = baseViewHolder.getView(R.id.tv_sales_quantity);
                if (tv_sales_quantity != null) {
                    setViewVisibility(tv_sales_quantity, View.GONE);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            baseViewHolder.setGone(R.id.shop_price, false);
            baseViewHolder.setGone(R.id.tv_original_price, false);
            baseViewHolder.setGone(R.id.el_edit, false);
            tv.setVisibility(View.VISIBLE);
            tv.setText(rowsBean.controlTitle);
        }
    }

    public void setShowOftenBuyTypeName(boolean isShowOftenBuyTypeName) {
        this.isShowOftenBuyTypeName = isShowOftenBuyTypeName;
    }

}
