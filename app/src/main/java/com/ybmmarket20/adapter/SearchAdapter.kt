package com.ybmmarket20.adapter

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.ProductDetailActivity
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarket20.bean.OPERATION_POSITION_TYPE_MULTI_SHOP
import com.ybmmarket20.bean.OPERATION_POSITION_TYPE_SINGLE_SHOP
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarket20.bean.SEARCH_LIST_CARD_TYPE_GOODS
import com.ybmmarket20.bean.SEARCH_LIST_CARD_TYPE_OPERATION_POSITION
import com.ybmmarket20.bean.SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS
import com.ybmmarket20.bean.SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_MULTI
import com.ybmmarket20.bean.SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_SINGLE
import com.ybmmarket20.bean.SEARCH_SPELL_GROUP_LIMIT_TIME_PREMIUM_GOODS
import com.ybmmarket20.bean.SearchRowsBean
import com.ybmmarket20.bean.product_detail.ReportPDButtonClick
import com.ybmmarket20.bean.product_detail.ReportPDExtendOuterBean
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JGTrackManager.Common
import com.ybmmarket20.common.JGTrackManager.GlobalVariable
import com.ybmmarket20.common.JgOperationPositionInfo
import com.ybmmarket20.common.JgSearchSomeField
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.jgReport
import com.ybmmarket20.common.searchResourceClickJGTrack
import com.ybmmarket20.common.splicingModule2Entrance
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.reportBean.JGPageListCommonBean
import com.ybmmarket20.reportBean.PageListProductClick
import com.ybmmarket20.reportBean.PageListProductExposure
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.flowDataPageOPListExposureWithCode
import com.ybmmarket20.utils.analysis.getOpenUrlNotJump
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.view.operationposition.OPCardAdapter
import com.ybmmarket20.xyyreport.page.search.GoodsPlaceExposureRecord
import com.ybmmarket20.xyyreport.page.search.SearchProductReport
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import com.ybmmarketkotlin.adapter.GoodsCombinedBuyMultiAdapter
import com.ybmmarketkotlin.adapter.GoodsCombinedBuySingleAdapter
import com.ybmmarketkotlin.adapter.GoodsListAnalysisAdapter
import com.ybmmarketkotlin.adapter.SpellGroupLimitTimePremiumAdapter
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuyListener
import com.ydmmarket.report.ReportManager

/**
 * 大搜Adapter(商品+运营位)
 */
class SearchAdapter(
        val context: Context,
        data: MutableList<SearchRowsBean>,
) : GoodsListAnalysisAdapter<SearchRowsBean>(data) {
    var loadMoreFlag = false
    constructor(iCouponEntryType: ICouponEntryType, context: Context, data: MutableList<SearchRowsBean>): this(context, data) {
        goodsAdapter.mCouponEntryType = iCouponEntryType
        mLimitTimePremiumAdapter.mCouponEntryType = iCouponEntryType
    }

    var jgTrackBean:JgTrackBean? = null
        set(value) {
            field = value
            goodsAdapter.jgTrackBean = value?.copy()?.apply {
                //这里路径细化到bindItemView去处理
//                entrance = splicingModule2Entrance(entrance?:"",Common.MODULE_SEARCH_FEED)
                module = Common.MODULE_SEARCH_FEED
            }
            mLimitTimePremiumAdapter.jgTrackBean = value?.copy()?.apply {
                //这里路径细化到bindItemView去处理
//                entrance = splicingModule2Entrance(entrance?:"",Common.MODULE_SEARCH_FEED)
                module = Common.MODULE_SEARCH_FEED
            }

            opAdapter.jgTrackBean = value?.copy()?.apply {
                entrance = splicingModule2Entrance(entrance ?: "", Common.MODULE_OPERATIONS)
                module = Common.MODULE_OPERATIONS
            }
        }

    var jGPageListCommonBean: JGPageListCommonBean? = null
        set(value) {
            field = value
            goodsAdapter.jGPageListCommonBean = value
            mLimitTimePremiumAdapter.jGPageListCommonBean = value
            opAdapter.jGPageListCommonBean = value
        }

    private val goodsAdapter: GoodListAdapterNew = GoodListAdapterNew(R.layout.item_goods_new, mutableListOf(), true).apply {
        isFromSearch = true
    }
    private val mLimitTimePremiumAdapter: SpellGroupLimitTimePremiumAdapter = SpellGroupLimitTimePremiumAdapter(mutableListOf()).apply {
        isFromSearch = true
    }
    private val mGoodsCombinationSingleAdapter = GoodsCombinedBuySingleAdapter(mutableListOf()).apply {
//        isFromSearch = true
    }
    private val mGoodsCombinationMultiAdapter = GoodsCombinedBuyMultiAdapter(mutableListOf()).apply {
//        isFromSearch = true
    }

    fun getListGoodsAdapter(): GoodListAdapterNew = goodsAdapter

    private val opAdapter = OPCardAdapter(mutableListOf()).apply {
        mTrackViewListener = { searchBean, productPosition ->
            //每个只报一次
            productViewTrackMap[searchBean.productInfo?.id.toString()] ?: kotlin.run {
                productViewTrackMap[searchBean.productInfo?.id.toString()] = System.currentTimeMillis()

                searchPageListProductExposure(PageListProductExposure(
                        jGPageListCommonBean = jGPageListCommonBean,
                        search_sort_strategy_id = searchBean.productInfo?.searchSortStrategyCode?:"",
                        operation_id = searchBean.productInfo?.operationId ?: "",
                        operation_rank = productPosition+1,
                        list_position_type = (searchBean.productInfo?.positionType ?: 0).toString(),
                        list_position_typename = searchBean.productInfo?.positionTypeName ?: "",
                        product_id = searchBean.productInfo?.id,
                        product_name = searchBean.productInfo?.productName?:"",
                        rank = searchBean.outPosition+1,
                        product_first = searchBean.productInfo?.categoryFirstId,
                        product_price = searchBean.productInfo?.jgProductPrice,
                        product_type = searchBean.productInfo?.productType?.toString()?:"",
                        product_activity_type = searchBean.productInfo?.productActivityType,
                        product_shop_code = searchBean.productInfo?.shopCode,
                        product_shop_name = searchBean.productInfo?.shopName
                ))
            }
        }

        mTrackClickListener = { searchBean, productPosition,isBtnClick,mContent,number ->
            mContext.searchResourceClickJGTrack(
                    searchBean,
                    productPosition,
                    Common.MODULE_OPERATIONS,
                    JGTrackManager.Common.MODULE_OPERATIONS,
                    jgTrackBean?.entrance ?: "",
                    operationId = searchBean.productInfo?.operationId ?: "",
                    operationRank = searchBean.outPosition+1,
                    listPositionType = searchBean.productInfo?.positionType?:0,
                    listPositionTypeName = searchBean.productInfo?.positionTypeName?:"")

            searchPageListProductClick(PageListProductClick(
                    jGPageListCommonBean = jGPageListCommonBean,
                    search_sort_strategy_id = searchBean.productInfo?.searchSortStrategyCode?:"",
                    operation_id = searchBean.productInfo?.operationId ?: "",
                    operation_rank = productPosition+1,
                    rank = searchBean.outPosition+1,
                    list_position_type = (searchBean.productInfo?.positionType ?: 0).toString(),
                    list_position_typename = searchBean.productInfo?.positionTypeName ?: "",
                    product_id = searchBean.productInfo?.id,
                    product_name = searchBean.productInfo?.productName?:"",
                    product_first = searchBean.productInfo?.categoryFirstId,
                    product_price = searchBean.productInfo?.jgProductPrice,
                    product_type = searchBean.productInfo?.productType?.toString()?:"",
                    product_activity_type = searchBean.productInfo?.productActivityType,
                    product_shop_code = searchBean.productInfo?.shopCode,
                    product_shop_name = searchBean.productInfo?.shopName
            ))

            if (isBtnClick){
                try {
                    context.jgReport(
                            ReportPDButtonClick().apply {
                                url = jgTrackBean?.url ?: ""
                                title = jgTrackBean?.title ?: ""
                                referrer = jgTrackBean?.jgReferrer?:""
                                accountId = SpUtil.getAccountId()
                                merchantId = SpUtil.getMerchantid()
                                productId = searchBean.productInfo?.id?.toInt()
                                productName = searchBean.productInfo?.productName?:""
                                productFirst = searchBean.productInfo?.categoryFirstId
                                productPrice = searchBean.productInfo?.jgProductPrice
                                productType = searchBean.productInfo?.productType.toString()
                                productActivityType = searchBean.productInfo?.productActivityType
                                productNumber = number
                                productShopCode = searchBean.productInfo?.shopCode
                                productShopName = searchBean.productInfo?.shopName
                                btnName = mContent
                                btnDesc = "列表页"
                                direct = "1"
                                outerBean = ReportPDExtendOuterBean().apply {
                                    sptype = jGPageListCommonBean?.sptype
                                    jgspid = jGPageListCommonBean?.jgspid
                                    sid = jGPageListCommonBean?.sid
                                    resultCnt = jGPageListCommonBean?.result_cnt
                                    pageNo =jGPageListCommonBean?.page_no
                                    pageSize = jGPageListCommonBean?.page_size
                                    totalPage = jGPageListCommonBean?.total_page
                                    rank = searchBean.outPosition+1
                                    keyWord = searchBean.productInfo?.searchKeyword?:""
                                    listPositionType = searchBean.productInfo?.positionType.toString()
                                    listPositionTypename = searchBean.productInfo?.positionTypeName ?: ""
                                    searchSortStrategyId = searchBean.productInfo?.searchSortStrategyCode?:""
                                    operationId = searchBean.productInfo?.operationId ?: ""
                                    operationRank = productPosition+1
                                }
                            }

                    )
                }catch (e:Exception){
                    e.printStackTrace()
                }
            }

            if (searchBean.isOperation()) {
                GlobalVariable.mJgOperationInfo = JgOperationPositionInfo(
                        searchBean.productInfo?.productId,
                        searchBean.productInfo?.operationId,
                        searchBean.outPosition + 1,
                        productPosition + 1, )
            }

            GlobalVariable.apply{
                mJgSearchRowsBean = searchBean.productInfo
                mJgSearchSomeField = JgSearchSomeField(mJgPageListCommonBean = jGPageListCommonBean,searchBean.outPosition+1)
            }
        }
    }

    // key: 商品Id     value:当时埋点的时间戳
    private val productViewTrackMap = hashMapOf<String, Long>()

    companion object {
//        private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
//        private const val TRACK_DURATION = 0 //无时间限时
    }

    override var flowData: BaseFlowData? = null
        set(value) {
            field = value
            goodsAdapter.flowData = value
            mLimitTimePremiumAdapter.flowData = value
            opAdapter.flowData = value
        }

    init {
        //商品
        addItemType(SEARCH_LIST_CARD_TYPE_GOODS, goodsAdapter.layoutResId)
        //运营位
        addItemType(SEARCH_LIST_CARD_TYPE_OPERATION_POSITION, opAdapter.layoutResId)
        //单品使用商品样式
        addItemType(SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS, goodsAdapter.layoutResId)
        //限时加补拼团品样式
//        addItemType(SEARCH_SPELL_GROUP_LIMIT_TIME_PREMIUM_GOODS,R.layout.item_goods_new_limit_time_premium)
        // 组合购
        addItemType(SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_SINGLE,mGoodsCombinationSingleAdapter.layoutResId)
        // 加价购
        addItemType(SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_MULTI,mGoodsCombinationMultiAdapter.layoutResId)
        goodsAdapter.setContext(context)
        mLimitTimePremiumAdapter.setContext(context)
        opAdapter.setContext(context)
        mGoodsCombinationSingleAdapter.setContext(context)
        mGoodsCombinationMultiAdapter.setContext(context)
    }

    override fun setBaseFlowData(flowData: BaseFlowData?) {

    }


    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SearchRowsBean) {
        super.bindItemView(baseViewHolder, t)
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            when(bean.itemType) {
                SEARCH_LIST_CARD_TYPE_GOODS -> bindGoodsItemView(holder, bean)
                SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS -> bindOPItemGoodsView(holder, bean)
                SEARCH_LIST_CARD_TYPE_OPERATION_POSITION -> bindOPItemView(holder, bean)
//                SEARCH_SPELL_GROUP_LIMIT_TIME_PREMIUM_GOODS -> bindLimitTimePremiumGoodsItemView(holder, bean)
                SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_SINGLE -> bindCombinationSingleItemView(holder, bean)
                SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_MULTI -> bindCombinationMultiItemView(holder, bean)
            }
        }
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SearchRowsBean, payloads: List<Any?>) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            when(bean.itemType) {
                SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_SINGLE -> bindCombinationSingleItemView(holder, bean,payloads)
                SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_MULTI -> bindCombinationMultiItemView(holder, bean,payloads)
            }
        }
    }

    override fun setNewData(data: MutableList<Any?>?) {
        productViewTrackMap.clear()
        super.setNewData(data)
    }

    /**
     * 限时补价拼团品
     * @param holder YBMBaseHolder
     * @param bean SearchRowsBean
     */
    private fun bindLimitTimePremiumGoodsItemView(holder: YBMBaseHolder, bean: SearchRowsBean) {
        bean.productInfo?.let {

            //这个回调要在后面的bind传递 所以要先赋值带过去
            mLimitTimePremiumAdapter.productClickTrackListener = { rowsBean,position,isBtnClick,mContent,number ->
                val module = if (rowsBean.isOPSingleGoods) JGTrackManager.Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                val resourceName = if (rowsBean.isOPSingleGoods) Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                mContext.searchResourceClickJGTrack(
                        SearchRowsBean(
                                if (rowsBean.isOPSingleGoods) 3 else 1,
                                rowsBean,
                                null,
                                keyword = rowsBean.searchKeyword?:""),
                        position,
                        resourceName,
                        module,
                        splicingModule2Entrance(
                                jgTrackBean?.entrance ?: "",
                                module),
                        operationId = rowsBean.operationId ?: "",
                        operationRank = bean.outPosition+1,
                        listPositionType = rowsBean.positionType,
                        listPositionTypeName = rowsBean.positionTypeName?:"")

                searchPageListProductClick(PageListProductClick(
                        jGPageListCommonBean = jGPageListCommonBean,
                        search_sort_strategy_id = rowsBean.searchSortStrategyCode?:"",
                        operation_id = rowsBean.operationId ?: "",
                        operation_rank = if (rowsBean.isOPSingleGoods) 1 else null,
                        rank = position+1,
                        list_position_type = rowsBean.positionType.toString(),
                        list_position_typename = rowsBean.positionTypeName ?: "",
                        product_id = rowsBean.id,
                        product_name = rowsBean.productName?:"",
                        product_first = rowsBean.categoryFirstId,
                        product_price = rowsBean.jgProductPrice,
                        product_type = rowsBean.productType.toString(),
                        product_activity_type = rowsBean.productActivityType,
                        product_shop_code = rowsBean.shopCode,
                        product_shop_name = rowsBean.shopName
                ))

                if (isBtnClick){
                    try {
                        context.jgReport(
                                ReportPDButtonClick().apply {
                                    url = jgTrackBean?.url ?: ""
                                    title = jgTrackBean?.title ?: ""
                                    referrer = jgTrackBean?.jgReferrer?:""
                                    accountId = SpUtil.getAccountId()
                                    merchantId = SpUtil.getMerchantid()
                                    productId = rowsBean.id.toInt()
                                    productName = rowsBean.productName?:""
                                    productFirst = rowsBean.categoryFirstId
                                    productPrice = rowsBean.jgProductPrice
                                    productType = rowsBean.productType.toString()
                                    productActivityType = rowsBean.productActivityType
                                    productNumber = number
                                    productShopCode = rowsBean.shopCode
                                    productShopName = rowsBean.shopName
                                    btnName = mContent
                                    btnDesc = "列表页"
                                    direct = "1"
                                    outerBean = ReportPDExtendOuterBean().apply {
                                        sptype = jGPageListCommonBean?.sptype
                                        jgspid = jGPageListCommonBean?.jgspid
                                        sid = jGPageListCommonBean?.sid
                                        resultCnt = jGPageListCommonBean?.result_cnt
                                        pageNo =jGPageListCommonBean?.page_no
                                        pageSize = jGPageListCommonBean?.page_size
                                        totalPage = jGPageListCommonBean?.total_page
                                        rank = position+1
                                        keyWord = rowsBean.searchKeyword?:""
                                        listPositionType = rowsBean.positionType.toString()
                                        listPositionTypename = rowsBean.positionTypeName ?: ""
                                        searchSortStrategyId = rowsBean.searchSortStrategyCode?:""
                                        operationId = rowsBean.operationId ?: ""
                                        operationRank = if (rowsBean.isOPSingleGoods) 1 else null
                                    }
                                }

                        )
                    }catch (e:Exception){
                        e.printStackTrace()
                    }
                }

                if (rowsBean.isOPSingleGoods) {
                    GlobalVariable.mJgOperationInfo = JgOperationPositionInfo(
                            rowsBean.productId,
                            rowsBean.operationId,
                            position + 1, 1)
                }
                GlobalVariable.apply {
                    mJgSearchRowsBean = rowsBean
                    mJgSearchSomeField = JgSearchSomeField(mJgPageListCommonBean = jGPageListCommonBean,position + 1)
                }
            }
            mLimitTimePremiumAdapter.bindItemView(holder,it)
            productViewTrackMap[it.id.toString()]?: kotlin.run {
                val module = if (it.isOPSingleGoods) JGTrackManager.Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                val resourceName = if (it.isOPSingleGoods) Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                productViewTrackMap[it.id.toString()] = System.currentTimeMillis()

                searchPageListProductExposure(PageListProductExposure(
                        jGPageListCommonBean = jGPageListCommonBean,
                        search_sort_strategy_id = bean.productInfo?.searchSortStrategyCode?:"",
                        operation_id = bean.productInfo?.operationId ?: "",
                        operation_rank = if (it.isOPSingleGoods) 1 else null,
                        rank = holder.adapterPosition +1,
                        list_position_type = (bean.productInfo?.positionType ?: 0).toString(),
                        list_position_typename = bean.productInfo?.positionTypeName ?: "",
                        product_id = bean.productInfo?.id,
                        product_name = bean.productInfo?.productName?:"",
                        product_first = bean.productInfo?.categoryFirstId,
                        product_price = bean.productInfo?.jgProductPrice,
                        product_type = bean.productInfo?.productType?.toString()?:"",
                        product_activity_type = bean.productInfo?.productActivityType,
                        product_shop_code = bean.productInfo?.shopCode,
                        product_shop_name = bean.productInfo?.shopName
                ))
            }
        }
    }

    /**
     * 商品
     */
    private fun bindGoodsItemView(holder: YBMBaseHolder, bean: SearchRowsBean) {
        bean.productInfo?.let {

            //这个回调要在后面的bind传递 所以要先赋值带过去
            goodsAdapter.productClickTrackListener = { rowsBean,position,isBtnClick,mContent,number ->
                val module = if (rowsBean.isOPSingleGoods) JGTrackManager.Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                val resourceName = if (rowsBean.isOPSingleGoods) Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                mContext.searchResourceClickJGTrack(
                        SearchRowsBean(
                                if (rowsBean.isOPSingleGoods) 3 else 1,
                                rowsBean,
                                null,
                                keyword = rowsBean.searchKeyword?:""),
                        position,
                        resourceName,
                        module,
                        splicingModule2Entrance(
                                jgTrackBean?.entrance ?: "",
                                module),
                        operationId = rowsBean.operationId ?: "",
                        operationRank = bean.outPosition+1,
                        listPositionType = rowsBean.positionType,
                        listPositionTypeName = rowsBean.positionTypeName?:"")

                searchPageListProductClick(PageListProductClick(
                        jGPageListCommonBean = jGPageListCommonBean,
                        search_sort_strategy_id = rowsBean.searchSortStrategyCode?:"",
                        operation_id = rowsBean.operationId ?: "",
                        operation_rank = if (rowsBean.isOPSingleGoods) 1 else null,
                        rank = position+1,
                        list_position_type = rowsBean.positionType.toString(),
                        list_position_typename = rowsBean.positionTypeName ?: "",
                        product_id = rowsBean.id,
                        product_name = rowsBean.productName?:"",
                        product_first = rowsBean.categoryFirstId,
                        product_price = rowsBean.jgProductPrice,
                        product_type = rowsBean.productType.toString(),
                        product_activity_type = rowsBean.productActivityType,
                        product_shop_code = rowsBean.shopCode,
                        product_shop_name = rowsBean.shopName
                ))

                if (isBtnClick){
                    try {
                        context.jgReport(
                                ReportPDButtonClick().apply {
                                    url = jgTrackBean?.url ?: ""
                                    title = jgTrackBean?.title ?: ""
                                    referrer = jgTrackBean?.jgReferrer?:""
                                    accountId = SpUtil.getAccountId()
                                    merchantId = SpUtil.getMerchantid()
                                    productId = rowsBean.id.toInt()
                                    productName = rowsBean.productName?:""
                                    productFirst = rowsBean.categoryFirstId
                                    productPrice = rowsBean.jgProductPrice
                                    productType = rowsBean.productType.toString()
                                    productActivityType = rowsBean.productActivityType
                                    productNumber = number
                                    productShopCode = rowsBean.shopCode
                                    productShopName = rowsBean.shopName
                                    btnName = mContent
                                    btnDesc = "列表页"
                                    direct = "1"
                                    outerBean = ReportPDExtendOuterBean().apply {
                                        sptype = jGPageListCommonBean?.sptype
                                        jgspid = jGPageListCommonBean?.jgspid
                                        sid = jGPageListCommonBean?.sid
                                        resultCnt = jGPageListCommonBean?.result_cnt
                                        pageNo =jGPageListCommonBean?.page_no
                                        pageSize = jGPageListCommonBean?.page_size
                                        totalPage = jGPageListCommonBean?.total_page
                                        rank = position+1
                                        keyWord = rowsBean.searchKeyword?:""
                                        listPositionType = rowsBean.positionType.toString()
                                        listPositionTypename = rowsBean.positionTypeName ?: ""
                                        searchSortStrategyId = rowsBean.searchSortStrategyCode?:""
                                        operationId = rowsBean.operationId ?: ""
                                        operationRank = if (rowsBean.isOPSingleGoods) 1 else null
                                    }
                                }

                        )
                    }catch (e:Exception){
                        e.printStackTrace()
                    }
                }

                if (rowsBean.isOPSingleGoods) {
                    GlobalVariable.mJgOperationInfo = JgOperationPositionInfo(
                            rowsBean.productId,
                            rowsBean.operationId,
                            position + 1, 1)
                }
                GlobalVariable.apply {
                    mJgSearchRowsBean = rowsBean
                    mJgSearchSomeField = JgSearchSomeField(mJgPageListCommonBean = jGPageListCommonBean,position + 1)
                }
            }
            goodsAdapter.bindItemViewWithBackground(holder, it, getGoodsViewItemBackgroundResId(holder))
            productViewTrackMap[it.id.toString()]?: kotlin.run {
                val module = if (it.isOPSingleGoods) JGTrackManager.Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                val resourceName = if (it.isOPSingleGoods) Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                productViewTrackMap[it.id.toString()] = System.currentTimeMillis()

                searchPageListProductExposure(PageListProductExposure(
                        jGPageListCommonBean = jGPageListCommonBean,
                        search_sort_strategy_id = bean.productInfo?.searchSortStrategyCode?:"",
                        operation_id = bean.productInfo?.operationId ?: "",
                        operation_rank = if (it.isOPSingleGoods) 1 else null,
                        rank = holder.adapterPosition +1,
                        list_position_type = (bean.productInfo?.positionType ?: 0).toString(),
                        list_position_typename = bean.productInfo?.positionTypeName ?: "",
                        product_id = bean.productInfo?.id,
                        product_name = bean.productInfo?.productName?:"",
                        product_first = bean.productInfo?.categoryFirstId,
                        product_price = bean.productInfo?.jgProductPrice,
                        product_type = bean.productInfo?.productType?.toString()?:"",
                        product_activity_type = bean.productInfo?.productActivityType,
                        product_shop_code = bean.productInfo?.shopCode,
                        product_shop_name = bean.productInfo?.shopName
                ))
            }
        }
    }

    /**
     * 运营位
     */
    private fun bindOPItemView(
            holder: YBMBaseHolder,
            bean: SearchRowsBean
    ) {
        opAdapter.bindItemViewWithBackground(
                holder,
                bean)
    }

    /**
     * 运营位展示商品样式
     */
    private fun bindOPItemGoodsView(holder: YBMBaseHolder, bean: SearchRowsBean) {
        try {
            bean.operationInfo?.let {
                val rowsBean = it.products[0]
                rowsBean.acptUrl = it.jumpUrl
                rowsBean.isOPSingleGoods = true
                rowsBean.directModule = "2"
                rowsBean.oPSingleGoodsActName = it.title
                rowsBean.opSingleGoodsActJumpUrl = it.jumpUrl
                rowsBean.shopName = it.products[0].shopName
                rowsBean.shopUrl = it.products[0].shopUrl
                bean.productInfo = rowsBean
                bindGoodsItemView(holder, bean)
                traceGoodsExposure(rowsBean,holder)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 组合购
     */
    private fun bindCombinationSingleItemView(
        holder: YBMBaseHolder,
        bean: SearchRowsBean
    ) {
        mGoodsCombinationSingleAdapter.loadMoreFlag = loadMoreFlag
        val purchaseInfo = bean.groupPurchaseInfo!!
        mGoodsCombinationSingleAdapter.mListener =  object : CombinedBuyListener {
            override fun refresh(preItem:RowsBeanCombinedExt,preSubPosition: Int,item:RowsBeanCombinedExt,subPosition: Int) {
                // 刷新 需要将新的副品设置为选中
                purchaseInfo.subProducts[subPosition].selectStatus = 1
                GoodsPlaceExposureRecord.get(mContext).clearRecordByKey(preItem.getGroupGoodsPlaceInfo()?.getRecordKey(mContext))
                AdapterUtils.checkCombinedProductNum(purchaseInfo,item,subPosition,this@SearchAdapter,holder.layoutPosition)
            }

            override fun jumpToGoodsDetail(bean: RowsBeanCombinedExt) {
                jumpToProductDetail(purchaseInfo,bean, holder)
                SearchProductReport.trackSearchGoodsClick(mContext, holder.bindingAdapterPosition, bean)
            }

            override fun preSettle(subPosition: Int) {
                AdapterUtils.preSettle(mContext, purchaseInfo,subPosition)
                SearchProductReport.trackSearchPlaceOrderClick(mContext, purchaseInfo.mainProduct, listOf(purchaseInfo.subProducts[subPosition]), true)
            }

            override fun changeNum(itemBean: RowsBeanCombinedExt,curSubPos:Int,addFlag:Boolean,preNum:Int) {
                AdapterUtils.checkCombinedProductNum(purchaseInfo,itemBean,curSubPos,this@SearchAdapter,holder.layoutPosition)
            }

            override fun changeNumClick(bean: RowsBeanCombinedExt, curSubPosition: Int,addFlag:Boolean, preNum: Int) {
                trackCombinationBtnClick(bean, curSubPosition, addFlag, preNum)
            }
        }
        mGoodsCombinationSingleAdapter.bindItemView(
            holder,
            bean.groupPurchaseInfo!!)
    }
    private fun bindCombinationSingleItemView(
        holder: YBMBaseHolder,
        bean: SearchRowsBean,
        payloads:List<Any?>
    ){
        mGoodsCombinationSingleAdapter.bindItemView(
            holder,
            bean.groupPurchaseInfo!!,
            payloads
        )
    }

    /**
     * 加价购
     */
    private fun bindCombinationMultiItemView(
        holder: YBMBaseHolder,
        bean: SearchRowsBean
    ) {
        val purchaseInfo = bean.additionalPurchaseInfo!!
        mGoodsCombinationMultiAdapter.mListener =  object : CombinedBuyListener {
            override fun preSettle(subPosition: Int) {
                AdapterUtils.preSettle(mContext, purchaseInfo)
                SearchProductReport.trackSearchPlaceOrderClick(mContext, purchaseInfo.mainProduct, purchaseInfo.subProducts, true)
            }

            override fun jumpToGoodsDetail(bean: RowsBeanCombinedExt) {
                jumpToProductDetail(purchaseInfo,bean, holder)
                SearchProductReport.trackSearchGoodsClick(mContext, holder.bindingAdapterPosition, bean)
            }

            override fun changeNum(itemBean: RowsBeanCombinedExt,subPos:Int,addFlag: Boolean,preNum:Int) {
                if(itemBean.newQty == 0){
                    AdapterUtils.getAdditionalPurchaseInfo(purchaseInfo,this@SearchAdapter,true,holder.layoutPosition,false,null)
                }else{
                    AdapterUtils.checkCombinedProductNum(purchaseInfo,itemBean,-1,this@SearchAdapter,holder.layoutPosition)
                }
            }

            override fun changeNumClick(bean: RowsBeanCombinedExt, curSubPosition: Int,addFlag:Boolean, preNum: Int) {
                trackCombinationBtnClick(bean, curSubPosition, addFlag, preNum)
            }
        }
        mGoodsCombinationMultiAdapter.bindItemView(
            holder,
            bean.additionalPurchaseInfo!!)
    }
    private fun bindCombinationMultiItemView(
        holder: YBMBaseHolder,
        bean: SearchRowsBean,
        payloads:List<Any?>
    ){
        mGoodsCombinationMultiAdapter.bindItemView(
            holder,
            bean.additionalPurchaseInfo!!,
            payloads)
    }

    fun jumpToProductDetail(purchaseInfo: GroupPurchaseInfo, rowsBean: RowsBeanCombinedExt, holder: YBMBaseHolder) {
        XyyIoUtil.track("Test_Product_Action", rowsBean)
        var url =
            "ybmpage://productdetail?${IntentCanst.PRODUCTID}=${rowsBean.id}&nsid=${rowsBean.nsid ?: ""}&sdata=${rowsBean.sdata ?: ""}&sourceType=${rowsBean.sourceType}&search_sort_strategy_id=${rowsBean.searchSortStrategyCode}&index=${holder.bindingAdapterPosition}"
        getOpenUrlNotJump(
            url,
            flowData
        )?.let {
            url = it
        }

        val mParams = Bundle().apply {
            putString(IntentCanst.PRODUCTID, rowsBean.id.toString())
            putString("nsid", rowsBean.nsid ?: "")
            putString("sdata", rowsBean.sdata ?: "")
            putString("sourceType", rowsBean.sourceType)
            putString("search_sort_strategy_id", rowsBean.searchSortStrategyCode)
            putInt("index", holder.adapterPosition)
            putBoolean(IntentCanst.SHOW_GROUPPURCHASE_FLAG, rowsBean.showGroupPurchase())
            putBoolean(IntentCanst.SHOW_ADDITIONALPURCHASE_FLAG, rowsBean.showAdditinalPurchase())
            putSerializable(
                IntentCanst.JG_JSON_REPORT_PD_EXTEND_OUTER_BEAN,
                ReportPDExtendOuterBean().apply {
                    rank = holder.adapterPosition + 1
                    keyWord = rowsBean.searchKeyword ?: ""
                    searchSortStrategyId = rowsBean.searchSortStrategyCode ?: ""
                    operationId = rowsBean.operationId ?: ""
                    listPositionType = rowsBean.positionType.toString()
                    listPositionTypename = rowsBean.positionTypeName ?: ""
                }
            )
        }

        //这里带参太多了 只能Intent跳了 不能用路由
        val intent = Intent(mContext, ProductDetailActivity::class.java)
        intent.putExtras(mParams)
        mContext.startActivity(intent)
    }

    /**
     * 商品曝光埋点
     */
    private fun traceGoodsExposure(rowsBean: RowsBean, holder: YBMBaseHolder) {
        if (flowData != null) {
            flowDataPageOPListExposureWithCode(
                    flowData,
                    rowsBean.productId,
                    rowsBean.showName,
                    "0",
                    rowsBean.searchSortStrategyCode,
                    "0",
                    rowsBean.operationExhibitionId,
                    rowsBean.operationId,
                    "${holder.bindingAdapterPosition}"
            )
        }
    }

    fun searchPageListProductExposure(pageListProductExposure: PageListProductExposure?) {
        pageListProductExposure ?: return
        ReportManager.getInstance().report(
                pageListProductExposure
        )
    }
    fun searchPageListProductClick(pageListProductExposure: PageListProductClick?) {
        pageListProductExposure ?: return
        ReportManager.getInstance().report(
                pageListProductExposure
        )
    }

    /**
     * 设置商品View背景圆角
     */
    private fun getGoodsViewItemBackgroundResId(holder: YBMBaseHolder): Int {
        val currPosition = holder.bindingAdapterPosition
        //是否是第一个
        val isFirst = currPosition == 0
        //是否是最后一个
        val isLast = mData.size == currPosition + 1
        //上一个item是否是运营位类型（非单品）
        val isTopCircle = if (!isFirst) {
            val preBean = mData[currPosition - 1] as SearchRowsBean
            preBean.cardType == SEARCH_LIST_CARD_TYPE_OPERATION_POSITION && preBean.operationInfo != null && (preBean.operationInfo.showType == OPERATION_POSITION_TYPE_SINGLE_SHOP || preBean.operationInfo.showType == OPERATION_POSITION_TYPE_MULTI_SHOP)
        } else false
        //下一个item是否是运营位类型（非单品）
        val isBottomCircle = if (!isLast) {
            val nextBean = mData[currPosition + 1] as SearchRowsBean
            nextBean.cardType == SEARCH_LIST_CARD_TYPE_OPERATION_POSITION && nextBean.operationInfo != null && (nextBean.operationInfo.showType == OPERATION_POSITION_TYPE_SINGLE_SHOP || nextBean.operationInfo.showType == OPERATION_POSITION_TYPE_MULTI_SHOP)
        } else false
        return if (isTopCircle && isBottomCircle) R.drawable.shape_op_bg_radius_all
        else if (isTopCircle) R.drawable.shape_op_bg_radius_tl_tr
        else if (isBottomCircle) R.drawable.shape_op_bg_radius_bl_br
        else R.drawable.shape_op_bg_radius_nothing
    }

}