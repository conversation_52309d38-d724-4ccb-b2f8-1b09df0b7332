package com.ybmmarket20.adapter

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RegisterShopBean
import com.ybmmarket20.utils.ifNotNull

/**
 * <AUTHOR> Brin
 * @date : 2020/4/18 - 14:21
 * @Description :
 * @version
 */
class LinkShopAdapter(layoutId: Int, rows: ArrayList<RegisterShopBean?>,isSelectedList:ArrayList<Int?>) : YBMBaseAdapter<RegisterShopBean?>(layoutId, rows) {
    var isSelectedList=isSelectedList
    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: RegisterShopBean?) {
        ifNotNull(baseViewHolder, t) { holder, bean ->
            val ivSelected=holder.getView<ImageView>(R.id.iv_selected)
            holder.getView<TextView>(R.id.tv_shop_name).text = bean.name
            holder.getView<TextView>(R.id.tv_shop_address).text = bean.address
            if (isSelectedList.contains(baseViewHolder?.adapterPosition)){
                ivSelected.visibility = View.VISIBLE
        }else{
                ivSelected.visibility = View.GONE
            }
//            holder.itemView.setOnClickListener {
//
//                if (isSelectedList.contains(baseViewHolder?.adapterPosition)){
//                    isSelectedList.clear()
//                    ivSelected.visibility = View.GONE
//                    notifyDataSetChanged()
//                }else{
//                    isSelectedList.clear()
//                    isSelectedList.add(baseViewHolder?.adapterPosition)
//                    ivSelected.visibility = View.VISIBLE
//                    notifyDataSetChanged()
//                }
//            }
        }
    }

}