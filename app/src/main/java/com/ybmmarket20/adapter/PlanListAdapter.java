package com.ybmmarket20.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.PlanListData;
import com.ybmmarket20.view.SwipeMenuLayout;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Created by wh on 2017/4/6.
 */

public class PlanListAdapter extends YBMBaseAdapter<PlanListData> {

    private int tab;
    private OnSwipeListener mSwipeListener;
    private OnItemClickListener mItemClickListener;
    private List<PlanListData> mData;
    private SimpleDateFormat dateFormat;

    public PlanListAdapter(int layoutResId, List<PlanListData> data, int tab) {
        super(layoutResId, data);
        this.tab = tab;
        mData = data;
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
    }

    public void setTab(int tab){
        this.tab = tab;
    }

    public void setNewData(List<PlanListData> list,int tab) {
        this.tab = tab;
        if (mData == null) {
            mData = new ArrayList<>();
        }
        mData.clear();
        if (list != null) {
            mData.addAll(list);
        }
        super.setNewData(mData);
    }

    public void setOnSwipeListener(OnSwipeListener listener) {
        mSwipeListener = listener;
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        mItemClickListener = listener;
    }

    @Override
    protected void bindItemView(final YBMBaseHolder baseViewHolder, PlanListData planListBean) {

        switch (tab) {
            case 0:
                ((SwipeMenuLayout) baseViewHolder.itemView).setSwipeEnable(true);
                baseViewHolder.setText(R.id.tv_kind_num, planListBean.productAmount + "种");
                baseViewHolder.setText(R.id.tv_plan_list_name, planListBean.planningName);
                if (!TextUtils.isEmpty(planListBean.updateTimeString)) {
                    baseViewHolder.setText(R.id.tv_plan_update_time, planListBean.updateTimeString);
                }

                ((SwipeMenuLayout) baseViewHolder.itemView).setExpandListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mSwipeListener != null) {
                            mSwipeListener.onSwipe(baseViewHolder.getAdapterPosition());
                        }
                    }
                });
                baseViewHolder.getView(R.id.tv_rename).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mSwipeListener != null) {
                            ((SwipeMenuLayout) baseViewHolder.itemView).quickClose();
                            mSwipeListener.onRename(baseViewHolder.getAdapterPosition());
                        }
                    }
                });

                baseViewHolder.getView(R.id.tv_delete).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mSwipeListener != null) {
                            ((SwipeMenuLayout) baseViewHolder.itemView).quickClose();
                            mSwipeListener.onDelete(baseViewHolder.getAdapterPosition());
                        }
                    }
                });

                baseViewHolder.getView(R.id.tv_share).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mSwipeListener != null) {
                            ((SwipeMenuLayout) baseViewHolder.itemView).quickClose();
                            mSwipeListener.onShare(baseViewHolder.getAdapterPosition());
                        }
                    }
                });
                break;
            case 1:
                ((SwipeMenuLayout) baseViewHolder.itemView).setSwipeEnable(false);

                baseViewHolder.setText(R.id.tv_kind_num, planListBean.picNum + "张图片");
                baseViewHolder.setText(R.id.tv_plan_list_name, planListBean.purchaseName);
                String format = dateFormat.format(new Date(planListBean.subTime));

                baseViewHolder.setText(R.id.tv_plan_update_time, format);
                break;
        }

        baseViewHolder.getView(R.id.rl_content).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mItemClickListener != null) {
                    mItemClickListener.onItemClick(baseViewHolder.getAdapterPosition());
                }
            }
        });

    }


    public interface OnSwipeListener {
        void onRename(int position);

        void onDelete(int position);

        void onShare(int position);

        void onSwipe(int position);
    }

    public interface OnItemClickListener {
        void onItemClick(int position);
    }
}
