package com.ybmmarket20.adapter

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.payment.VirtualGoldRechargeBean

/**
 * @class   ShoppingGoldRechargeAdapter
 * <AUTHOR>
 * @date  2025/2/25
 * @description 购物金充值适配器
 */
class ShoppingGoldRechargeAdapter(
        data: ArrayList<VirtualGoldRechargeBean>
): YBMBaseAdapter<VirtualGoldRechargeBean>(R.layout.item_shopping_gold_recharge, data) {

    var onItemClickListener: ((VirtualGoldRechargeBean,isReadySelect:Boolean) -> Unit)? = null

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, bean: VirtualGoldRechargeBean) {
        baseViewHolder?:return
        val tvMoneyContent = baseViewHolder.getView<TextView>(R.id.tv_money_content)
        val tvRedEnvelope = baseViewHolder.getView<TextView>(R.id.tv_red_envelope)
        val tvCanUse = baseViewHolder.getView<TextView>(R.id.tv_can_use)
        val ivRecharge = baseViewHolder.getView<ImageView>(R.id.iv_shopping_gold_recharge)

        tvMoneyContent.text = bean.amountText?:""
        bean.rightText?.let {
            tvRedEnvelope.text = it
            if (it.isEmpty()){
                tvRedEnvelope.visibility = View.GONE
            }else{
                tvRedEnvelope.visibility = View.VISIBLE
            }
        }?: kotlin.run {
            tvRedEnvelope.visibility = View.GONE
        }

        if (bean.isCanUse){
            tvCanUse.visibility = View.VISIBLE
        }else{
            tvCanUse.visibility = View.GONE
        }

        // 设置选中状态
        ivRecharge.isSelected = bean.isSelected()

        // 添加点击事件
        baseViewHolder.itemView.setOnClickListener {
            onItemClickListener?.invoke(bean,bean.isSelected())
        }
    }

    public fun getSelectedBean(): VirtualGoldRechargeBean? {
        for (bean in data ) {
            if (bean is VirtualGoldRechargeBean?){
                if (bean?.isSelected() == true) {
                    return bean
                }
            }
        }
        return null
    }

}