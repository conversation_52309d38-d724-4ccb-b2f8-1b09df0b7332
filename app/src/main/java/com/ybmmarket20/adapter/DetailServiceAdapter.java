package com.ybmmarket20.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.PromiseListBean;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.ViewHolderUtil;

import java.util.List;

/**
 * 商品详情-服务简介
 */

public class DetailServiceAdapter extends YbmBaseAdapter<PromiseListBean> {

    public DetailServiceAdapter(List<PromiseListBean> list, Context context) {
        super(list, context);
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {

        final PromiseListBean promiseListBean = mList.get(position);
        if (convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(
                    R.layout.detail_service_item, viewGroup, false);
        }
        ImageView iv = ViewHolderUtil.get(convertView, R.id.iv_service);
        TextView tv = ViewHolderUtil.get(convertView, R.id.tv_service);

        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + promiseListBean.image)
                .placeholder(R.drawable.transparent)
                .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .dontTransform().dontAnimate().into(iv);

        tv.setText(promiseListBean.title);

        convertView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(promiseListBean);
                }
            }
        });

        return convertView;
    }

    public interface OnGridViewItemClickListener {
        void onItemClick(PromiseListBean rows);
    }

    private OnGridViewItemClickListener mOnItemClickListener = null;

    public void setOnItemClickListener(OnGridViewItemClickListener listener) {
        this.mOnItemClickListener = listener;
    }
}
