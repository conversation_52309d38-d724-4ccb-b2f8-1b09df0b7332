package com.ybmmarket20.common;

import static com.ybm100.app.push.PushNotificationManager.MSGCLICKED;

import android.app.Activity;
import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.multidex.MultiDex;

import com.baidu.location.LocationClient;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.tencent.wework.api.IWWAPI;
import com.tencent.wework.api.WWAPIFactory;
import com.ybm.app.bean.PushBean;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.common.apicache.ApiCacheManager;
import com.ybm.app.utils.Utils;
import com.ybm100.app.push.PushNotificationManager;
import com.ybmmarket20.BuildConfig;
import com.ybmmarket20.common.util.Abase;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.ConstantData;
import com.ybmmarket20.db.DBHelper;
import com.ybmmarket20.message.Message;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SettingProxy;
import com.ybmmarket20.utils.ShareUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.waf.XyyWafManager;
import com.ybmmarket20.xyyreport.XyyReportManager;
import com.ydmmarket.report.ReportManager;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;

import cn.jpush.android.api.JPushInterface;


/**
 * tinker代理application 类
 */
public class YBMAppLike extends BaseYBMApp {
    public final static int MSGCENTER = 1000;//消息中心 场景
    public static int cartNum = 0;//购物车数量
    public static int[] endLocation = new int[2];// 存储动画结束位置的X、Y坐标
    public static int[] endLocationInCoupon = new int[2];// 存储动画结束位置的X、Y坐标
    public static int[] endLocationInCommodity = new int[2];// 存储动画结束位置的X、Y坐标在商品详情页
    private static final String KEY_APP_VERSION = "app_version";
    public static int createdActivityCount = 0;

    private int spVersion = 0;
    public static boolean isNewTheme = false;
    public static volatile IWXAPI mApi;
    public static IWWAPI mWWApi;

    public static String preActivityName = "";
    public static String currentActivityName = "";

    public String registerSource = null;
    public String saasOrderSourcePath = null;
    public String organSign = null;

    private int countActivity = 0;
    //是否进入后台
    private boolean isBackground = false;
    public boolean isShowCallUser = false;

    private OnForegroundListener mOnForegroundListener;
    //从添加首营资质
    public boolean mineSelectShop = false;

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        fixTimeout();
        PrivacyInitManager.INSTANCE.installPingAn(this);
        MultiDex.install(this);
    }


    @Override
    public void onCreate() {
        super.onCreate();
        LocationClient locationClient = new LocationClient(getApplicationContext());

        //初始化Flutter容器
        //initFlutterContainer();

        registerActivityLifecycleCallbacks(mActivityLifecycle);
        Abase.initialize(this);

        PrivacyInitManager.INSTANCE.initXyyIO(this);
        //推送
        PrivacyInitManager.INSTANCE.initXyyPush();
        PrivacyInitManager.INSTANCE.initTbs(this);

        PrivacyInitManager.INSTANCE.initMMKV(this);

        try {
            SQLiteDatabase db = DBHelper.getDb(this);
            db.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (PrivacyInitManager.INSTANCE.isAgreedPrivacy()) {
            try {
                HashMap<String, String> contentMap = new HashMap<>();
                String merchantId = SpUtil.getMerchantid();
                contentMap.put("merchantId", merchantId);
                contentMap.put("appVersion", Utils.getVersionName(this));
                XyyIoUtil.track(XyyIoUtil.ACTION_APP_STARTUP, contentMap);
                XyyReportManager.init(this);
            } catch (Throwable e) {

            }
        } else {
            XyyReportManager.preInit(this);
        }
        handleDirtyData();

        //极光埋点
        initJgAnalysys();
        // 初始化升级SDK
        initUpdate();

        // 初始化apm - countly  SDK
        PrivacyInitManager.INSTANCE.initApmCly();
        PrivacyInitManager.INSTANCE.initPingAn(this);
        PrivacyInitManager.INSTANCE.initWaf(this);
        try {
            //FIXME WAF初始化必须延迟两秒后才能调用接口
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void initUpdate() {
        PrivacyInitManager.INSTANCE.initUpdate(this);
    }

    //极光埋点
    private void initJgAnalysys(){
        if (PrivacyInitManager.INSTANCE.isAgreedPrivacy()) {
            ReportManager.getInstance().init(this);
        }
    }

    private void handleDirtyData() {
        String loginPhoneOld = SpUtil.getLoginPhoneOld();
        if (loginPhoneOld != null && !loginPhoneOld.isEmpty()) {
            SpUtil.setLoginPhone(loginPhoneOld);
            SpUtil.remove("LOGIN_PHONE");
        }
    }

    @Override
    public boolean isAgreedPrivacy() {
        return PrivacyInitManager.INSTANCE.isAgreedPrivacy();
    }

    private void initFlutterContainer() {
//        ContainerRuntime.INSTANCE.init(this, new ContainerConfig.Builder().setContainerClass(CustomFlutterActivity.class).build());
//        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("can_direct_jump", CanDirectJumpHandler.class);
//        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("app_info", AppInfoHandler.class);
//        ContainerRuntime.INSTANCE.getBridge().setBridgeImpl(new YBMFlutterBridgeImpl());
//        ContainerRuntime.INSTANCE.getFlutterRouter().addRouterInterceptor(new CanDirectJumpInterceptor());
        //ContainerRuntime.INSTANCE.getFlutterRouter().open(getAppContext(), "/");
    }

    public void setOnForegroundListener(OnForegroundListener onForegroundListener) {
        mOnForegroundListener = onForegroundListener;
    }


    private ActivityLifecycleCallbacks mActivityLifecycle = new ActivityLifecycleCallbacks() {


        @Override
        public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
            createdActivityCount++;
        }

        @Override
        public void onActivityStarted(Activity activity) {
            countActivity++;
            if (countActivity == 1 && isBackground) {
                isBackground = false;
                //说明应用重新进入了前台
                isShowCallUser = true;
            } else {
                isShowCallUser = false;
            }
        }

        @Override
        public void onActivityResumed(Activity activity) {
            currentActivityName = activity.getLocalClassName();
            Abase.setCurrentActivity(activity);
        }

        @Override
        public void onActivityPaused(Activity activity) {
        }

        @Override
        public void onActivityStopped(Activity activity) {
            preActivityName = XyyIoUtil.getPageName(activity);
            countActivity--;
            if (countActivity <= 0 && !isBackground) {
                isBackground = true;
            }
        }

        @Override
        public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

        }

        @Override
        public void onActivityDestroyed(Activity activity) {
        }

    };

    public interface OnForegroundListener {
        void callback();
    }


    @Override
    protected void initDebugMode() {
        super.initDebugMode();
    }

    @Override
    protected void initSDKMainProcess() {
        super.initSDKMainProcess();
//        YbmCommand.init();//药口令功能
    }

    @Override
    protected void initSDKMainProcessBg() {
        super.initSDKMainProcessBg();
        if (!isDebug()) {
            SettingProxy.setProxy();
        }
        int ver = Utils.getVersionCode(getAppContext());
        if (isNewInstall() || ver > getSpVersion()) {//新安装或者升级，清空本地ip,一定要在接口请未前设置
            AppNetConfig.cleanIp();
            APPUpdateManager.cleanDirBg();
            ApiCacheManager.getInstance().cleanAllCache();
        }
        mApi = WXAPIFactory.createWXAPI(this, ConstantData.APP_ID_WX, true);
        mWWApi = WWAPIFactory.createWWAPI(this);
        mWWApi.registerApp(ShareUtil.SHARE_WEWORK_SCHEMA);
        PrivacyInitManager.INSTANCE.initBugly();
        PrivacyInitManager.INSTANCE.initPushManager(this);
        setSpVersion(ver);
        initVoice();
    }

    private void initVoice() {
        SmartExecutorManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                RoutersUtils.init();
                PrivacyInitManager.INSTANCE.initVoice();
            }
        });
    }

    @Override
    public boolean isDebug() {
        return BuildConfig.DEBUG;
    }


    @Override
    public void handlerPush(String content, int type, int from, final PushBean bean) {//推送消息处理类
        if (TextUtils.isEmpty(content)) {
            return;
        }

        if (from == MSGCLICKED) {
            if (bean.pushMsgId == null) bean.pushMsgId = "";
            String registrationId = JPushInterface.getRegistrationID(this);
            if (registrationId == null) registrationId = "";
            JSONObject pro = new JSONObject();
            try {
                pro.put("pushMsgId", bean.pushMsgId);
                pro.put("registrationId", registrationId);
                XyyIoUtil.track("action_Push_Click", pro);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (!TextUtils.isEmpty(bean.action) && from != MSGCLICKED) {//主线程处理
            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                @Override
                public void run() {
                    RoutersUtils.open(bean.action);
                }
            });
        } else if (!TextUtils.isEmpty(bean.pageUrl) && from == PushNotificationManager.MSGCLICKED) {
            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                @Override
                public void run() {
                    RoutersUtils.open(bean.pageUrl);
                }
            });
        }
        switch (type) {//业场景自己定义
            case MSGCENTER:
                SmartExecutorManager.getInstance().executeUI(new Runnable() {
                    @Override
                    public void run() {
                        Message.instance.increase();
                    }
                });
//                LocalBroadcastManager.getInstance(getAppContext()).sendBroadcast(new Intent(IntentCanst.MSG_NUM_EDIT));//有新的消息来了
                break;
        }
    }

    private int getSpVersion() {
        if (spVersion == 0) {
            spVersion = SpUtil.readInt(KEY_APP_VERSION, -1);
        }
        return spVersion;
    }

    private void setSpVersion(int ver) {
        if (ver <= 0) {
            ver = Utils.getVersionCode(getAppContext());
        }
        SpUtil.writeInt(KEY_APP_VERSION, ver);
    }

    private boolean isNewInstall() {
        return getSpVersion() == -1;
    }

    //改变顶部的bar背景
    public static void changeThemeBg(int resBg, View... views) {
        if (!isNewTheme || resBg <= 0 || views == null || views.length == 0) {
            return;
        }
        for (View view : views) {
            if (view == null) {
                continue;
            }
            view.setBackgroundResource(resBg);
        }
    }


    /**
     * 处理 Timeout 异常
     */
    private void fixTimeout() {
        try {
            Class<?> c = Class.forName("java.lang.Daemons");
            Field maxField = c.getDeclaredField("MAX_FINALIZE_NANOS");
            maxField.setAccessible(true);
            maxField.set(null, Long.MAX_VALUE);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }

        try {
            Class<?> clazz = Class.forName("java.lang.Daemons$FinalizerWatchdogDaemon");
            Method method = clazz.getSuperclass().getDeclaredMethod("stop");
            method.setAccessible(true);
            Field field = clazz.getDeclaredField("INSTANCE");
            field.setAccessible(true);
            method.invoke(field.get(null));
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

}
