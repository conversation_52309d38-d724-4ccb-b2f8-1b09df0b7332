package com.ybmmarket20.apshare;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;

import com.alipay.share.sdk.openapi.*;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.utils.ShareUtil;

public class ShareEntryActivity extends Activity implements IAPAPIEventHandler {

    @Override
    protected void onCreate(@Nullable @org.jetbrains.annotations.Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //创建工具对象实例，此处的APPID为上文提到的，申请应用生效后，在应用详情页中可以查到的支付宝应用唯一标识
        IAPApi api = APAPIFactory.createZFBApi(getApplicationContext(), ShareUtil.SHARE_ALI_APPID, false);
        Intent intent = getIntent();
        //通过调用工具实例提供的handleIntent方法，绑定消息处理对象实例，
        api.handleIntent(intent, this);
    }

    @Override
    public void onReq(BaseReq baseReq) {
//        Toast.makeText(this, "Result Code:" + baseReq.toString(), Toast.LENGTH_LONG).show();
    }

    @Override
    public void onResp(BaseResp baseResp) {
        if (baseResp.errCode == 0) {
            ToastUtils.showShort("分享成功");
        } else {
            ToastUtils.showShort("分享失败");
        }
        finish();
    }
}
