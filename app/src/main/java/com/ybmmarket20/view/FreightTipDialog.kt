package com.ybmmarket20.view

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.graphics.Point
import android.os.Bundle
import androidx.core.content.ContextCompat
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.FreightTipBean
import com.ybmmarket20.bean.FreightTipItem
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.widget.RoundLinearLayout
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.SpUtil

/**
 * <AUTHOR>
 * 运费提示
 */
class FreightTipDialog(val mContext: Context) : Dialog(mContext, R.style.AlertDialog) {

    private var tv_tip_content: TextView? = null
    private var tv_i_got_it: TextView? = null
    private var sv_freight: ScrollView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setCancelable(false)
    }

    override fun show() {
        super.show()
    }

    fun showTip() {
        getTipContent(null)
    }

    /**
     * 显示对话框
     */
    fun showTip(shopCode: String? = null) {
        getTipContent(shopCode)
    }

    /**
     * 获取数据
     */
    private fun getTipContent(shopCode: String?) {
        val params = RequestParams().also {
            it.put("merchantId", SpUtil.getMerchantid())
            shopCode?.apply {
                it.put("shopCode", this)
            }
            it.url = AppNetConfig.FREIGHT_DIALOG
        }
        HttpManager.getInstance().post(params, object : BaseResponse<FreightTipBean>() {
            override fun onSuccess(content: String?, obj: BaseBean<FreightTipBean>?, t: FreightTipBean?) {
                super.onSuccess(content, obj, t)
                if (context is BaseActivity && (context as BaseActivity).isDestroy) return
                val contentView = View.inflate(mContext, R.layout.dialog_freight_tip, null) as RoundLinearLayout
                setContentView(contentView)
                tv_tip_content = contentView.findViewById(R.id.tv_tip_content)
                tv_i_got_it = contentView.findViewById(R.id.tv_i_got_it)
                sv_freight = contentView.findViewById(R.id.sv_freight)
                tv_i_got_it?.setOnClickListener {
                    dismiss()
                }
                val wm = (mContext as Activity).windowManager
                val display = wm.defaultDisplay
                val lp = window?.attributes
                val point = Point()
                display.getSize(point)
                lp?.width = (point.x * 0.75f).toInt()
                window?.attributes = lp
                t?.also {
                    tv_tip_content?.text = assembleContent(it.freightTemplateTipsList)
                }
                contentView.postDelayed({
                    if (sv_freight?.measuredHeight ?: 0 > ConvertUtils.dp2px(357f)) {
                        val llLp = sv_freight!!.layoutParams as LinearLayout.LayoutParams
                        llLp.height = ConvertUtils.dp2px(357f)
                        sv_freight!!.layoutParams = llLp
                    }
                }, 100)
                show()
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
            }
        })
    }

    fun showHtml(content: CharSequence, title: String) {
        val contentView = View.inflate(mContext, R.layout.dialog_freight_tip, null) as RoundLinearLayout
        setContentView(contentView)
        tv_tip_content = contentView.findViewById(R.id.tv_tip_content)
        tv_i_got_it = contentView.findViewById(R.id.tv_i_got_it)
        sv_freight = contentView.findViewById(R.id.sv_freight)
        contentView.findViewById<TextView>(R.id.tv_title).text = title
        tv_i_got_it?.setOnClickListener {dismiss() }
        val wm = (mContext as Activity).windowManager
        val display = wm.defaultDisplay
        val lp = window?.attributes
        val point = Point()
        display.getSize(point)
        lp?.width = (point.x * 0.75f).toInt()
        window?.attributes = lp
        tv_tip_content?.text = content
        contentView.postDelayed({
            if (sv_freight?.measuredHeight ?: 0 > ConvertUtils.dp2px(357f)) {
                val llLp = sv_freight!!.layoutParams as LinearLayout.LayoutParams
                llLp.height = ConvertUtils.dp2px(357f)
                sv_freight!!.layoutParams = llLp
            }
        }, 100)
        show()
    }

    fun showNormal(content: String, title: String) {
        val contentView = View.inflate(mContext, R.layout.dialog_freight_tip, null) as RoundLinearLayout
        setContentView(contentView)
        tv_tip_content = contentView.findViewById(R.id.tv_tip_content)
        tv_i_got_it = contentView.findViewById(R.id.tv_i_got_it)
        sv_freight = contentView.findViewById(R.id.sv_freight)
        contentView.findViewById<TextView>(R.id.tv_title).text = title
        tv_i_got_it?.setOnClickListener {dismiss() }
        val wm = (mContext as Activity).windowManager
        val display = wm.defaultDisplay
        val lp = window?.attributes
        val point = Point()
        display.getSize(point)
        lp?.width = (point.x * 0.75f).toInt()
        window?.attributes = lp
        tv_tip_content?.text = content
        contentView.postDelayed({
            if (sv_freight?.measuredHeight ?: 0 > ConvertUtils.dp2px(357f)) {
                val llLp = sv_freight!!.layoutParams as LinearLayout.LayoutParams
                llLp.height = ConvertUtils.dp2px(357f)
                sv_freight!!.layoutParams = llLp
            }
        }, 100)
        show()
    }

    /**
     * 组装内容
     */
    private fun assembleContent(content: MutableList<FreightTipItem>?): SpannableStringBuilder {
        val span = SpannableStringBuilder("")
        content?.forEach { freightTipItem ->
            val titleSpan = SpannableStringBuilder(freightTipItem.title)
            val textColorSpan = ForegroundColorSpan(ContextCompat.getColor(context, R.color.color_292933))
            val textSizeSpan = AbsoluteSizeSpan(14, true)
            titleSpan.setSpan(textColorSpan, 0, titleSpan.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            titleSpan.setSpan(textSizeSpan, 0, titleSpan.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            titleSpan.append("\n")
            span.append(titleSpan)
            freightTipItem.freightTips?.forEach { contentStr ->
                val tipSpan = SpannableStringBuilder(contentStr)
                val tipTextColorSpan = ForegroundColorSpan(ContextCompat.getColor(context, R.color.color_676773))
                val tipSizeSpan = AbsoluteSizeSpan(12, true)
                tipSpan.setSpan(tipTextColorSpan, 0, tipSpan.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                tipSpan.setSpan(tipSizeSpan, 0, tipSpan.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                tipSpan.append("\n")
                span.append(tipSpan)
            }
            if (content.indexOf(freightTipItem) != content.size - 1) {
                span.append("\n")
            }
        }
        return span
    }
}