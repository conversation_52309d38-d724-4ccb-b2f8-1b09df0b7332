# 搜索配送时效引导组件

## 概述
这是一个用于搜索页面的配送时效引导组件，当用户首次进入搜索结果页面且包含配送时效筛选选项时，会显示引导蒙层来指导用户使用配送时效功能。

## 组件结构

### 1. SearchDeliveryGuideView.kt
主要的引导组件类，负责：
- 显示半透明蒙层背景
- 在指定位置显示手势指引图片
- 在手势图片下方显示"我知道了"按钮
- 处理点击事件和位置计算

### 2. SearchGuideManager.kt
引导状态管理工具类，负责：
- 管理引导显示状态（避免重复显示）
- 使用SharedPreferences持久化状态
- 提供重置功能用于测试

### 3. view_search_delivery_guide.xml
引导组件的布局文件，包含：
- 半透明黑色背景
- 手势指引图片
- "我知道了"按钮图片

## 使用方法

### 1. 在BaseSearchProductActivity中的集成
组件已经集成到BaseSearchProductActivity中：

```java
// 在initData方法中初始化
initDeliveryGuide();

// 在updateSearchData方法中检查显示
if (isFirst) {
    checkAndShowDeliveryGuide();
}
```

### 2. 显示条件
引导会在以下条件都满足时显示：
- 搜索结果页面首次加载（isFirst = true）
- 包含配送时效筛选选项（rbExpress按钮可见）
- 用户之前没有看过此引导

### 3. 图片资源
需要准备以下图片资源：
- `mask_hands.png`: 手势指引图片
- `mask_iknow.png`: "我知道了"按钮图片

### 4. 自定义使用
如果需要在其他页面使用，可以参考以下代码：

```kotlin
// 创建引导组件
val deliveryGuideView = SearchDeliveryGuideView(context)
rootLayout.addView(deliveryGuideView)

// 设置点击监听
deliveryGuideView.setOnGuideClickListener {
    SearchGuideManager.markDeliveryGuideShown(context)
    // 其他逻辑
}

// 显示引导
if (SearchGuideManager.shouldShowDeliveryGuide(context, hasDeliveryOptions)) {
    targetView.post {
        deliveryGuideView.showGuide(targetRadioGroup)
    }
}
```

## API说明

### SearchDeliveryGuideView

#### 方法
- `showGuide(targetRadioGroup: RadioGroup?)`: 显示引导，相对于指定的RadioGroup定位
- `hideGuide()`: 隐藏引导
- `setOnGuideClickListener(listener: () -> Unit)`: 设置点击监听器
- `isShowing(): Boolean`: 检查是否正在显示

### SearchGuideManager

#### 方法
- `shouldShowDeliveryGuide(context: Context, hasDeliveryOptions: Boolean): Boolean`: 检查是否应该显示引导
- `markDeliveryGuideShown(context: Context)`: 标记引导已显示
- `resetDeliveryGuideState(context: Context)`: 重置引导状态（测试用）

## 注意事项

1. **位置计算**: 组件会自动计算手势图片和按钮的位置，确保在目标组件居中显示
2. **生命周期**: 引导组件会在点击后自动隐藏并标记状态
3. **性能**: 使用post方法确保在布局完成后再显示引导
4. **测试**: 可以使用resetDeliveryGuideState方法重置状态进行测试

## 埋点
组件集成了埋点功能：
- 引导显示时的曝光埋点
- 用户点击引导时的点击埋点

## 扩展性
如果需要添加其他类型的引导，可以：
1. 在SearchGuideManager中添加新的状态管理
2. 创建新的引导组件类
3. 在相应的页面中集成使用
