package com.ybmmarket20.view

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.view.ViewTreeObserver
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybmmarket20.R
import com.ybmmarket20.bean.CheckOrderRowsBean
import com.ybmmarket20.common.widget.RoundLinearLayout
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import kotlinx.android.synthetic.main.view_order_item_aptitude.view.*

/**
 * 卡单
 */
class OrderItemAptitudeView(context: Context, attr: AttributeSet?): ConstraintLayout(context, attr) {

    var mFromPage = ""
    var mCanTrack = false

    init {
        View.inflate(context, R.layout.view_order_item_aptitude, this)
    }

    var merchantExceptionCheckCallback: ((msg: String) -> Unit)? = null
    var systemExceptionCheckCallback: ((msg: String) -> Unit)? = null

    fun setData(data: CheckOrderRowsBean) {
        val llSystem = findViewById<LinearLayout>(R.id.llSystem)
        val llMerchant = findViewById<LinearLayout>(R.id.llMerchant)
        val tvSystem = findViewById<TextView>(R.id.tvSystem)
        val tvMerchant = findViewById<TextView>(R.id.tvMerchant)
        val tvMerchantCheck = findViewById<TextView>(R.id.tvMerchantCheck)
        val rllUpdate = findViewById<RoundLinearLayout>(R.id.rllUpdate)
        rllUpdate.setOnClickListener {
            RoutersUtils.open("ybmpage://aptitude")
            XyyIoUtil.track("Update_Qualification", hashMapOf(
                "order_no" to data.orderNo,
                "page_source" to mFromPage
            ))
        }
        setOnClickListener{}
        visibility = if (data.hasOrderExceptionFlag) {
            llSystem.visibility = if (TextUtils.isEmpty(data.sysException)) {
                 View.GONE
            } else View.VISIBLE
            llMerchant.visibility = if (TextUtils.isEmpty(data.supplierException)) {
                View.GONE
            } else View.VISIBLE
            tvSystem.text = data.sysException?.replace("\n", "")?: ""
            tvMerchant.text = data.supplierException?.replace("\n", "")?: ""
            if (TextUtils.isEmpty(data.sysException) && TextUtils.isEmpty(data.supplierException)) View.GONE else View.VISIBLE
        } else {
            View.GONE
        }
        tvMerchantCheck.setOnClickListener {
            merchantExceptionCheckCallback?.invoke(data.supplierException)
            handleCheckTrack(data, false)
        }
        tvSystemCheck.setOnClickListener {
            systemExceptionCheckCallback?.invoke(data.sysException)
            handleCheckTrack(data, true)
        }
        val vto = tvMerchant.viewTreeObserver
        vto.addOnGlobalLayoutListener(VTObserver(tvMerchant, tvMerchantCheck))
        val systemVto = tvSystem.viewTreeObserver
        systemVto.addOnGlobalLayoutListener(VTObserver(tvSystem, tvSystemCheck))

        if (data.hasOrderExceptionFlag && mCanTrack) {
            postDelayed({
                XyyIoUtil.track("Business_Reminder_More_Exposure", hashMapOf(
                    "order_no" to data.orderNo,
                    "page_source" to mFromPage,
                    "system_more" to if (tvSystem.visibility == View.GONE) "0" else "1",
                    "business_more" to if (tvMerchant.visibility == View.GONE) "0" else "1"
                ))
            }, 300)
        }
    }

    fun setFromPage(fromPage: String) {
        mFromPage = fromPage
    }

    fun setCanTrack(canTrack: Boolean) {
        mCanTrack = canTrack
    }

    fun handleCheckTrack(data: CheckOrderRowsBean, isSystemException: Boolean) {
        XyyIoUtil.track("Business_Reminder_More_Detail", hashMapOf(
            "order_no" to data.orderNo,
            "page_source" to mFromPage,
            "system_more_click" to if (isSystemException) "1" else "0",
            "business_more_click" to if (isSystemException) "0" else "1"
        ))
    }

    inner class VTObserver(val contentView: TextView, private val checkView: TextView): ViewTreeObserver.OnGlobalLayoutListener {
        override fun onGlobalLayout() {
            contentView.viewTreeObserver.removeOnGlobalLayoutListener(this)
            contentView.height
            val tvMerchantWidth = contentView.width
            val tvMerchantTextWidth = contentView.paint.measureText(contentView.text.toString())
            if (tvMerchantTextWidth > tvMerchantWidth) {
                //换行
                checkView.visibility = View.VISIBLE
            } else {
                //不换行
                checkView.visibility = View.GONE
            }
        }
    }
}