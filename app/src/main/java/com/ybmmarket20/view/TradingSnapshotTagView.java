package com.ybmmarket20.view;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.LabelIconBean;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.utils.UiUtils;

import java.util.List;

/**
 * 二级标签view
 */

public class TradingSnapshotTagView extends LinearLayout {
    public TradingSnapshotTagView(Context context) {
        this(context, null);
    }

    public TradingSnapshotTagView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }


    private void initView() {
        setOrientation(HORIZONTAL);
//        setGravity(Gravity.CENTER_VERTICAL | Gravity.LEFT);
    }

    /**
     * 绑定数据
     *
     * @param list 标签组
     *             默认三个，
     */
    public void bindData(List<LabelIconBean> list) {
        bindData(list, 3);
    }

    /**
     * 绑定数据
     *
     * @param list 标签组
     * @param max  最多显示多少个，<=0 或者3 都用默认三个，
     */
    public void bindData(List<LabelIconBean> list, int max, boolean gone) {
        removeAllViews();
        if (list == null || list.size() <= 0) {//添加一个空view,填充高度问题
            setVisibility(gone ? View.GONE : View.INVISIBLE);
            addView(createView(null));
            return;
        } else {
            setVisibility(View.VISIBLE);
        }
        if (max <= 0) {
            max = 3;
        }
        int size = Math.min(max, list.size());
        for (int a = 0; a < size; a++) {
            addView(createView(list.get(a)));
        }
    }


    /**
     * 绑定数据
     *
     * @param list 标签组
     * @param max  最多显示多少个，<=0 或者3 都用默认三个，
     */
    public void bindData(List<LabelIconBean> list, int max) {
        bindData(list, max, false);
    }

    private TextView createView(LabelIconBean bean) {
        TextView textView = new TextView(getContext());
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.setMargins(0, 0, ConvertUtils.dp2px(4), 0);
        textView.setGravity(Gravity.CENTER);
        textView.setSingleLine(true);
        textView.setLayoutParams(params);
        textView.setPadding(UiUtils.dp2px(4), UiUtils.dp2px(1), UiUtils.dp2px(4), UiUtils.dp2px(1));
        textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f);
        int drawRes = R.drawable.bg_brand_item_type4;
        int colorRes = R.color.brand_icon_type4;
        if (bean != null) {
            textView.setText(bean.name);
            switch (bean.uiType) {
                //标签显示类型 1,临期 2，券 3，自定义1 4，自定义2
                case 1:
                    drawRes = R.drawable.bg_brand_item_type1;
                    colorRes = R.color.white;
                    break;
                case 2:
                    drawRes = R.drawable.shape_tag_bg_red;
                    colorRes = R.color.color_ff2121;
                    break;
                case 3:
                    drawRes = R.drawable.bg_brand_item_type3;
                    colorRes = R.color.brand_icon_type3;
                    break;
                case 5://医保
                    drawRes = R.drawable.bg_brand_item_health_insurance;
                    colorRes = R.color.white;
                    break;
                case 4:
                default:
                    drawRes = R.drawable.bg_brand_item_type4;
                    colorRes = R.color.brand_icon_type4;
                    break;

            }
        } else {
            textView.setVisibility(View.INVISIBLE);
        }
        textView.setBackgroundResource(drawRes);
        textView.setTextColor(getResources().getColor(colorRes));
        return textView;
    }
}
