package com.ybmmarket20.view.cms;

import android.content.Context;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.adapter.GoodsListAdapter;
import com.ybmmarket20.adapter.ProductGrid3AdapterCms;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.cms.ModuleBeanCms;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.home.BrandFragment;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.view.indicator.CircleRecyclerPageIndicator;
import com.ybmmarket20.view.snaprecycleview.GravitySnapHelper;
import com.ybmmarket20.view.snaprecycleview.GridPagerUtils;
import com.ybmmarket20.view.snaprecycleview.InvertRowColumnDataTransform;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 集合推荐 recommendList
 */
public class DynamicSlideItemPagerLayoutCms extends BaseDynamicLayoutCms<RowsBean> {

    private LinearLayout llTitle;
    private TextView tvRecTitle;
    private TextView tvRecSubTitle;
    private int getDefTitleHeigth = 102;

    private final static int ITEM_GRID_NUM_DEF = 6;
    private final static int ITEM_GRID_NUM_1 = 3;
    private final static int ITEM_GRID_NUM_2 = 9;

    public int item_grid_num = ITEM_GRID_NUM_DEF;//每一页中GridView中item的数量
    public int number_columns = 3;//gridview一行展示的数目

    private int styleNum;

    private RecyclerView rvRecommend;
    private CircleRecyclerPageIndicator crpiCenter;
    private BrandFragment.MyItemDecoration itemDecoration;
    private ProductGrid3AdapterCms adapter;

    private boolean isWhiteBg;
    // 原始数据，需要处理一下
    private List<RowsBean> rowsBeanList;
    // 处理后的数据
    private List<RowsBean> itemDataList;
    private int rowCount;           // 几行
    private GridLayoutManager gridLayoutManager;

    // 整个控件的左右外边距
    private int marginLeft = 10;
    private int marginRight = 10;

    private int paddingLeft = 0;
    private int paddingRight = 0;

    public DynamicSlideItemPagerLayoutCms(Context context) {
        super(context);
    }

    public DynamicSlideItemPagerLayoutCms(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicSlideItemPagerLayoutCms(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initViews() {
        rvRecommend = (RecyclerView) findViewById(R.id.rv_recommend_list);
        crpiCenter = (CircleRecyclerPageIndicator) findViewById(R.id.crpiCenter);

        llTitle = (LinearLayout) findViewById(R.id.ll_title);
        tvRecTitle = (TextView) findViewById(R.id.tv_rec_title);
        tvRecSubTitle = (TextView) findViewById(R.id.tv_rec_sub_title);

        //itemDecoration = new BrandFragment.MyItemDecoration();
        rowCount = 2;
        gridLayoutManager = new GridLayoutManager(getContext(), rowCount, LinearLayoutManager.HORIZONTAL, false);
        rvRecommend.setLayoutManager(gridLayoutManager);
        rvRecommend.setNestedScrollingEnabled(false);
        rvRecommend.setOverScrollMode(OVER_SCROLL_NEVER);
        //rvRecommend.addItemDecoration(itemDecoration);
        itemDataList = new ArrayList<>();
        adapter = new ProductGrid3AdapterCms(itemDataList);
        adapter.setOnListItemClickListener(new GoodsListAdapter.OnListViewItemClickListener() {
            @Override
            public void onItemClick(RowsBean rows) {
                // cms 横向商品埋点
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put("id", rows.getId());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                XyyIoUtil.track(XyyIoUtil.ACTION_HOME_COLLECTION_PRODUCT, jsonObject,rows);

                RoutersUtils.open("ybmpage://productdetail/" + rows.getId());
            }
        });
        rvRecommend.setAdapter(adapter);
    }

    @Override
    public int getDefBg() {
        return -1;
    }

    @Override
    public boolean supportSetHei() {
        return false;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_slide_item_cms;
    }

    @Override
    public void setItemData(ModuleBeanCms moduleBean, List<RowsBean> list, boolean isUpdate) {
        if (list == null || list.isEmpty()) {
            return;
        }
        if (rowsBeanList != null && rowsBeanList.size() > 0) {
            rowsBeanList.clear();
        }
        this.rowsBeanList = list;
        setRecommentListTitle(moduleBean);
        setStyle(getCount());
        adapter.setWhiteBg(isWhiteBg);
        configCenterRecyclerView(getCount() / 3, 3);
    }

    private void configCenterRecyclerView(int row, int column) {

        rvRecommend.setHasFixedSize(false);

        //setLayoutManager
        if (gridLayoutManager != null || row != rowCount) {
            gridLayoutManager = new GridLayoutManager(getContext(), row, LinearLayoutManager.HORIZONTAL, false);
            rvRecommend.setLayoutManager(gridLayoutManager);
        }

        // setRecyclerWith
        if (moduleBean.styles != null && moduleBean.styles.margin != null && moduleBean.styles.margin.size() >= 4 && moduleBean.styles.margin.get(3) != marginLeft && moduleBean.styles.margin.get(1) != marginRight) {
            marginLeft = moduleBean.styles.margin.get(3);
            marginRight = moduleBean.styles.margin.get(1);
            adapter.setMargin(marginLeft, marginRight);

        }
        if (moduleBean.styles != null && moduleBean.styles.padding != null && moduleBean.styles.padding.size() >= 4 && moduleBean.styles.padding.get(3) != paddingLeft && moduleBean.styles.padding.get(1) != paddingRight) {
            paddingLeft = moduleBean.styles.padding.get(3);
            paddingRight = moduleBean.styles.padding.get(1);
            adapter.setPadding(paddingLeft, paddingRight);
        }

        //getDataSource
        itemDataList.clear();
        itemDataList.addAll(GridPagerUtils.transformAndFillEmptyData(
                new InvertRowColumnDataTransform<>(column, row), rowsBeanList));

        //setAdapter
        if (rvRecommend.isComputingLayout()) {
            rvRecommend.post(new Runnable() {
                @Override
                public void run() {
                    adapter.notifyDataSetChanged();
                }
            });
        } else {
            adapter.notifyDataSetChanged();
        }

        //attachToRecyclerView
        GravitySnapHelper snapHelper = new GravitySnapHelper(Gravity.CENTER);
        snapHelper.setColumn(column);
        snapHelper.attachToRecyclerView(rvRecommend);
        snapHelper.setCanPageScroll(true);

        if (itemDataList.size() <= getCount()) {
            crpiCenter.setVisibility(View.GONE);
        } else {
            crpiCenter.setVisibility(View.VISIBLE);
            crpiCenter.setRecyclerView(rvRecommend);
            crpiCenter.setFillColor(getColor(content.color));
            crpiCenter.setPageColumn(column);
        }
    }

    private void setRecommentListTitle(ModuleBeanCms moduleBean) {
        if (tvRecTitle != null && (!TextUtils.isEmpty(moduleBean.content.recTitle)
                || !TextUtils.isEmpty(moduleBean.content.recSubTitle))
                || !TextUtils.isEmpty(moduleBean.content.titleImage)) {
            if (!TextUtils.isEmpty(moduleBean.content.recTitle)) {
                tvRecTitle.setVisibility(View.VISIBLE);
                tvRecTitle.setText(moduleBean.content.recTitle);
            }
            if (!TextUtils.isEmpty(moduleBean.content.recSubTitle)) {
                tvRecSubTitle.setVisibility(View.VISIBLE);
                tvRecSubTitle.setText(moduleBean.content.recSubTitle);
            }

            LayoutParams params = (LayoutParams) tvTitle.getLayoutParams();
            if (params == null) {
                params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(getDefTitleHeigth));
            }
            int hei = 0;
            if (styles != null) {
                hei = moduleBean.styles.height;
            }
            if (hei <= 0) {
                hei = getDefTitleHeigth;
            }

            if (hei == 0) {
                llTitle.setVisibility(View.GONE);
                llTitle.setOnClickListener(null);
            } else {
                params.height = dp2px(hei);
                llTitle.setLayoutParams(params);
            }

            setNetBackground(llTitle, moduleBean.content.titleImage);
            if (!TextUtils.isEmpty(moduleBean.content.action)) {//f设置块点击事件
                llTitle.setTag(R.id.tag_action, moduleBean.content.action);
                llTitle.setTag(R.id.tag_click_type, XyyIoUtil.ACTION_HOME_COLLECTION_PRODUCT_TITLE_IMAGE);
                llTitle.setOnClickListener(itemClick);
            }

            tvRecTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 32);
            tvRecSubTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);

            if (!TextUtils.isEmpty(moduleBean.content.rectitleColor)) {
                tvRecTitle.setTextColor(getColor(moduleBean.content.rectitleColor));
            }
            if (!TextUtils.isEmpty(moduleBean.content.recSubTitleColor)) {
                tvRecSubTitle.setTextColor(getColor(moduleBean.content.recSubTitleColor));
            }

            TextPaint paint = tvRecTitle.getPaint();
            paint.setFakeBoldText(true);
            paint.setTextSkewX(0);

            paint = tvRecSubTitle.getPaint();
            paint.setFakeBoldText(false);
            paint.setTextSkewX(0);

        } else {
            if (llTitle != null) {
                llTitle.setVisibility(View.GONE);
                llTitle.setOnClickListener(null);
            }
            this.setTag(R.id.tag_action, moduleBean.content.action);
            llTitle.setTag(R.id.tag_click_type, XyyIoUtil.ACTION_HOME_COLLECTION_PRODUCT_TITLE_IMAGE);
            this.setOnClickListener(itemClick);
        }
    }

    private int getCount() {
        int count = 0;
        try {
            count = Integer.parseInt(content.count);
        } catch (NumberFormatException e) {
            count = 6;
        }
        return count;
    }

    @Override
    public void setStyle(int style) {
        if (style <= 0 && getContext() != null && getContext() instanceof MainActivity) {
            style = 6;
        }
        if (this.styleNum == style && adapter != null && isWhiteBg == isWhiteBg()) {
            return;
        }
        this.styleNum = style;
        isWhiteBg = isWhiteBg();
        switch (style) {
            case 6:
            default:
                item_grid_num = ITEM_GRID_NUM_DEF;
                break;
            case 3:
                item_grid_num = ITEM_GRID_NUM_1;
                break;
            case 9:
                item_grid_num = ITEM_GRID_NUM_2;
                break;
        }
    }

    @Override
    public void setImageView(ImageView view, RowsBean bean) {

    }

    @Override
    public boolean needUpdateItem(ModuleBeanCms<RowsBean> moduleBean, List<RowsBean> list) {
        return true;
    }

    private boolean isWhiteBg() {
        if (TextUtils.isEmpty(content.bgRes) || "#ffffff".equals(content.bgRes.toLowerCase())
                || "#F7F7F8".equals(content.bgRes.toLowerCase())) {
            return false;
        }
        return true;
    }
}
