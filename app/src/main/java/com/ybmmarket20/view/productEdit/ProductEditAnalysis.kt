package com.ybmmarket20.view.productEdit

import android.content.Context
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.xyyreport.page.search.SearchProductReport

/**
 * 加购按钮埋点
 */
class ProductEditAnalysis: IProductEditAnalysis {
    override fun onAddCartBtnClick(context: Context, rowsBean: RowsBean, position: Int) {
        if (rowsBean.isOpGoods && !rowsBean.isOPSingleGoods) return
        SearchProductReport.trackSearchItemBtnClickAddCart(context, position, rowsBean)
    }

    override fun onAddBtnClick(context: Context, rowsBean: RowsBean, position: Int) {
        if (rowsBean.isOpGoods && !rowsBean.isOPSingleGoods) return
        SearchProductReport.trackSearchItemBtnClickAdd(context, position, rowsBean)
    }

    override fun onSubtractBtnClick(context: Context, rowsBean: RowsBean, position: Int) {
        if (rowsBean.isOpGoods && !rowsBean.isOPSingleGoods) return
        SearchProductReport.trackSearchItemBtnClickSubtract(context, position, rowsBean)
    }

    override fun onProductEditNumClick(
        context: Context,
        rowsBean: RowsBean,
        position: Int,
        content: String
    ) {
        if (rowsBean.isOpGoods && !rowsBean.isOPSingleGoods) return
        SearchProductReport.trackSearchItemBtnClickAddCount(context, position, rowsBean, content)
    }
}