package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.RadioGroup
import com.ybmmarket20.R

/**
 * 搜索页面配送时效引导组件
 * 在搜索结果页面显示引导用户使用配送时效筛选功能
 */
class SearchDeliveryGuideView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var ivHands: ImageView? = null
    private var ivIKnow: ImageView? = null
    private var onGuideClickListener: (() -> Unit)? = null

    init {
        initView()
    }

    private fun initView() {
        // 设置半透明黑色背景
        setBackgroundColor(0x80000000.toInt())
        
        // 创建手势图片
        ivHands = ImageView(context).apply {
            setImageResource(R.drawable.mask_hands)
            scaleType = ImageView.ScaleType.CENTER
        }
        
        // 创建"我知道了"按钮图片
        ivIKnow = ImageView(context).apply {
            setImageResource(R.drawable.mask_iknow)
            scaleType = ImageView.ScaleType.CENTER
        }
        
        // 添加到布局
        addView(ivHands)
        addView(ivIKnow)
        
        // 设置点击事件
        setOnClickListener {
            hideGuide()
            onGuideClickListener?.invoke()
        }
        
        // 默认隐藏
        visibility = View.GONE
    }

    /**
     * 显示引导，相对于指定的RadioGroup定位
     */
    fun showGuide(targetRadioGroup: RadioGroup?) {
        if (targetRadioGroup == null) return
        
        visibility = View.VISIBLE
        
        // 获取目标组件的位置信息
        val location = IntArray(2)
        targetRadioGroup.getLocationInWindow(location)
        
        val targetX = location[0]
        val targetY = location[1]
        val targetWidth = targetRadioGroup.width
        val targetHeight = targetRadioGroup.height
        
        // 计算手势图片位置（在mBrandRg02组件居中）
        val handsLayoutParams = LayoutParams(
            LayoutParams.WRAP_CONTENT,
            LayoutParams.WRAP_CONTENT
        ).apply {
            // 相对于父容器的位置
            leftMargin = targetX + (targetWidth / 2) - (ivHands?.drawable?.intrinsicWidth ?: 0) / 2
            topMargin = targetY + (targetHeight / 2) - (ivHands?.drawable?.intrinsicHeight ?: 0) / 2
        }
        ivHands?.layoutParams = handsLayoutParams
        
        // 计算"我知道了"按钮位置（在手势图片下方居中）
        val iKnowLayoutParams = LayoutParams(
            LayoutParams.WRAP_CONTENT,
            LayoutParams.WRAP_CONTENT
        ).apply {
            leftMargin = targetX + (targetWidth / 2) - (ivIKnow?.drawable?.intrinsicWidth ?: 0) / 2
            topMargin = targetY + targetHeight + 20 // 在目标组件下方20dp
        }
        ivIKnow?.layoutParams = iKnowLayoutParams
        
        // 请求重新布局
        requestLayout()
    }

    /**
     * 隐藏引导
     */
    fun hideGuide() {
        visibility = View.GONE
    }

    /**
     * 设置引导点击监听器
     */
    fun setOnGuideClickListener(listener: () -> Unit) {
        this.onGuideClickListener = listener
    }

    /**
     * 检查是否正在显示
     */
    fun isShowing(): Boolean {
        return visibility == View.VISIBLE
    }
}
