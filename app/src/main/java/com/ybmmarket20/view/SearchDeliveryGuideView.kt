package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.RadioGroup
import com.ybmmarket20.R

/**
 * 搜索页面配送时效引导组件
 * 在搜索结果页面显示引导用户使用配送时效筛选功能
 */
class SearchDeliveryGuideView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var ivHands: ImageView? = null
    private var ivIKnow: ImageView? = null
    private var onGuideClickListener: OnGuideClickListener? = null

    interface OnGuideClickListener {
        fun onGuideClick()
    }

    init {
        initView()
    }

    private fun initView() {
        // 加载布局文件
        LayoutInflater.from(context).inflate(R.layout.view_search_delivery_guide, this, true)

        // 获取视图引用
        ivHands = findViewById(R.id.iv_guide_hands)
        ivIKnow = findViewById(R.id.iv_guide_iknow)

        // 设置点击事件
        setOnClickListener {
            hideGuide()
            onGuideClickListener?.onGuideClick()
        }

        // 默认隐藏
        visibility = View.GONE
    }

    /**
     * 显示引导，相对于指定的RadioGroup定位
     */
    fun showGuide(targetRadioGroup: RadioGroup?) {
        if (targetRadioGroup == null) return

        visibility = View.VISIBLE

        // 延迟执行定位，确保布局已完成
        post {
            positionGuideViews(targetRadioGroup)
        }
    }

    private fun positionGuideViews(targetRadioGroup: RadioGroup) {
        // 获取目标组件相对于父容器的位置
        val targetLocation = IntArray(2)
        val parentLocation = IntArray(2)

        targetRadioGroup.getLocationInWindow(targetLocation)
        (parent as? View)?.getLocationInWindow(parentLocation)

        val relativeX = targetLocation[0] - parentLocation[0]
        val relativeY = targetLocation[1] - parentLocation[1]
        val targetWidth = targetRadioGroup.width
        val targetHeight = targetRadioGroup.height

        // 设置手势图片位置（在mBrandRg02组件居中）
        ivHands?.let { hands ->
            val handsLayoutParams = hands.layoutParams as LayoutParams
            handsLayoutParams.leftMargin = relativeX + (targetWidth / 2) - (hands.drawable?.intrinsicWidth ?: 0) / 2
            handsLayoutParams.topMargin = relativeY + (targetHeight / 2) - (hands.drawable?.intrinsicHeight ?: 0) / 2
            handsLayoutParams.gravity = 0 // 清除gravity，使用margin定位
            hands.layoutParams = handsLayoutParams
        }

        // 设置"我知道了"按钮位置（在手势图片下方居中）
        ivIKnow?.let { iknow ->
            val iKnowLayoutParams = iknow.layoutParams as LayoutParams
            iKnowLayoutParams.leftMargin = relativeX + (targetWidth / 2) - (iknow.drawable?.intrinsicWidth ?: 0) / 2
            iKnowLayoutParams.topMargin = relativeY + targetHeight + 40 // 在目标组件下方40dp
            iKnowLayoutParams.gravity = 0 // 清除gravity，使用margin定位
            iknow.layoutParams = iKnowLayoutParams
        }
    }

    /**
     * 隐藏引导
     */
    fun hideGuide() {
        visibility = View.GONE
    }

    /**
     * 设置引导点击监听器
     */
    fun setOnGuideClickListener(listener: OnGuideClickListener?) {
        this.onGuideClickListener = listener
    }

    /**
     * 检查是否正在显示
     */
    fun isShowing(): Boolean {
        return visibility == View.VISIBLE
    }
}
