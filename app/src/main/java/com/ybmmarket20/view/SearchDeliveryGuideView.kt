package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.RadioGroup
import com.ybmmarket20.R
import com.ybmmarket20.view.searchFilter.view.SearchDynamicLabelView

/**
 * 搜索页面配送时效引导组件
 * 在搜索结果页面显示引导用户使用配送时效筛选功能
 */
class SearchDeliveryGuideView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var ivHands: ImageView? = null
    private var ivIKnow: ImageView? = null
    private var onGuideClickListener: OnGuideClickListener? = null

    interface OnGuideClickListener {
        fun onGuideClick()
    }

    init {
        initView()
    }

    private fun initView() {
        // 加载布局文件
        LayoutInflater.from(context).inflate(R.layout.view_search_delivery_guide, this, true)

        // 获取视图引用
        ivHands = findViewById(R.id.iv_guide_hands)
        ivIKnow = findViewById(R.id.iv_guide_iknow)

        // 设置点击事件
        setOnClickListener {
            hideGuide()
            onGuideClickListener?.onGuideClick()
        }

        // 默认隐藏
        visibility = View.GONE
    }

    /**
     * 显示引导，相对于指定的SearchDynamicLabelView定位
     */
    fun showGuide(targetDynamicLabelView: SearchDynamicLabelView?) {
        if (targetDynamicLabelView == null) return

        visibility = View.VISIBLE

        // 延迟执行定位，确保布局已完成
        post {
            positionGuideViewsForDynamicLabel(targetDynamicLabelView)
        }
    }

    private fun positionGuideViewsForDynamicLabel(targetDynamicLabelView: SearchDynamicLabelView) {
        // 获取屏幕宽度
        val screenWidth = resources.displayMetrics.widthPixels

        // 获取目标组件在屏幕中的绝对位置
        val targetLocation = IntArray(2)
        targetDynamicLabelView.getLocationInWindow(targetLocation)

        // 由于现在使用DecorView作为父容器，直接使用屏幕坐标
        val targetY = targetLocation[1]

        // 获取目标组件的高度，用于计算真正的顶部位置
        val targetHeight = targetDynamicLabelView.height

        // 计算手势图标应该对齐的Y坐标（目标组件的真正顶部）
        // 如果当前位置在中间，需要减去一半高度来对齐到顶部
        val adjustedY = targetY - (targetHeight / 2)

        // 设置手势图片位置
        // 顶部与SearchDynamicLabelView顶部对齐，水平屏幕居中
        ivHands?.let { hands ->
            val handsLayoutParams = hands.layoutParams as LayoutParams
            val handsWidth = dpToPx(102) // 固定宽度102dp

            // 水平居中
            handsLayoutParams.leftMargin = (screenWidth - handsWidth) / 2
            // 顶部与目标组件真正的顶部对齐
            handsLayoutParams.topMargin = adjustedY
            handsLayoutParams.gravity = 0 // 清除gravity，使用margin定位
            hands.layoutParams = handsLayoutParams
        }

        // 设置"我知道了"按钮位置（顶部与手势图片底部对齐）
        ivIKnow?.let { iknow ->
            val iKnowLayoutParams = iknow.layoutParams as LayoutParams
            val handsHeight = dpToPx(124) // 固定高度124dp
            val margin16dp = dpToPx(16)

            // 设置宽度为屏幕宽度减去左右各16dp
            iKnowLayoutParams.width = screenWidth - (margin16dp * 2)
            iKnowLayoutParams.height = dpToPx(119) // 固定高度119dp

            // 设置左边距16dp
            iKnowLayoutParams.leftMargin = margin16dp
            iKnowLayoutParams.rightMargin = 0 // 不需要右边距，因为宽度已经计算好了
            // 顶部与手势图片底部对齐
            iKnowLayoutParams.topMargin = adjustedY + handsHeight
            iKnowLayoutParams.gravity = 0 // 清除gravity，使用margin定位
            iknow.layoutParams = iKnowLayoutParams
        }
    }

    /**
     * dp转px
     */
    private fun dpToPx(dp: Int): Int {
        return (dp * resources.displayMetrics.density).toInt()
    }

    /**
     * 隐藏引导
     */
    fun hideGuide() {
        visibility = View.GONE
    }

    /**
     * 设置引导点击监听器
     */
    fun setOnGuideClickListener(listener: OnGuideClickListener?) {
        this.onGuideClickListener = listener
    }

    /**
     * 检查是否正在显示
     */
    fun isShowing(): Boolean {
        return visibility == View.VISIBLE
    }

    /**
     * 检查SearchDynamicLabelView中是否包含"配送时效"标签
     */
    fun hasDeliveryTimeLabel(dynamicLabelView: SearchDynamicLabelView?): Boolean {
        if (dynamicLabelView == null) return false

        return try {
            dynamicLabelView.mDataList.any { config ->
                config.labelName?.contains("配送时效") == true
            }
        } catch (e: Exception) {
            false
        }
    }
}
