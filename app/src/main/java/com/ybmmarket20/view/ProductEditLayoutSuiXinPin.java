package com.ybmmarket20.view;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.xyy.canary.utils.LogUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.ProductEditLayoutSuccessParams;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.UiUtils;

/**
 * 商品增加、减少、输入布局
 */
public class ProductEditLayoutSuiXinPin extends FrameLayout {
    private long lastTime;
    protected static RequestParams editShopNumberParams;
    public final static int DIFF_TIME = 400;
    private int ADD_STEP = 10;//加号处理
    private boolean split = true;//可拆开卖
    private boolean needHide = true;//true 可以收起 false不能收起
    protected ImageView iv_numSub;
    protected ImageView iv_numAdd;
    protected TextView tv_number;
    protected int number;
    protected int oldNumber;
    private long proId;
    private boolean isPackage;
    protected ViewGroup rootView;
    private boolean modifyViewStyle = false;
    protected FrameLayout fl_root_view;
    private AddCartListener mAddCartListener;
    // 按钮类型-减
    public static final String BUTTON_TYPE_REDUCE = "1";
    // 按钮类型-输入
    public static final String BUTTON_TYPE_INPUT = "2";
    // 按钮类型-加
    public static final String BUTTON_TYPE_ADD= "3";

    //这里先不处理了， 因为加购成功不会进购物车
    public JgTrackBean jgTrackBean = null;
    public ProductEditLayoutSuiXinPin(Context context) {
        this(context, null);
    }

    public ProductEditLayoutSuiXinPin(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ProductEditLayoutSuiXinPin(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initViews();
    }

    public void initViews() {
        View view = View.inflate(getContext(), R.layout.product_edit_layout_new, this);
        iv_numSub = view.findViewById(R.id.iv_numSub);
        iv_numAdd = view.findViewById(R.id.iv_numAdd);
        tv_number = view.findViewById(R.id.tv_number);
        fl_root_view = view.findViewById(R.id.rl_layout);
//        tv_number.setEnabled(false);
        tv_number.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s != null) {
                    if (s.length() <= 3) {
                        tv_number.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15);
                    }
                    if (s.length() >= 4) {
                        tv_number.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }

    public int getProductNum(){
        return number;
    }

    /**
     * 最小的状态，使整个控件最小
     */
    private void toMin() {
        iv_numSub.setVisibility(GONE);
        tv_number.setVisibility(GONE);
        iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_only_add);
        fl_root_view.getLayoutParams().width = LayoutParams.WRAP_CONTENT;
    }

    private void toMax() {
        ViewGroup.LayoutParams layoutParams = (LayoutParams) fl_root_view.getLayoutParams();
        layoutParams.width = ConvertUtils.dp2px(75);
        fl_root_view.setLayoutParams(layoutParams);
        iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_add);
        iv_numSub.setVisibility(VISIBLE);
        tv_number.setVisibility(VISIBLE);
    }

    /**
     * @param skuId
     * @param status
     * @param isBuy
     * @param needHide 当为0时或者小于0，减号那部分是否需要隐藏
     * @param step
     * @param split
     */
    public void bindData(final String skuId, final int status, final boolean isBuy, boolean needHide, int step, final boolean split, String numberStr) {
        try {
            this.proId = Long.parseLong(skuId);
        } catch (Exception e) {
            this.proId = 0;
        }
        this.split = split;
        if (step <= 0) {
            step = 1;
        }
        ADD_STEP = step;
        if (isBuy) {
            tv_number.setInputType(InputType.TYPE_NULL);
            //初始化商品数量
            tv_number.setTag(R.id.tag_3, false);
            tv_number.setText(String.valueOf(0));
            tv_number.setTextColor(getResources().getColor(R.color.coupon_limit_tv01));
            tv_number.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15);
            tv_number.setOnClickListener(v -> {
                if (v.getContext() instanceof BaseActivity) {
                    ((BaseActivity) v.getContext()).hideSoftInput();
                }
                final TextView textNum = (TextView) v;
                //编辑弹出对话框加减数量
                DialogUtil.addOrSubDialog(((BaseActivity) v.getContext()), InputType.TYPE_CLASS_NUMBER, textNum.getText().toString(), ADD_STEP
                        , split, true, new DialogUtil.DialogClickListener() {

                            private InputMethodManager mImm;

                            @Override
                            public void confirm(String content) {
                                if (!checkProductCanBuy(status)) {
                                    return;
                                }
                                sendShopNum(proId, content);
                            }

                            @Override
                            public void cancel() {

                            }

                            @Override
                            public void showSoftInput(View view) {
                                mImm = (InputMethodManager) (view.getContext()).getSystemService(Context.INPUT_METHOD_SERVICE);
                                mImm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
                            }

                        });
            });

            try {
                number = Integer.parseInt(numberStr);
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (number < 0) {
                number = 0;
            }

            //如果是小于0并且需要隐藏，那么才隐藏,否则不隐藏
            boolean initSate = number <= 0 && needHide;
            if (initSate) {
                fl_root_view.setBackgroundColor(getResources().getColor(R.color.white));
                iv_numSub.setVisibility(View.GONE);
                tv_number.setVisibility(View.GONE);
                iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_only_add);
                ViewGroup.LayoutParams layoutParams = fl_root_view.getLayoutParams();
                layoutParams.width = UiUtils.dp2px(25);
                fl_root_view.setLayoutParams(layoutParams);
            } else {
                iv_numSub.setVisibility(View.VISIBLE);
                tv_number.setVisibility(View.VISIBLE);
                iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_add);
                ViewGroup.LayoutParams layoutParams = fl_root_view.getLayoutParams();
                layoutParams.width = UiUtils.dp2px(75);
                fl_root_view.setLayoutParams(layoutParams);
            }

            if (!modifyViewStyle) {
                if (initSate) {
                    iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_only_add);
                } else {
                    iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_add);
                }
            }
            if (number >= 0) {
                tv_number.setText(String.valueOf(number));
            }
            if (number > 0 && oldNumber <= 0) {
                toMax();
                showReduceView();
            }
            oldNumber = number;
            iv_numSub.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    numOnClick(proId, status, false);
                }
            });
            iv_numAdd.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    numOnClick(proId, status, true);
                }
            });
        }
    }

    private boolean checkProductCanBuy(int status) {
        if (status == 2) {
            ToastUtils.showShort("产品已经售罄");
            return false;
        } else if (status == 4) {
            ToastUtils.showShort("产品已经下架不能购买");
            return false;
        }
        return true;
    }

    public void numOnClick(long skuId, final int status, boolean isAdd) {
        if (!checkProductCanBuy(status)) {
            return;
        }

        //获取商品的数量
        number = Integer.parseInt(tv_number.getText().toString());
        int preNum=number;
        if (!isAdd) {
            if (split) {
                number -= 1;
            } else {
                number -= ADD_STEP;
            }
            if (number < 1) {
                number = 0;
            }
        } else {
            if (number <= 0) {//如果本来是小于0的，添加的时候显示出来
                toMax();
                showReduceView();
                tv_number.setTag(R.id.tag_3, true);
            }
            number += ADD_STEP;
        }
        tv_number.setText(number + "");
//        if (isAdd) {
//            isFastDoubleClick(skuId, true, number);
//        } else {
//            isFastDoubleClick(skuId, false, number);
//        }
        editShopNumber(skuId, isAdd, false, 0, tv_number, getButtonType(isAdd),preNum);
    }

    private String getButtonType(boolean isAdd) {
        return isAdd? BUTTON_TYPE_ADD: BUTTON_TYPE_REDUCE;
    }

    /**
     * 页面关闭时调用
     */
    private void clean() {
        editShopNumberParams = null;
    }

    private class LastRunnable implements Runnable {
        private long proId;
        private int number;
        private boolean lastIsAdd;

        @Override
        public void run() {
            if (tv_number == null) {
                return;
            }
            if (System.currentTimeMillis() - lastTime < DIFF_TIME) {
                return;
            }
            editShopNumber(proId, lastIsAdd, isPackage, number, tv_number, getButtonType(lastIsAdd),number);//执行切换的任务
        }

        public void setData(long proId, int number, boolean lastIsAdd) {
            this.proId = proId;
            this.lastIsAdd = lastIsAdd;
            this.number = number;
        }
    }

    public void sendShopNum(long skuId, String str) {
        //获取商品的数量
        int num = 0;
        try {
            num = Integer.parseInt(str);
        } catch (Exception e) {
            num = 0;
        }
        if (num <= 0) {
            num = 0;
        }
        boolean isAdd = false;
        if (number <= 0 && num > 0) {
            tv_number.setTag(R.id.tag_3, true);
        }
        isAdd = num > number;
        int preNum=number;
        number = num;
        tv_number.setText(String.valueOf(number));
        editShopNumber(skuId, isAdd, isPackage, number, tv_number, BUTTON_TYPE_INPUT,preNum);
    }

    //修改商品发送到服务器
    private void editShopNumber(final long id, final boolean isAdd, final boolean isPackage, final int number, final TextView tv_number, String buttonType,int preNum) {
        if (mAddCartListener != null) {
            ProductEditLayoutSuccessParams params = new ProductEditLayoutSuccessParams(buttonType, tv_number.getText().toString(),preNum);
            mAddCartListener.onAddCartSuccess(params);
        }
    }

    private void showReduceView() {
//        ValueAnimator animator = ValueAnimator.ofFloat(0f, 100f);
//        animator.setDuration(200);
//        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
//            @Override
//            public void onAnimationUpdate(ValueAnimator animation) {
//                //百分比对应的值
//                float value = (float) animation.getAnimatedValue();
//                ViewGroup.LayoutParams layoutParams = ProductEditLayoutSuiXinPin.this.getLayoutParams();
//                layoutParams.width = UiUtils.dp2px(25) + (int) ((UiUtils.dp2px(50) * value) / 100f);
//                ProductEditLayoutSuiXinPin.this.setLayoutParams(layoutParams);
//                if (value == 0) {
//                    iv_numSub.setVisibility(View.VISIBLE);
//                    tv_number.setVisibility(View.VISIBLE);
//                }
//                if (value == 100) {
//                    iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_add);
//                }
//            }
//        });
//        animator.start();

        ViewGroup.LayoutParams layoutParams = ProductEditLayoutSuiXinPin.this.getLayoutParams();
        layoutParams.width = UiUtils.dp2px(75);
        ProductEditLayoutSuiXinPin.this.setLayoutParams(layoutParams);
        iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_add);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        new Handler(Looper.myLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                clean();
            }
        }, DIFF_TIME);
    }

    /**
     * 加购添加监听
     *
     * @param listener
     */
    public void setOnAddCartListener(AddCartListener listener) {
        mAddCartListener = listener;
    }

    /**
     * 加购监听接口
     */
    public interface AddCartListener {
        //加购前
        RequestParams onPreAddCart(RequestParams params);

        //加购成功
        void onAddCartSuccess(ProductEditLayoutSuccessParams params);
    }

}
