package com.ybmmarket20.view.homesteady

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.FastEntry
import com.ybmmarket20.bean.homesteady.FastEntryItem
import com.ybmmarket20.common.JgTrackBean


/**
 * <AUTHOR>
 * @date 2020-05-11
 * @description 首页快捷入口
 */

class HomeSteadyFastEntryView(context: Context, attr: AttributeSet?) : RecyclerView(context, attr), IHomeSteady {

    private var analysisCallback: ((String, Int, FastEntryItem, View, Int, String) -> Unit)? = null

    private var isTransparent: Boolean = false

    var dataChangeCallBack:(()->Unit)? = null //测量出来的高度的回调

    constructor(context: Context): this(context, null)

    constructor(context: Context, isTransparent: Boolean): this(context, null) {
        this.isTransparent = isTransparent
    }

    var mData = mutableListOf<FastEntryItem>()
    var mAdapter: HomeSteadyFastEntryAdapter? = null
    var mSuperData: FastEntry? = null
    var jgTrackBean: JgTrackBean? = null
    var mHomeJgspid: String? = null
    private var isSetData = false

    override fun initPlaceHold() {
        setAdapterData(generatePlaceHoldData())
    }

    fun initSingleLineHoldPlace() {
        setAdapterData(generatePlaceHoldData(5))
    }

    fun setFastEntryData(data: MutableList<FastEntryItem>?) {
        if (isSetData) return
        data?.also(this::setAdapterData)
        isSetData = true
    }

    override fun onMeasure(widthSpec: Int, heightSpec: Int) {
        val mHeightSpec = MeasureSpec.makeMeasureSpec(0,MeasureSpec.UNSPECIFIED)
        super.onMeasure(widthSpec, mHeightSpec)
    }


    @SuppressLint("NotifyDataSetChanged")
    private fun setAdapterData(data: MutableList<FastEntryItem>) {
        mData.clear()
        mData.addAll(data)
        if (mAdapter == null) {
            mAdapter = if (isTransparent) {
                HomeSteadyFastEntryAdapter(context, mData, mHomeJgspid, mSuperData, R.layout.item_home_steady_fast_entry_v2)
            } else HomeSteadyFastEntryAdapter(context, mData, mHomeJgspid, mSuperData)
            layoutManager = GridLayoutManager(context, 5)
            adapter = mAdapter?.apply {
                jgTrackBean = <EMAIL>
            }
        } else mAdapter?.notifyDataSetChanged()
        dataChangeCallBack?.invoke()
        analysisCallback?.let {
            mAdapter?.setAnalysisCallback(it, this)
        }
    }

    private fun generatePlaceHoldData(count: Int = 10): MutableList<FastEntryItem> = arrayListOf<FastEntryItem>().apply {
        for(i in 0 until count) {
            add(FastEntryItem().apply { itemType = HOME_STEADY_LAYOUT_DEFAULT })
        }
    }

    /**
     * 设置埋点回调
     */
    fun setAnalysisCallback(callback: ((String, Int, FastEntryItem, View, Int, String) -> Unit)?) {
        analysisCallback = callback
    }

}