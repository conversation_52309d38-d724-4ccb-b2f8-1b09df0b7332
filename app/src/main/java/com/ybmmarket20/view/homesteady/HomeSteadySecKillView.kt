package com.ybmmarket20.view.homesteady

import android.annotation.SuppressLint
import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.SeckillInfo
import com.ybmmarket20.bean.homesteady.SeckillModule
import com.ybmmarket20.bean.homesteady.SeckillProduct
import com.ybmmarket20.utils.IntervalListener
import com.ybmmarket20.utils.IntervalUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.databinding.backgroundColorStr
import com.ybmmarket20.view.databinding.imageUrlNoPlace
import com.ybmmarket20.view.homesteady.callback.ISeckillAnalysisCallback
import kotlinx.android.synthetic.main.layout_home_steady_seckill.view.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * 秒杀模块
 */
class HomeSteadySecKillView(context: Context, attrs: AttributeSet) : BaseHomeSteadyView(context, attrs) {

    var intervalListener: IntervalListener? = null

    var preStatus: Int = -1

    var adapter: HomeSteadySecKillAdapter? = null

    private var analysisCallback: ISeckillAnalysisCallback? = null

    var showType: Int = 1

    override fun getLayoutId(): Int = 0

    override fun initPlaceHold() {

    }

    init {
        View.inflate(context, R.layout.layout_home_steady_seckill, this)
    }

    /**
     * 设置数据
     * @param showType 1：限时秒杀 2：拼团
     */
    fun setData(seckill: SeckillModule, licenseStatus: Int, showType: Int = 1) {
        this.showType = showType
        val seckillInfo = seckill.content
        backgroundColorStr(cl_bg, seckillInfo?.bgColor)
        imageUrlNoPlace(iv_seckill_bg, seckillInfo?.bgUrl)
        imageUrlNoPlace(iv_title, seckillInfo?.mainTitleUrl)
        imageUrlNoPlace(iv_des, seckillInfo?.subTitleUrl)
        seckill.content?.list?.also {
            val rv = findViewById<RecyclerView>(R.id.rv_seckill)
            rv.layoutManager = WrapLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            if (seckill.content?.list?.size ?:0 >= 4) {
                SeckillProduct().apply {
                    this.itemType = SECKILL_MORE
                    seckill.content?.list?.add(this)
                }
            }
            adapter = HomeSteadySecKillAdapter(it, analysisCallback, showType, seckill.content?.jumpUrl)
            adapter?.setLicenseStatus(licenseStatus)
            adapter?.setMoreUrl(seckill.content?.jumpUrl)
            rv.adapter = adapter
            tv_seckill_entry.text = seckill.content?.jumpName
            tv_seckill_entry.setOnClickListener {
                RoutersUtils.open(seckill.content?.jumpUrl)
                analysisCallback?.onHomeSteadyAnalysisSeckillTopClick(seckill.content?.jumpUrl, "$showType")
            }
        }
        setBg(seckill)
    }

    /**
     * 设置背景
     */
    private fun setBg(seckill: SeckillModule) {
        iv_seckill_bg.layoutParams = iv_seckill_bg.layoutParams.apply {
            height = measuredHeight
        }
        val isShowBg = (TextUtils.isEmpty(seckill.content?.bgUrl).not() || TextUtils.isEmpty(seckill.content?.bgColor).not())
        cl_title.setBackgroundResource(if (isShowBg) R.drawable.shape_transparent else R.drawable.shape_seckill_title)
        rv_seckill.setBackgroundResource(if (isShowBg) R.drawable.shape_transparent else R.drawable.shape_white)
        v_bottom_corner.setBackgroundResource(if (isShowBg) R.drawable.shape_transparent else R.drawable.shape_seckill_foot)
    }

    /**
     * 设置状态
     */
    fun setStatus(seckill: SeckillInfo?) = seckill.also {
        it?.apply {
            tvTimeTitle.text = getSeckillName(this)
            tvTimeTitle.setTextColor(ContextCompat.getColor(context, R.color.white))
            when (status) {
                SECKILL_STATUS_WAIT -> {
                    //即将开始
                    llTime.background = ContextCompat.getDrawable(context, R.drawable.shape_home_time_green_parent)
                    tvTimeTitle.background = ContextCompat.getDrawable(context, R.drawable.shape_home_time_green)
                    tvTimeRemains.setTextColor(ContextCompat.getColor(context, R.color.color_00b377))
                    it.time?.also { time ->
                        tvTimeRemains.text = "${time}后开始"
                    }
                }
                SECKILL_STATUS_END -> {
                    //已结束
                    llTime.background = ContextCompat.getDrawable(context, R.drawable.shape_home_time_gray_parent)
                    tvTimeTitle.background = ContextCompat.getDrawable(context, R.drawable.shape_home_time_gray)
                    tvTimeRemains.setTextColor(ContextCompat.getColor(context, R.color.color_9494A6))
                    tvTimeRemains.text = "已结束"
                }
                SECKILL_STATUS_HAVE_IN_HAND -> {
                    //进行中
                    llTime.background = ContextCompat.getDrawable(context, R.drawable.shape_home_time_red_parent)
                    tvTimeTitle.background = ContextCompat.getDrawable(context, R.drawable.shape_home_time_red)
                    tvTimeRemains.setTextColor(ContextCompat.getColor(context, R.color.color_ff2121))
                    it.time?.also { time ->
                        tvTimeRemains.text = "${time}后结束"
                    }
                }
            }
        }
    }

    /**
     * 获取xx点场次
     */
    @SuppressLint("SimpleDateFormat")
    fun getSeckillName(seckill: SeckillInfo?): String? = seckill?.let {
        val cal = Calendar.getInstance()
        cal.time = Date(it.startDate)
        val hours = SimpleDateFormat("HH").format(Date(it.startDate))
        return "${hours}点场"
    }

    fun setshoppingGuideData(seckill: SeckillModule) {
        intervalListener?.also(IntervalUtil::unRegisterInterval)
        handleCountDown(seckill.content)
    }

    /**
     * 处理倒计时
     */
    private fun handleCountDown(seckillInfo: SeckillInfo?) {
        seckillInfo?.also {
            intervalListener = object : IntervalListener {
                override fun callback() {
                    if (it.status == SECKILL_STATUS_WAIT) {
                        it.currentDate += 1000
                        if (it.currentDate >= it.startDate) {
                            it.status = SECKILL_STATUS_HAVE_IN_HAND
                        } else {
                            it.time = timeFormat(it.startDate - it.currentDate)
                        }
                    } else if (it.status == SECKILL_STATUS_HAVE_IN_HAND) {
                        it.currentDate += 1000
                        if (it.currentDate >= it.endDate) {
                            it.status = SECKILL_STATUS_END
                            it.time = timeFormat(it.endDate - it.currentDate)
                            if (intervalListener != null) {
                                IntervalUtil.unRegisterInterval(intervalListener!!)
                            }
                        } else {
                            it.time = timeFormat(it.endDate - it.currentDate)
                        }
                    }
                    if (preStatus != seckillInfo.status) {
                        seckillInfo.list?.forEach { item ->
                            item.status = seckillInfo.status
                        }
                        adapter?.notifyDataSetChanged()
                    }
                    setStatus(seckillInfo)
                }
            }
            IntervalUtil.registerInterval(intervalListener!!)
        }
    }

    /**
     * 时间格式化
     */
    private fun timeFormat(millisUntilFinished: Long): String {
        val h = millisUntilFinished / 1000 / 3600
        val fen = millisUntilFinished / 1000 % 3600 / 60
        val s = millisUntilFinished / 1000 % 60
        val hh = if (h < 10) {
            "0$h"
        } else {
            "" + h
        }
        val ff = if (fen < 10) {
            "0$fen"
        } else {
            "" + fen
        }
        val ss = if (s < 10) {
            "0$s"
        } else {
            "" + s
        }
        return "$hh:$ff:$ss"
    }

    /**
     * 埋点回调
     */
    fun setAnalysisCallback(callback: ISeckillAnalysisCallback) {
        analysisCallback = callback
    }

}