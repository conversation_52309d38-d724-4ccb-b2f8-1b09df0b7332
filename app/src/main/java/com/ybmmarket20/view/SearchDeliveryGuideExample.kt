package com.ybmmarket20.view

import android.app.Activity
import android.widget.RadioGroup
import com.ybmmarket20.utils.SearchGuideManager

/**
 * 搜索配送时效引导组件使用示例
 * 
 * 使用方法：
 * 1. 在搜索页面的Activity中初始化引导组件
 * 2. 在搜索结果加载完成后调用检查方法
 * 3. 引导组件会自动判断是否需要显示引导
 */
object SearchDeliveryGuideExample {
    
    /**
     * 在Activity中使用引导组件的示例
     */
    fun exampleUsage(activity: Activity, rootLayout: android.view.ViewGroup, targetRadioGroup: RadioGroup) {
        // 1. 创建引导组件
        val deliveryGuideView = SearchDeliveryGuideView(activity)
        
        // 2. 添加到根布局
        rootLayout.addView(deliveryGuideView)
        
        // 3. 设置点击监听
        deliveryGuideView.setOnGuideClickListener {
            // 标记引导已显示
            SearchGuideManager.markDeliveryGuideShown(activity)
            // 可以添加埋点等其他逻辑
            println("用户点击了配送时效引导")
        }
        
        // 4. 在搜索结果加载完成后检查是否显示引导
        checkAndShowGuide(activity, deliveryGuideView, targetRadioGroup)
    }
    
    /**
     * 检查并显示引导的示例方法
     */
    private fun checkAndShowGuide(
        activity: Activity, 
        guideView: SearchDeliveryGuideView, 
        targetRadioGroup: RadioGroup
    ) {
        // 检查是否有配送时效选项（这里假设有）
        val hasDeliveryOptions = true
        
        // 检查是否应该显示引导
        if (SearchGuideManager.shouldShowDeliveryGuide(activity, hasDeliveryOptions)) {
            // 延迟显示，确保布局完成
            targetRadioGroup.post {
                guideView.showGuide(targetRadioGroup)
            }
        }
    }
    
    /**
     * 重置引导状态（用于测试）
     */
    fun resetGuideState(activity: Activity) {
        SearchGuideManager.resetDeliveryGuideState(activity)
    }
}

/**
 * 使用说明：
 * 
 * 1. 引导组件特点：
 *    - 半透明黑色蒙层背景
 *    - 手势图片在目标组件居中显示
 *    - "我知道了"按钮在手势图片下方
 *    - 点击任意位置可关闭引导
 * 
 * 2. 显示条件：
 *    - 搜索结果包含配送时效选项
 *    - 用户之前没有看过此引导
 * 
 * 3. 图片资源：
 *    - mask_hands.png: 手势指引图片
 *    - mask_iknow.png: "我知道了"按钮图片
 * 
 * 4. 状态管理：
 *    - 使用SharedPreferences保存引导显示状态
 *    - 每个用户只显示一次
 *    - 提供重置方法用于测试
 */
