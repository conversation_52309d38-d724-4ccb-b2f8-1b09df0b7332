package com.ybmmarket20.bean;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import com.ybmmarket20.adapter.LicensePicListAdapter;

import java.util.ArrayList;
import java.util.List;

public class LicenceBean implements Parcelable {
    public String id;
    public String categoryCode;//资质的分类
    public String name;
    public long validateTime;
    public int status;//1.已提交，2.未提交
    public int savedStatus;         //标识资质状态 0：正常 1：过期 2：临期
    public String templateUrl;         //示例图片 ,新增字段

    public String statusName;
    public int isRequired;              //1.是必须，2.是非必须
    public String xyyEntrusCode;        //小药药委托书编号
    public long xyyEntrusValidateTime;  //小药药委托书有效期
    //图片链接
    @Deprecated
    public String licenseImgUrls;
    public List<String> listImgUrls;    // 回显图片

    public transient LicensePicListAdapter adapter;


    protected LicenceBean(Parcel in) {
        id = in.readString();
        categoryCode = in.readString();
        name = in.readString();
        validateTime = in.readLong();
        status = in.readInt();
        savedStatus = in.readInt();
        templateUrl = in.readString();
        statusName = in.readString();
        isRequired = in.readInt();
        xyyEntrusCode = in.readString();
        xyyEntrusValidateTime = in.readLong();
        licenseImgUrls = in.readString();
        listImgUrls = in.createStringArrayList();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(id);
        dest.writeString(categoryCode);
        dest.writeString(name);
        dest.writeLong(validateTime);
        dest.writeInt(status);
        dest.writeInt(savedStatus);
        dest.writeString(templateUrl);
        dest.writeString(statusName);
        dest.writeInt(isRequired);
        dest.writeString(xyyEntrusCode);
        dest.writeLong(xyyEntrusValidateTime);
        dest.writeString(licenseImgUrls);
        dest.writeStringList(listImgUrls);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<LicenceBean> CREATOR = new Creator<LicenceBean>() {
        @Override
        public LicenceBean createFromParcel(Parcel in) {
            return new LicenceBean(in);
        }

        @Override
        public LicenceBean[] newArray(int size) {
            return new LicenceBean[size];
        }
    };

    public List<LicensePicListAdapter.ImageInfo> fetchImageUrlList() {
        List<LicensePicListAdapter.ImageInfo> imageList = new ArrayList<>();
        if (listImgUrls != null){
            for (String url:listImgUrls){
                LicensePicListAdapter.ImageInfo imageInfo = new LicensePicListAdapter.ImageInfo();
                imageInfo.oldPath = url;
                imageList.add(imageInfo);
            }
        }
        return imageList;


        /*if (imageInfoList == null) {
            imageInfoList = new ArrayList<>();
            try {
                String[] split = licenseImgUrls.split(",");
                for (String s : split) {
                    if (!TextUtils.isEmpty(s)) {
                        LicensePicListAdapter.ImageInfo imageInfo = new LicensePicListAdapter.ImageInfo();
                        imageInfo.oldPath = s;
                        imageInfoList.add(imageInfo);
                    }
                }
                return imageInfoList;
            } catch (Exception exception) {
                return imageInfoList = new ArrayList<>();
            }
        } else {
            return imageInfoList;
        }*/


    }


    public LicenceBean(String code, String name, int required, String templateUrl,int savedStatus,List<String> urlList) {
        this.categoryCode = code;
        this.name = name;
        this.templateUrl = templateUrl;
        this.isRequired = required;
        this.savedStatus = savedStatus;
        this.listImgUrls = urlList;
    }



}
