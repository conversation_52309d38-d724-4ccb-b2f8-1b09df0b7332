package com.ybmmarket20.bean

import java.io.Serializable

/*
 * Created by sean on 2019-08-06
 */
data class TradingSnapshotDetail(
    val agent: String?,
    val approvalNumber: String?,
    val balanceFlag: String?,
    val balancePercent: String?,
    val branchCode: String?,
    val createTime: Long,
    val creator: String?,
    val cxTagList: List<CxTag>?,
    val cxTagStr: String?,
    val description: String?,
    val dosageForm: String?,
    val drugClassification: String?,
    val drugClassificationImage: String?,
    val drugClassificationText: String?,
    val farEffect: String?,
    val id: String?,
    val imageUrl: String?,
    val imagesList: List<String?>?,
    val imagesVideosList: List<ImagesVideos>?,
    val indication: String?,
    val isSplit: String?,
    val isThirdCompany: String?,
    val isUsableMedical: String?,
    val manufacturer: String?,
    val mediumPackageNum: String?,
    val nearEffect: String?,
    val orderNo: String?,
    val packageCount: String?,
    val packageId: String?,
    val pieceLoading: String?,
    val preferentialAmount: String?,
    val preferentialCount: String?,
    val productAmount: String?,
    val productName: String?,
    val productPrice: Double,
    val productUnit: String?,
    val shelfLife: String?,
    val shelfLifeText: String?,
    val showName: String?,
    val skuId: String?,
    val spec: String?,
    /**
     * 是否有追溯码 0-否，1-是
     */
    val tracingCode: String?,
    val storageCondition: String?,
    val subTotal: Double,
    val tagJsonStr: String?,
    val untowardEffect: String?,
    val usageAndDosage: String?,
    val videoUrl: String?,
    var activityType //提示语优化
    : Int = 0,
    var nearEffectiveFlag //近效期标识(1:临期，2:近效)
    : Int = 0,
    var categoryFirstId: String?
) : Serializable {
    fun isActivityType(): Boolean {
        return activityType == 99
    }

    fun isNearEffectiveFlag(): Boolean {
        return nearEffectiveFlag == 1 || nearEffectiveFlag == 2
    }
}

data class ImagesVideos(
    val imageUrl: String?,
    val type: String?,
    var videoUrl: String?
) : Serializable

data class CxTag(
    val appURL: String?,
    val description: String?,
    val isNewTab: String?,
    val name: String?,
    val pcURL: String?,
    val sort: String?,
    val uiType: String?
) : Serializable