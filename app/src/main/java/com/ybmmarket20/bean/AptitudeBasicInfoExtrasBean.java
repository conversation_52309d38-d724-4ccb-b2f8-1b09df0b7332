package com.ybmmarket20.bean;

import android.os.Parcelable;

import java.io.Serializable;

public class AptitudeBasicInfoExtrasBean implements Serializable {
    private String companyName;
    private String companyType;
    private String detailAddress;
    private String province;
    private String city;
    private String district;
    private String street;
    private String provinceId;
    private String cityId;
    private String districtId;
    private String streetId;
    public int invoiceType;

    public AptitudeBasicInfoExtrasBean(String companyName, String companyType, String detailAddress,
                                       String province, String city, String district, String street,
                                       String provinceId, String cityId, String districtId, String streetId) {

        this.companyName = companyName;
        this.companyType = companyType;
        this.detailAddress = detailAddress;
        this.province = province;
        this.city = city;
        this.district = district;
        this.street = street;
        this.provinceId = provinceId;
        this.cityId = cityId;
        this.districtId = districtId;
        this.streetId = streetId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(String provinceId) {
        this.provinceId = provinceId;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getDistrictId() {
        return districtId;
    }

    public void setDistrictId(String districtId) {
        this.districtId = districtId;
    }

    public String getStreetId() {
        return streetId;
    }

    public void setStreetId(String streetId) {
        this.streetId = streetId;
    }
}
