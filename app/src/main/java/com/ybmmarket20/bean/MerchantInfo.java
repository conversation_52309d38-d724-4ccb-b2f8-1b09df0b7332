package com.ybmmarket20.bean;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Created by wh on 2018/1/16.
 */

public class MerchantInfo {

    public AccountInfo accountInfo;
    public BaseInfo baseInfo;
    public List<String> tagList;
    public String notice;
    @Deprecated
    public String orderEscortSwitch;//1显示，0不显示
    @Deprecated
    public String inviteSwitch;             //1开 0关
    public int licenseDownStatus;           //小药药资质查看  0无权限下载  1有权限下载
    @Deprecated
    public String assignment;               //是否显示任务中心 1 显示 0不显示
    @Deprecated
    public String naturePersonSwitch; // 控销商城开关，目前无用
    @Deprecated
    public String naturePersonUrl;    // 控销商城 跳转链接

    public String totalQuota;       //总限额
    public int enableMyFinance;     // 白条区域控制字段 1：展示，0：关闭
    public int isBlack;             // 1 白条的白名单用户
    public int iousIsOpen;          //是否湖北域 1 白条开通  0 白条未开通
    public String creditExtensionUrl;//小药白条h5地址
    public boolean isKa;//是否ka用户
    public int validity;            // 资质提醒 0：正常，1：过期，2：临期


    public static class AccountInfo {
        public int signPointCount;
        public int wishListCount;
        public int voucherCount;
        public String balance;
        public int unReadNum;
        public String redPacketBalance; //红包金额
        public int virtualGoldShowFlag;//购物金1显示;0隐藏
        public String virtualGold;//购物金
        public String pingAnCredBalance;//可用余额
        public int pingAnCredShowFlag;//1显示0不显示

    }

    public static class BaseInfo {
        public String id;
        public String realName;
        public String mobile;
        public String password;
        public String email;
        public String nickname;
        public String businessType;
        public String businessTypeName;
        public String address;
        public String province;         // 省名
        public String provinceCode;     // 省code
        public String city;             // 市名
        public String cityCode;         // 市code
        public String district;         // 区名
        @SerializedName("districtCode")
        public String areaCode;         // 区code
        public long createTime;       // 注册时间
        public String registerCode;
        public String lastLoginTime;
        public int status;
        public String channelNames;      //用户当前所属渠道
    }
}
