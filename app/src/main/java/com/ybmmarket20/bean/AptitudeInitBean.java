package com.ybmmarket20.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class AptitudeInitBean {
    public List<ImageItem> licenseAuditImgList;
    public List<Licence> necessaryLicenceList;
    public List<Licence> optionalLicenceList;
    public LicenceItem licenseAudit;
    public String tempRemark;


    public static class ImageItem {
        @Deprecated
        public String licenseImgUrls;
        public String xyyEntrusCode;            //小药药委托书编号
        public long xyyEntrusValidateTime;      //小药药委托书有效期
        //7.11 资质优化 新加变更字段 取新字段
        public String credentialName;           //分类条目 医疗机构执业许可证
        public String licenseCode;              //条目英文编码


    }

    public String getRemark() {
        if (licenseAudit == null) {
            return "";
        } else {
            return licenseAudit.remark;
        }
    }

    public static class LicenceItem {
        //public int id;
        public int audit1Status;
        public String remark;
        //7.11 资质优化 新加变更字段 取新字段
        public String applicationNumber;        // -1 表示添加资质，其他值表示更新资质
    }

    public static class Licence implements Parcelable {
        public String name;
        public String code;
        public int isRequired;
        public int status;                  //标识资质状态:    0：正常 1：过期 2：临期
        public String templateUrl;          //示例图片
        public List<String> listImgUrls;    // 回显图片


        protected Licence(Parcel in) {
            name = in.readString();
            code = in.readString();
            isRequired = in.readInt();
            status = in.readInt();
            templateUrl = in.readString();
            listImgUrls = in.createStringArrayList();
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(name);
            dest.writeString(code);
            dest.writeInt(isRequired);
            dest.writeInt(status);
            dest.writeString(templateUrl);
            dest.writeStringList(listImgUrls);
        }

        @Override
        public int describeContents() {
            return 0;
        }

        public static final Creator<Licence> CREATOR = new Creator<Licence>() {
            @Override
            public Licence createFromParcel(Parcel in) {
                return new Licence(in);
            }

            @Override
            public Licence[] newArray(int size) {
                return new Licence[size];
            }
        };
    }
}
