package com.ybmmarket20.bean

data class PayNongData(
    val h5Url: String? = "",
    val thirdApplyNo: String? = ""
)

data class PayNongCheckData(
    //核验结果  true 通过，false会返回YBM单号
    val checkResult: Boolean,
    //核验状态 0-核验通过可下单     1-存在已核验通过未支付支付单   2-当前订单存在已核验通过未支付支付单
    val checkStatus: String? = "",
    //农行h5 url
    val abchinaLoanAppDirectUrl: String? = "",
    val orderNo: String? = "",
    //金额
    val amount: String?=""
)

