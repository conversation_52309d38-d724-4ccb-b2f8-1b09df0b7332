package com.ybmmarket20.bean

/**
 * 下载记录
 */
data class DownloadRecordBean(
    //订单号
    val orderNo: String?,
    //卖家ID
    val merchantId: String?,
    //商业ID
    val ordorgIderNo: String?,
    //创建时间
    val createTime: Long,
    //邮箱
    val email: String?,
    //失败原因
    val failReason: String?,
    //下载地址
    val downUrl: String?,
    //资质类型编码
    val ordqualificationTypeerNo: String?,
    //资质类型文案
    val qualificationTypeStr: String?,
    //下载状态
    val downloadStatus: Int
)