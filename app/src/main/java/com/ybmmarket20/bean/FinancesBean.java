package com.ybmmarket20.bean;

public class FinancesBean {

    /**
     * agreeAuthorize : 0
     * applyNum : 0
     * applyStatus : 3
     * branchCode : XS420000
     * businessType : 1
     * createTime : 1586316809000
     * creator : test
     * creditAvailable : 0.0
     * creditLine : 1000.0
     * creditUsed : 0.0
     * financeCode : gfbt
     * id : 1
     * merchantId : 110217
     * mobile : ***********
     * overdue : 0
     * realName : realName
     * updateTime : 1586316809000
     * updater : test
     */

    private int agreeAuthorize;//0未授权,1已授权,-1拒绝授权
    private int applyNum;//申请次数
    private int applyStatus;//申请状态 1未申请,2审核中,3审核通过,4审核驳回
    private String branchCode;//地址编码
    private int businessType;//客户类型
    private long createTime;//创建时间
    private String creator;//创建人
    private double creditAvailable;//可用额度
    private double creditLine;//额度
    private double creditUsed;//已用额度
    private String financeCode;//金融产品编码
    private int id;
    private int merchantId;//用户编码
    private String mobile;//客户手机号
    private int overdue;//0未逾期,1逾期
    private String realName;//客户名称
    private long updateTime;//修改时间
    private String updater;//修改人
    private String financeName;//广发白条名称

    public int getAgreeAuthorize() {
        return agreeAuthorize;
    }

    public void setAgreeAuthorize(int agreeAuthorize) {
        this.agreeAuthorize = agreeAuthorize;
    }

    public int getApplyNum() {
        return applyNum;
    }

    public void setApplyNum(int applyNum) {
        this.applyNum = applyNum;
    }

    public int getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(int applyStatus) {
        this.applyStatus = applyStatus;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public int getBusinessType() {
        return businessType;
    }

    public void setBusinessType(int businessType) {
        this.businessType = businessType;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public double getCreditAvailable() {
        return creditAvailable;
    }

    public void setCreditAvailable(double creditAvailable) {
        this.creditAvailable = creditAvailable;
    }

    public double getCreditLine() {
        return creditLine;
    }

    public void setCreditLine(double creditLine) {
        this.creditLine = creditLine;
    }

    public double getCreditUsed() {
        return creditUsed;
    }

    public void setCreditUsed(double creditUsed) {
        this.creditUsed = creditUsed;
    }

    public String getFinanceCode() {
        return financeCode;
    }

    public void setFinanceCode(String financeCode) {
        this.financeCode = financeCode;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(int merchantId) {
        this.merchantId = merchantId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public int getOverdue() {
        return overdue;
    }

    public void setOverdue(int overdue) {
        this.overdue = overdue;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public String getFinanceName() {
        return financeName;
    }

    public void setFinanceName(String financeName) {
        this.financeName = financeName;
    }
}
