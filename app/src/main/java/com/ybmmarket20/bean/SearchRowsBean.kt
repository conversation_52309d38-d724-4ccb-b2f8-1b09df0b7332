package com.ybmmarket20.bean

import com.ybm.app.bean.AbstractMutiItemEntity

/**
 * 大搜数据结构(带运营位)
 *
 * 查询赠品池复用这个数据结构
 */
data class SearchRowsBean(
    val cardType: Int, //卡片类型。1商品，2运营位,3组合购，4加价购。
    var productInfo: RowsBean?, //商品信息
    val operationInfo: OperationPositionInfo?, //运营位信息
    var keyword: String? = "",
    var outPosition:Int = 0,
    var needLimitFullDiscountActInfo: Boolean? = false,
    var groupPurchaseInfo: GroupPurchaseInfo? = null,
    var additionalPurchaseInfo:GroupPurchaseInfo? = null
): AbstractMutiItemEntity() {

    override fun getItemType(): Int {
        return when {
//            (productInfo?.limitFullDiscountActInfo != null) && needLimitFullDiscountActInfo == true -> SEARCH_SPELL_GROUP_LIMIT_TIME_PREMIUM_GOODS //限时补价
            cardType == SEARCH_LIST_CARD_TYPE_GOODS -> SEARCH_LIST_CARD_TYPE_GOODS //单品使用商品样式
            cardType == SEARCH_LIST_CARD_TYPE_OPERATION_POSITION && operationInfo?.showType == OPERATION_POSITION_TYPE_SINGLE_GOODS -> SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS
            cardType == SEARCH_LIST_CARD_TYPE_COMBINATIONBUY_SINGLE ->  SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_SINGLE
            cardType == SEARCH_LIST_CARD_TYPE_COMBINATIONBUY_MULTI -> SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_MULTI
            else -> SEARCH_LIST_CARD_TYPE_OPERATION_POSITION
        }
    }

    //是否是运营位
    fun isOperation(): Boolean = cardType == SEARCH_LIST_CARD_TYPE_OPERATION_POSITION || cardType == SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS
}

//搜索卡片类型-商品
const val SEARCH_LIST_CARD_TYPE_GOODS = 1
//搜索卡片类型-运营位
const val SEARCH_LIST_CARD_TYPE_OPERATION_POSITION = 2
//搜索卡片类型-运营位（单品）
const val SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS = 3
//搜索限时加补拼团品
const val SEARCH_SPELL_GROUP_LIMIT_TIME_PREMIUM_GOODS = 4
//组合购
const val SEARCH_LIST_CARD_TYPE_COMBINATIONBUY_SINGLE = 3
const val SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_SINGLE = 5
//组合购：多选
const val SEARCH_LIST_CARD_TYPE_COMBINATIONBUY_MULTI = 4
const val SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_MULTI = 6