package com.ybmmarket20.bean;

import com.google.gson.annotations.SerializedName;
import com.ybmmarket20.xyyreport.spm.ScmBean;
import com.ybmmarket20.xyyreport.spm.SpmBean;
import com.ybmmarket20.xyyreport.spm.TrackData;

import java.io.Serializable;

/**
 * Created by mbdn on 2017/8/10.
 * 广告
 */

public class AdDataBean implements Serializable {
    public String startImage = "";//广告图片 地址
    public String startImageOther = "";//广告图片 地址
    public String jumpUrl = "";//广告图片点击 地址
    public boolean isdisplay;//广告是否显示
    public boolean isNewTheme;//是否使用本地的新主题
    public String sptype; //来源类型
    public String jgspid;
    public String sid;
    public String pageType;
    public String pageId;
    public String pageName;
    public String componentPosition;
    public String componentName;
    public String componentTitle;
    public TrackData trackData;
}
