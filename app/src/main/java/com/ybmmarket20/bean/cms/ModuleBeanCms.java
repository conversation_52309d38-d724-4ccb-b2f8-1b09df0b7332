package com.ybmmarket20.bean.cms;

import java.util.List;

public class ModuleBeanCms<T> {

    public static final String SEARCHBOX = "searchBox";                 //首页搜索框
    //public static final String BANNER = "banner";                       //轮播图
    public static final String FASTENTRY = "fastEntry";                 //快捷入口
    public static final String HEADLINE = "headline";                   //药头条
    public static final String PRODUCTEXHIBITION = "productExhibition"; //单品展示模块
    public static final String RECOMMENDLIST = "recommendList";         //集合推荐
    public static final String BRAND_H = "brand-h";                     //黄金广告位
    public static final String ADONE = "adOne";                         //首页一图
    public static final String ADTWO = "adTwo";                         //首页二图
    public static final String ADTHREE = "adThree";                     //首页三图
    public static final String ADFOUR = "adFour";                       //首页四图
    public static final String ONEPLUSTWO = "onePlusTwo";               //首页1+2布局
    public static final String ONEPLUSTHREE = "onePlusThree";           //首页1+3布局
    public static final String ONEPLUSFOUR = "onePlusFour";             //首页1+4布局
    public static final String TWOPLUSTWO = "twoPlusTwo";               //首页2+2布局
    public static final String STREAMER = "streamer";                   //横幅广告
    public static final String MOREACTIVE = "moreActive";               //文字标题
    public static final String WONDERACTIVE = "wonderActive";           //图片标题
    public static final String FLOORSPACING = "floorSpacing";           //楼层间隔


    public static final String SECKILL = "seckill";//秒杀
    public static final String COLUMNPRODUCT = "columnProduct";           //竖向商品列表

    public static final String HOMETAB = "homeTab";                     // 选项卡
    public static final String TABBAR = "tabbar";                       //底部导航

    //-------------new---------------
    public String name;//模块标识，searchBox
    public ModuleStyles styles;//样式 上下排列 圆角
    public String title;//模块唯二标识，首页搜索框
    public ModuleContent<T> content;//模块显示内容，图片地址，颜色，背景颜色等

    @Deprecated
    public String titleText;//标题
    public String titleRes;// 背景色 #777777 或者 图片url,默认白色
    public List<Integer> titleMargin;// [left,top,rigth,bottom] 外边距 单位dp       | int型数组

    @Override
    public boolean equals(Object o) {

        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ModuleBeanCms that = (ModuleBeanCms) o;

        if (name == null) {
            name = "";
        }
        if (that.name == null) {
            that.name = "";
        }
        if (title == null) {
            title = "";
        }
        if (that.title == null) {
            that.title = "";
        }

        return name.equals(that.name) && title.equals(that.title) ;

    }

    @Override
    public int hashCode() {

        int result = name != null ? name.hashCode() : 0;
        result = 31 * result + (title != null ? title.hashCode() : 0);
        return result;
    }

    public boolean isEquals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        ModuleBeanCms that = (ModuleBeanCms) o;
        if (name == null) {
            name = "";
        }
        if (that.name == null) {
            that.name = "";
        }
        if (title == null) {
            title = "";
        }
        if (that.title == null) {
            that.title = "";
        }

        boolean equalsStyle = false;
        if ((styles == null && that.styles == null) || (styles != null && styles.equals(that.styles))) {
            equalsStyle = true;
        }

        return name.equals(that.name) && title.equals(that.title) && equalsStyle ;
    }

}