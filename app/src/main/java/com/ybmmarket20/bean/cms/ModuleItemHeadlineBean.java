package com.ybmmarket20.bean.cms;

/*
 * 药头条
 * */
public class ModuleItemHeadlineBean {

    public String color;//颜色

    public String getContent() {
        if (content == null) {
            content = "";
        }
        return content;
    }

    public String content;//轮播内容
    public String url;//点击事件

    // 这个是废弃了么？
    public String imgUrl;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ModuleItemHeadlineBean item = (ModuleItemHeadlineBean) o;

        if (content != null ? !content.equals(item.content) : item.content != null) return false;
        return url != null ? url.equals(item.url) : item.url == null;

    }

    @Override
    public int hashCode() {
        int result = url != null ? url.hashCode() : 0;
        result = 31 * result + (content != null ? content.hashCode() : 0);
        return result;
    }
}
