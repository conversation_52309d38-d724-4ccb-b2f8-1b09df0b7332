package com.ybmmarket20.bean

/**
 * <AUTHOR>
 * @desc    组合购
 * @date 2025/5/7
 * @updateDate 2025/5/9
 */
data class GroupPurchaseInfo(
    var combinedDiscountSub: Double? = 0.0, // 订单返回：总组合优惠
    var realPay: String? = "",  // 订单返回：总到手价
    var tips: String = "",// 订单返回：已选3种50盒
    var mainProduct: RowsBeanCombinedExt,  // 大搜返回：主品商品
    var subProducts: MutableList<RowsBeanCombinedExt>,  // 大搜返回：副品商品列表
    var combinedList:MutableList<RowsBeanCombinedExt>?=null,   // 订单返回:商品列表
    var errMsg:String? = null
)

/**
 * 初始化组合购数据：首次
 */
fun GroupPurchaseInfo.initGroup(){
    for (subProduct in this.subProducts) {
        if (subProduct.actPt == null) {
            subProduct.qty = 0
        } else {
            subProduct.qty = subProduct.actPt.skuStartNum
        }
        subProduct.selectStatus = 1
    }
    this.mainProduct.selectStatus = 1
    if (this.mainProduct.actPt == null) {
        this.mainProduct.qty = 0
    } else {
        this.mainProduct.qty = this.mainProduct.actPt.skuStartNum
    }
}

/**
 * 初始化加价购数据：首次
 */
fun GroupPurchaseInfo.initAdditonal(){
    for (subProduct in this.subProducts) {
        if (subProduct.actPt == null) {
            subProduct.qty = 0
        } else {
            subProduct.qty = subProduct.actPt.skuStartNum
        }
    }
}