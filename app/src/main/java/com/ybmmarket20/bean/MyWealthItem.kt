package com.ybmmarket20.bean

import com.google.gson.annotations.SerializedName

data class MyWealthItem(
    val title: String?,
    val tips: String?,
    //0-额度未申请成功;1-额度已申请成功, 已申请成功的 给tips字段不加背景色，没申请的 加背景色
    @Deprecated(message="V12.0.06 delete pingAnCreditState",replaceWith = ReplaceWith("state"))
    val pingAnCreditState: Int,
    //农行复用是json字符串需要去解析
    val reqUrl: String? = "",
    @SerializedName("itemType")
    val wealthItemType: Int,
    // 0-待申请;1-未提交;2-提交否决;3-审批中;4-审批否决；5-审批通过;6-建额失败;7-已签署合同	 8-失效;9-过期;10-冻结
    val state:Int,
    //平安商户开通状态 1 已开通 若当前客户平安商户状态不是“开户成功”，则toast提示“请先到平安商户页面进行开户、绑卡，开户成功后可重试”
    val accountState:Int,
)

data class ReqUrlJsonBean(
    val androidScheme: String? = "",
    val abchinaDirectUrl: String? = ""
)
