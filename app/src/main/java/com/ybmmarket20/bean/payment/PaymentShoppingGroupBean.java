package com.ybmmarket20.bean.payment;


import java.util.List;

public class PaymentShoppingGroupBean {

    private int valid;
    private List<PaymentSortedBean> sorted;//购物车实体类
    private boolean showChannelCode;
    private int selectStatus;// 非自营状态:0-未选中，1-选中
    private String orgId;//自营或非自营勾选的id
    private int isThirdCompany;//是否是自营（0：是；1：否）
    private String companyName;//自营或者非自营name
    private String channelCode;//渠道编码：默认为 1; 药帮忙 1; 2 宜块钱
    private int productVarietyNum;//共几件
    private int ykqProductVarietyNum;//共几件-宜块钱
    private int b2bProductVarietyNum;//b2b商品品种
    private double freightAmount;//运费
    private double totalAmount;//合计
    private double ykqTotalAmount;//合计-宜块钱
    private double b2bTotalAmount;//b2b已购总金额
    private int isThirdCompanyLastFlag;// isThirdCompanyLastFlag=1自营最后一个商品
    private double promoDiscountAmount;//优惠满减
    private double voucherDiscountAmount;//优惠优惠券
    private String returnBalanceTips;//领取余额文案
    private double rebate = 0.00;//返点
    private double balanceAmount = 0.00;//预返余额明细

    /*-----------------old-----------------*/
    private int type;//(1, "满减"),(2, "满折"),(3, "满赠"),(4, "满减赠"),9-不参与活动，10-套餐，5:大礼包 **/
    private int id;//大礼包id
    private int directSelectStatus;//自营是否全选。首个grop下状态:0-未选中，1-选中

    private int giftPoolActTotalSelectedNum;  // 赠品池活动可选总数量
    private int giftPoolActHasSelectedNum;  // 赠品池活动已选数量
    private int giftPoolActCanSelectedNum;  // 赠品池活动待可选数量   giftPoolActTotalSelectedNum-giftPoolActHasSelectedNum
    private String promoId;      //满赠类:活动唯一:，拉起赠品池列表接口用
    private boolean canGoToGiftPool;      //是否可以去赠品池选赠品
    private boolean isGiveUpGift;   //是否选中   放弃赠品

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public List<PaymentSortedBean> getSorted() {
        return sorted;
    }

    public void setSorted(List<PaymentSortedBean> sorted) {
        this.sorted = sorted;
    }

    public boolean isShowChannelCode() {
        return showChannelCode;
    }

    public void setShowChannelCode(boolean showChannelCode) {
        this.showChannelCode = showChannelCode;
    }

    public int getSelectStatus() {
        return selectStatus;
    }

    public void setSelectStatus(int selectStatus) {
        this.selectStatus = selectStatus;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public int getIsThirdCompany() {
        return isThirdCompany;
    }

    public void setIsThirdCompany(int isThirdCompany) {
        this.isThirdCompany = isThirdCompany;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public int getProductVarietyNum() {
        return productVarietyNum;
    }

    public void setProductVarietyNum(int productVarietyNum) {
        this.productVarietyNum = productVarietyNum;
    }

    public int getYkqProductVarietyNum() {
        return ykqProductVarietyNum;
    }

    public void setYkqProductVarietyNum(int ykqProductVarietyNum) {
        this.ykqProductVarietyNum = ykqProductVarietyNum;
    }

    public int getB2bProductVarietyNum() {
        return b2bProductVarietyNum;
    }

    public void setB2bProductVarietyNum(int b2bProductVarietyNum) {
        this.b2bProductVarietyNum = b2bProductVarietyNum;
    }

    public double getFreightAmount() {
        return freightAmount;
    }

    public void setFreightAmount(double freightAmount) {
        this.freightAmount = freightAmount;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public double getYkqTotalAmount() {
        return ykqTotalAmount;
    }

    public void setYkqTotalAmount(double ykqTotalAmount) {
        this.ykqTotalAmount = ykqTotalAmount;
    }

    public double getB2bTotalAmount() {
        return b2bTotalAmount;
    }

    public void setB2bTotalAmount(double b2bTotalAmount) {
        this.b2bTotalAmount = b2bTotalAmount;
    }

    public int getIsThirdCompanyLastFlag() {
        return isThirdCompanyLastFlag;
    }

    public void setIsThirdCompanyLastFlag(int isThirdCompanyLastFlag) {
        this.isThirdCompanyLastFlag = isThirdCompanyLastFlag;
    }

    public double getPromoDiscountAmount() {
        return promoDiscountAmount;
    }

    public void setPromoDiscountAmount(double promoDiscountAmount) {
        this.promoDiscountAmount = promoDiscountAmount;
    }

    public double getVoucherDiscountAmount() {
        return voucherDiscountAmount;
    }

    public void setVoucherDiscountAmount(double voucherDiscountAmount) {
        this.voucherDiscountAmount = voucherDiscountAmount;
    }

    public String getReturnBalanceTips() {
        return returnBalanceTips;
    }

    public void setReturnBalanceTips(String returnBalanceTips) {
        this.returnBalanceTips = returnBalanceTips;
    }

    public double getRebate() {
        return rebate;
    }

    public void setRebate(double rebate) {
        this.rebate = rebate;
    }

    public double getBalanceAmount() {
        return balanceAmount;
    }

    public void setBalanceAmount(double balanceAmount) {
        this.balanceAmount = balanceAmount;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getDirectSelectStatus() {
        return directSelectStatus;
    }

    public void setDirectSelectStatus(int directSelectStatus) {
        this.directSelectStatus = directSelectStatus;
    }

    public int getGiftPoolActTotalSelectedNum() {
        return giftPoolActTotalSelectedNum;
    }

    public void setGiftPoolActTotalSelectedNum(int giftPoolActTotalSelectedNum) {
        this.giftPoolActTotalSelectedNum = giftPoolActTotalSelectedNum;
    }

    public int getGiftPoolActHasSelectedNum() {
        return giftPoolActHasSelectedNum;
    }

    public void setGiftPoolActHasSelectedNum(int giftPoolActHasSelectedNum) {
        this.giftPoolActHasSelectedNum = giftPoolActHasSelectedNum;
    }

    public int getGiftPoolActCanSelectedNum() {
        return giftPoolActCanSelectedNum;
    }

    public void setGiftPoolActCanSelectedNum(int giftPoolActCanSelectedNum) {
        this.giftPoolActCanSelectedNum = giftPoolActCanSelectedNum;
    }

    public String getPromoId() {
        return promoId;
    }

    public void setPromoId(String promoId) {
        this.promoId = promoId;
    }

    public boolean isCanGoToGiftPool() {
        return canGoToGiftPool;
    }

    public void setCanGoToGiftPool(boolean canGoToGiftPool) {
        this.canGoToGiftPool = canGoToGiftPool;
    }

    public boolean isGiveUpGift() {
        return isGiveUpGift;
    }

    public void setGiveUpGift(boolean giveUpGift) {
        isGiveUpGift = giveUpGift;
    }
}
