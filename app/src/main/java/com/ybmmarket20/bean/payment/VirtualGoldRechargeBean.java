package com.ybmmarket20.bean.payment;

public class VirtualGoldRechargeBean {

    private String amountText;  //文案： “充1000元”
    private double amount; // 1000
    private String rightAmount; // 红包金额： 20
    private String rightText; //文案： 返20元红包
    private String isSelected; //1代表选中 0代表非 选中
    private String useCurrOrder; //1代表当前可用 本单可用

    public String getAmountText() {
        return amountText;
    }

    public void setAmountText(String amountText) {
        this.amountText = amountText;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getRightAmount() {
        return rightAmount;
    }

    public void setRightAmount(String rightAmount) {
        this.rightAmount = rightAmount;
    }

    public String getRightText() {
        return rightText;
    }

    public void setRightText(String rightText) {
        this.rightText = rightText;
    }

    public String getIsSelected() {
        return isSelected;
    }

    public boolean isSelected() {
        return "1".equals(isSelected);
    }

    public boolean isCanUse() {
        return "1".equals(useCurrOrder);
    }

    public void setIsSelected(String isSelected) {
        this.isSelected = isSelected;
    }

    public String getUseCurrOrder() {
        return useCurrOrder;
    }

    public void setUseCurrOrder(String useCurrOrder) {
        this.useCurrOrder = useCurrOrder;
    }
}
