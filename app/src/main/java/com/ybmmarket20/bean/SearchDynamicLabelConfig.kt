package com.ybmmarket20.bean

/**
 * 搜索动态筛选标签
 */
data class SearchDynamicLabelConfig(
//    val id: String?,  // 12.0.07 移除，用不到。新api也取不到
    val labelName: String?,//标签展示名
    val icon: String?="",//标签展示名
    val labelType: Int,//标签类型：1 单选 2 复选 3 弹框多选
    val paramKey: String?,//上送搜索入参key
    var selectedParamValue: String?,//选中的值  仅限labelType=1，2
    val unselectedParamValue: String?,//未选中的值  仅限labelType=1，2
    val labelGroupKey: String?,//埋点数据上报标签组key
    var isSelected: Int,//是否选中，1是0否
    // 12.0.07 新增：产地、中药扩展属性
    var labelKey:String?,    // 标签key，可作为唯一标识
    var selectedItemNamesStr:String?,   // 选中并回显的内容
    var popShow:Boolean = false,    // 非API字段：监听popWindow
    val items: List<SearchDynamicLabelItem>? = null, //下拉数据
    val isDynamicParam:Int, // 是否为动态参数(中药扩展属性) 1是 0否
    val dynamicParamKey:String?, // 动态参数key
) {
    /**
     * 当前是否是选中状态
     */
    fun getSelectedStatus(): Boolean = isSelected == 1

    fun setSelectedStatus(status: Boolean) {
        isSelected = if (status) 1 else 0
    }
    companion object{
        const val TYPE_POP = 3
    }
}

data class SearchDynamicLabelItem(var itemName: String, val itemKey:String, val itemCount: Int,private var isSelected: Int = 0){
    fun getSelectedStatus(): Boolean = isSelected == 1

    fun setSelectedStatus(status: Boolean) {
        isSelected = if (status) 1 else 0
    }
}