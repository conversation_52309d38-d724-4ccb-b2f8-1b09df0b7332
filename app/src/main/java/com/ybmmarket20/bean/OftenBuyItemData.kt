package com.ybmmarket20.bean

import com.ybm.app.bean.AbstractMutiItemEntity

//常购清单Item
const val OFTEN_BUY_ITEM = 0
//常购清单标题Item
const val OFTEN_BUY_RECOMMEND_TITLE_ITEM = 1
//常购清单推荐Item
const val OFTEN_BUY_RECOMMEND_ITEM = 2
//常购清单空Item
const val OFTEN_BUY_ITEM_EMPTY = 3

/**
 * <AUTHOR>
 * @description
 */
data class OftenBuyItemData(
    var rows: MutableList<OftenBuyItem>?,
    var requestParam: HashMap<String, String>?,
    var isEnd: Boolean,
    var sptype: String?,
    var spid: String?,
    var sid: String?
)

data class OftenBuyItem(
    var minPrice: Double,
    var maxPrice: Double,
    var masterStandardProductId: String?,
    var showName: String?,
    var imageUrl: String?,
    var skuId: String?,
    var manufacturer: String?,
    var purchaseNum: String?,
    var originalShowName: String?
): AbstractMutiItemEntity() {
    override fun getItemType(): Int = OFTEN_BUY_ITEM
}
