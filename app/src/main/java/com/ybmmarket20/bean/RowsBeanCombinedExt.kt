package com.ybmmarket20.bean

/**
 * <AUTHOR>
 * @desc    商品扩展：组合购、加价购-组合扩展信息
 * @date 2025/5/7
 */
data class RowsBeanCombinedExt(
    var isMainProduct: Boolean = false,  // 是否为主品
    var costPrice: Double = 0.0, // 到手价
    var price: String? = "0", // 单价
    var selectStatus: Int = 0,
    var discount: Double = 0.0, // 省 * 元
) : RowsBean(){
    var newQty:Int = 0 // 操作、传值使用：前端数量
    var qty: Int = 0    //数量
        set(value){
            field = value
            this.newQty = value
        }

    override fun getCombinationSelectedStatus(): Int {
        return selectStatus
    }
}

/**
 * 设置订单信息给当前实体
 */
fun RowsBeanCombinedExt.setOrderData(newBean: RowsBeanCombinedExt) {
    price = newBean.price
    costPrice = newBean.costPrice
    discount = newBean.discount
    selectStatus = newBean.selectStatus
    isMainProduct = newBean.isMainProduct
    qty = newBean.qty
}
