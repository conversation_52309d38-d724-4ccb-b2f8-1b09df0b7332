package com.ybmmarket20.home;

import static com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt.flowDataPageListPageExposureNew;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.apkfuns.logutils.LogUtils;
import com.flyco.tablayout.CommonTabLayout;
import com.flyco.tablayout.listener.CustomTabEntity;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.github.mzule.activityrouter.router.Routers;
import com.google.android.material.appbar.AppBarLayout;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.CategoryLevel2Adapter;
import com.ybmmarket20.bean.AdBagListBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.OneRowsBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.bean.SearchResultBean;
import com.ybmmarket20.bean.SortBean;
import com.ybmmarket20.bean.TabEntity;
import com.ybmmarket20.common.AdDialog2;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.common.LicenseStatusFragment;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.eventbus.C;
import com.ybmmarket20.common.eventbus.Event;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.report.coupon.CouponEntryType;
import com.ybmmarket20.report.coupon.ICouponEntryType;
import com.ybmmarket20.search.SpecFilterPopupWindow;
import com.ybmmarket20.search.SpecFilterPopupWindowTransparent;
import com.ybmmarket20.utils.AdapterUtils;
import com.ybmmarket20.utils.AuditStatusSyncUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.AllGoodsPopWindowRanking;
import com.ybmmarket20.view.BaseFilterPopWindow;
import com.ybmmarket20.view.CategoryLevel1AllPopWindow;
import com.ybmmarket20.view.FiltrateClassifyPop;
import com.ybmmarket20.view.LeftPopWindow;
import com.ybmmarket20.view.ListFilterPopWindow;
import com.ybmmarket20.view.MyFastScrollView;
import com.ybmmarketkotlin.adapter.GoodListAdapterNew;
import com.ybmmarketkotlin.adapter.GoodsListAdapterNewCategory;
import com.ybmmarketkotlin.utils.ExposureCallback;
import com.ybmmarketkotlin.utils.ExposureManager;

import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import butterknife.Bind;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * 全部商品,商品分类
 */
public class BrandFragment extends LicenseStatusFragment implements ICouponEntryType {

//    @Bind(R.id.ll_search)
//    LinearLayout llSearch;
//    @Bind(R.id.iv_code)
//    ImageView code;
//    @Bind(R.id.iv_voice)
//    ImageView voice;
//    @Bind(R.id.title_tv)
//    TextView titleTv;
//    @Bind(R.id.home_search_rl)
//    RelativeLayout homeSearchRl;
    @Bind(R.id.rb_comprehensive_ranking)
    TextView rbComprehensiveRanking;
    @Bind(R.id.brand_rg_01)
    LinearLayout brandRg01;
    @Bind(R.id.tv_shop)
    TextView tvShop;
    @Bind(R.id.appbar)
    AppBarLayout appbar;
    @Bind(R.id.cv_list)
    CommonRecyclerView rvList;
    @Bind(R.id.ly_search)
    LinearLayout lySearch;
    @Bind(R.id.iv_fast_scroll_brand)
    MyFastScrollView mIvFastScrollBrand;
    @Bind(R.id.rb_promotion)
    TextView mRbPromotion;
    @Bind(R.id.brand_ctl)
    CoordinatorLayout mBrandCtl;
//    @Bind(R.id.title_left)
//    RelativeLayout msgLayout;
//    @Bind(R.id.tv_smg_num)
//    TextView tvSmg;
//    @Bind(R.id.tv_smg_num_more)
//    TextView tvSmgMore;
    @Bind(R.id.iv_ad_suspension)
    ImageView adSuspension;
    //    @Bind(R.id.xTabLayout)
//    XTabLayout tab_layout_category;
    @Bind(R.id.rv_level2)
    RecyclerView rv_level2;//二级分类的列表
    @Bind(R.id.imageButton_category_level1_show_all)
    ImageButton imageButton_category_level1_show_all;
    @Bind(R.id.layout_load_error)
    ViewGroup layout_load_error;
    @Bind(R.id.ll_content_show)
    ViewGroup layout_content_show;
    @Bind(R.id.iv_tablayout_masking)
    ImageView iv_tablayout_masking;
    @Bind(R.id.ll_condition)
    ViewGroup ll_condition;
    @Bind(R.id.ctl)
    CommonTabLayout ctl;
    @Bind(R.id.rb_spec)
    RadioButton rbSpec;

    private boolean isSpecSelected = false;
    private SpecFilterPopupWindow specFilterPopupWindow;
    private String specFilterStr = "";

    private int tab = -1;
    private int sort = -1;
    private String name = "";

    private List<RowsBean> allProductComments = new ArrayList<>();
    private List<String> lastNames = new ArrayList<>();

    //    private int allProductPager = 0;
    public int style = 1;

    private String mEmptyStr;
    private boolean isFirstRefresh = true;//标记是否是第一次刷新
    private GoodListAdapterNew goodsAdapter;

    protected FiltrateClassifyPop mClassifyPop;
    private ListFilterPopWindow mPopWindowRanking;
//    private Manufacturers2Pop mPopManufacturer;

    public boolean isClassA = false;//甲类otc
    public boolean isClassB = false;//乙类otc
    public boolean isClassRx = false;//处方药rx
    public boolean isClassElse = false;//其他

    private boolean isAvailable = false, isPromotion = false, mIsManufacturer = false;
    private String priceRangeFloor = "", priceRangeTop = "";
    //排序
    private String property = "smsr.sale_num", propertyDescOrAsc = "desc", propertyName = "";
    // 二级分类id
    private String categoryId = "";
    //经营范围
    private String drugClassification = "";
    private String shop = "";//厂家

    private List<OneRowsBean> categoryList_level1;
    private List<OneRowsBean> categoryList_level2;
    private CategoryLevel2Adapter categoryLevel2Adapter;
    private CategoryLevel1AllPopWindow popWindowCategoryLevel1All;
    private int selectedCategoryLevel1Position = 0;
    private int selectedCategoryLevel2Position = -1;
    private int clickPosition = -1;//当前点击的pos

    private String selectedCategoryLevel1Str = "";
    private String selectedCategoryLevel2Str = "";

    private ArrayList<CustomTabEntity> mTabEntities = new ArrayList<>();
    private RequestParams preParams = null;//上次请求参数
    private String preCategoryId = null;
    private boolean isUpdateAnalysis = true;
    private String categoryIdForLevel2 = "";


    @Override
    public int getLayoutId() {
        return R.layout.fragment_brand;
    }

    @Override
    public ViewGroup.LayoutParams getRootLayoutParam() {
        return new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 1);
    }

    @Override
    protected boolean needSetStatusHeight() {
        return false;
    }

    @Override
    protected void initData(String content) {
        initReceiver();
        initView();
        setTabFilterColor();
        init1Classify();
//        initManufacturerPop();
        appbar.addOnOffsetChangedListener((appBarLayout, verticalOffset) -> {
            if (rvList != null) {
                rvList.setEnabled(verticalOffset == 0);
            }
        });
//        Message.instance.bindUnreadMsgCount(listener);

        requestGetAllCategoryData();//获取所有分类
        showSpecPopupWindow();
    }

    private void requestGetAllCategoryData() {
        showProgress();
        RequestParams requestParams = new RequestParams();
        requestParams.setUrl(AppNetConfig.GET_ALL_GOODS_CATEGORY);
        requestParams.put("merchantId", SpUtil.getMerchantid());
        HttpManager.getInstance().post(AppNetConfig.STAIR_SORTNET, requestParams, new BaseResponse<SortBean>() {
            @Override
            public void onSuccess(String content, BaseBean<SortBean> obj, SortBean sortBean) {
                //                rvList.setRefreshEnable(true);//分类加载完方可刷新 todo 该方法无效 不知为何
                layout_content_show.setVisibility(View.VISIBLE);
                layout_load_error.setVisibility(View.INVISIBLE);
                dismissProgress();
                if (obj != null && obj.isSuccess() && sortBean != null && sortBean.getCategoryList() != null) {

                    categoryList_level1 = sortBean.getCategoryList();
                    //先初始化RecyclerView后初始化TabLayout，先设置监听，后初始化数据，不要颠倒顺序
                    initLevel2RecyclerView();
                    initLevel1TabLayout(categoryList_level1);

                } else {
                    layout_content_show.setVisibility(View.INVISIBLE);
                    layout_load_error.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                layout_content_show.setVisibility(View.INVISIBLE);
                layout_load_error.setVisibility(View.VISIBLE);
            }
        });
    }

    private void initLevel2RecyclerView() {
        rv_level2.setLayoutManager(new WrapLinearLayoutManager(getContext()));
        categoryLevel2Adapter = new CategoryLevel2Adapter(R.layout.item_category_level2_1line, categoryList_level2);

        //左侧ListView的选中item回调
        categoryLevel2Adapter.setOnSelectedCallback(new CategoryLevel2Adapter.OnSelectedCallback() {
            @Override
            public void onSelectedCallback(int position,String content) {
                selectedCategoryLevel2Position = position;
                selectedCategoryLevel2Str = content;
                JgTrackBean jgTrackBean = goodsAdapter.getJgTrackBean();
                if (jgTrackBean != null){
                    jgTrackBean.setNavigation_1(selectedCategoryLevel1Str);
                    jgTrackBean.setNavigation_2(selectedCategoryLevel2Str);
                }
                goodsAdapter.setJgTrackBean(jgTrackBean);
                if (position == -1) {
                    allProductComments.clear();
                    //goodsAdapter.clearTranceData();
                    goodsAdapter.notifyDataChangedAfterLoadMore(false);
                    return;
                }
                categoryList_level2 = categoryLevel2Adapter.getData();
                if (position >= 0 && position < categoryList_level2.size()) {
                    // 二级菜单点击埋点
                    JSONObject jsonObject = new JSONObject();
                    try {
                        jsonObject.put("title", categoryList_level2.get(position).getName());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    XyyIoUtil.track(XyyIoUtil.ACTION_ALLDRUGS_CLASSTWO, jsonObject);

                    categoryId = categoryList_level2.get(position).getId() + "";
                    //goodsAdapter.setCategoryId(categoryId);
                    allProductComments.clear();
                    //goodsAdapter.clearTranceData();
                    goodsAdapter.notifyDataSetChanged();
                    getNewData(true, true);
                }
            }
        });

        categoryLevel2Adapter.setOnItemClickListener((adapter, view, position) ->
                categoryLevel2Adapter.setSelectedPositionAndNotify(position));

        rv_level2.setAdapter(categoryLevel2Adapter);
    }

    private void initLevel1TabLayout(List<OneRowsBean> sortCategorys) {
        if (ctl == null) {
            return;
        }

        if (Build.VERSION.SDK_INT >= 23) {
            changeListenerTabLayoutScroll();
        }
        if (mTabEntities.size() > 0) {
            mTabEntities.clear();
        }
        for (int i = 0; i < sortCategorys.size(); i++) {
            mTabEntities.add(new TabEntity(sortCategorys.get(i).getName()));
        }
        ctl.setTabData(mTabEntities);
        ctl.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                setOnTabSelect(position, sortCategorys);
            }

            @Override
            public void onTabReselect(int position) {

            }
        });
        setOnTabSelect(0, sortCategorys);
    }

    private void setOnTabSelect(int position, List<OneRowsBean> sortCategorys) {
        selectedCategoryLevel1Position = position;
        categoryList_level2 = categoryList_level1.get(selectedCategoryLevel1Position).getRows();

        // 一级菜单点击埋点
        JSONObject jsonObject = new JSONObject();
        try {
            selectedCategoryLevel1Str =
                    categoryList_level1.get(selectedCategoryLevel1Position).getName();
            if (categoryList_level2 != null && categoryList_level2.size()>0){
                selectedCategoryLevel2Str = categoryList_level2.get(0).name;
            }

            selectedCategoryLevel2Str =
            categoryIdForLevel2 = sortCategorys.get(position).getId() + "";
            JgTrackBean jgTrackBean = goodsAdapter.getJgTrackBean();
            if (jgTrackBean != null){
                jgTrackBean.setNavigation_1(selectedCategoryLevel1Str);
                jgTrackBean.setNavigation_2(selectedCategoryLevel2Str);
            }
            goodsAdapter.setJgTrackBean(jgTrackBean);

            jsonObject.put("title", sortCategorys.get(position).getName());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        XyyIoUtil.track(XyyIoUtil.ACTION_ALLDRUGS_CLASSONE, jsonObject);
        if (categoryList_level2 != null) {
            resetShop();
            categoryLevel2Adapter.setCallbackPositionReset();
            categoryLevel2Adapter.setSelectedPosition(0);
            categoryLevel2Adapter.setNewData(categoryList_level2);
        } else {
            categoryLevel2Adapter.setNewData(new ArrayList<OneRowsBean>());
            allProductComments.clear();
            //goodsAdapter.clearTranceData();
            goodsAdapter.notifyDataChangedAfterLoadMore(false);
        }
    }

    /**
     * 点击全部药品重置各个按钮状态
     */
    private void resetShop() {
        if (mClassifyPop == null) {
            return;
        }

        mClassifyPop.reset(true);
//        if (mPopManufacturer != null) {
//            mPopManufacturer.reset(true);
//        }

        if (lastNames == null) {
            lastNames = new ArrayList<>();
        }
        lastNames.clear();
        shop = "";

        isClassA = false;
        isClassB = false;
        isClassRx = false;
        isClassElse = false;

        isAvailable = false;
        isPromotion = false;

        mIsManufacturer = false;

        drugClassification = "";
        priceRangeFloor = "";
        priceRangeTop = "";

        tvShop.setActivated(false);
        tvShop.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));

        mRbPromotion.setActivated(isPromotion);
        mRbPromotion.setTypeface(isPromotion ? Typeface.defaultFromStyle(Typeface.BOLD) : Typeface.defaultFromStyle(Typeface.NORMAL));

    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    private void changeListenerTabLayoutScroll() {
        ctl.setOnScrollChangeListener(new View.OnScrollChangeListener() {
            @Override
            public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                if (v == null || v.getWidth() == 0) {
                    return;
                }
                if (scrollX >= v.getWidth()) {
                    //几乎是滑动到头了
                    iv_tablayout_masking.setImageResource(0);
                } else {
                    iv_tablayout_masking.setImageResource(R.drawable.icon_tablayout_masking);
                }
            }
        });
    }

    @Override
    protected boolean isRegisterEventBus() {
        return true;
    }

    @Override
    protected void receiveEvent(Event event) {
        if (event.getCode() == C.EventCode.SUBSCRIBE_SUCCEED) {
            boolean isSubscribe = (boolean) event.getData();
            if (isSubscribe) {
                if (goodsAdapter != null && clickPosition >= 0 && clickPosition < goodsAdapter.getItemCount()) {
                    Object item = goodsAdapter.getItem(clickPosition);
                    if (item instanceof RowsBean) {
                        RowsBean rowsBeanItem = (RowsBean) item;
                        rowsBeanItem.setBusinessType(1);//设置已经订阅状态
                        goodsAdapter.notifyDataSetChanged();
                    }
                }
            }
        }
    }

    private void initView() {
        mEmptyStr = "抱歉，暂无数据!";
        goodsAdapter = new GoodsListAdapterNewCategory(this, allProductComments, false);
        JgTrackBean jgTrackBean = new JgTrackBean(
                JGTrackManager.TrackAllDrugs.TRACK_URL,
                JGTrackManager.TrackAllDrugs.TITLE,
                JGTrackManager.TrackAllDrugs.TITLE,
                JGTrackManager.Common.MODULE_PRODUCT_LIST,
                JGTrackManager.TrackAllDrugs.PAGE_ID,
                JGTrackManager.TrackAllDrugs.TITLE,
                "首页(全部药品)-全部药品",
                "",
                JGTrackManager.TrackAllDrugs.TRACK_URL,
                1,
                null,
                1,
                "",
                "",
                "",
                ""
        );
        goodsAdapter.setJgTrackBean(jgTrackBean);
        goodsAdapter.setResourceViewTrackListener((rowsBean, integer,pageListCommonBean) -> {
            StringBuilder productTag = new StringBuilder();
            String productId = "";
            String productName = "";
            double productPrice = 0.0;
            String productType = "";
            String mEntrance = "";
            if (jgTrackBean != null && jgTrackBean.getEntrance() != null){
                mEntrance = jgTrackBean.getEntrance();
            }

            if (rowsBean != null) {
                if (rowsBean.getProductId() != null) {
                    productId = rowsBean.getProductId();
                }
                if (rowsBean.getProductName() != null) {
                    productName = rowsBean.getProductName();
                }
                productPrice = rowsBean.getJgProductPrice();
            }

            if (rowsBean != null){
                productType = rowsBean.getJgProductType();
            }

            if (rowsBean != null && rowsBean.tags != null && rowsBean.tags.productTags != null && rowsBean.tags.productTags.size() > 0) {
                for (int i = 0; i < rowsBean.tags.productTags.size(); i++) {
                    if (i != rowsBean.tags.productTags.size() - 1) {
                        productTag.append(rowsBean.tags.productTags.get(i).text).append("，");
                    } else {
                        productTag.append(rowsBean.tags.productTags.get(i).text);
                    }
                }
            }
            if (rowsBean != null && rowsBean.tags != null && rowsBean.tags.dataTags != null && rowsBean.tags.dataTags.size() > 0) {
                if (productTag.length()>0){
                    productTag.append("，");
                }
                for (int i = 0; i < rowsBean.tags.dataTags.size(); i++) {
                    if (i != rowsBean.tags.dataTags.size() - 1) {
                        productTag.append(rowsBean.tags.dataTags.get(i).text).append("，");
                    } else {
                        productTag.append(rowsBean.tags.dataTags.get(i).text);
                    }
                }
            }
            return null;
        });
        goodsAdapter.setProductClickTrackListener((rowsBean, integer,isBtnClick,mContent,number) -> {
            StringBuilder productTag = new StringBuilder();
            String productId = "";
            String productName = "";
            double productPrice = 0.0;
            String mEntrance = "";
            if (jgTrackBean != null && jgTrackBean.getEntrance() != null){
                mEntrance = jgTrackBean.getEntrance();
            }

            if (rowsBean != null) {
                if (rowsBean.getProductId() != null) {
                    productId = rowsBean.getProductId();
                }
                if (rowsBean.getProductName() != null) {
                    productName = rowsBean.getProductName();
                }
                productPrice = rowsBean.getJgProductPrice();
            }

            if (rowsBean != null && rowsBean.tags != null && rowsBean.tags.productTags != null && rowsBean.tags.productTags.size() > 0) {
                for (int i = 0; i < rowsBean.tags.productTags.size(); i++) {
                    if (i != rowsBean.tags.productTags.size() - 1) {
                        productTag.append(rowsBean.tags.productTags.get(i).text).append("，");
                    } else {
                        productTag.append(rowsBean.tags.productTags.get(i).text);
                    }
                }
            }
            if (rowsBean != null && rowsBean.tags != null && rowsBean.tags.dataTags != null && rowsBean.tags.dataTags.size() > 0) {
                if (productTag.length()>0){
                    productTag.append("，");
                }
                for (int i = 0; i < rowsBean.tags.dataTags.size(); i++) {
                    if (i != rowsBean.tags.dataTags.size() - 1) {
                        productTag.append(rowsBean.tags.dataTags.get(i).text).append("，");
                    } else {
                        productTag.append(rowsBean.tags.dataTags.get(i).text);
                    }
                }
            }
            JGTrackTopLevelKt.jgTrackResourceProductClick(
                    YBMAppLike.getAppContext(),
                    AppUtilKt.getFullClassName(this),
                    JGTrackManager.Common.MODULE_SEARCH_FEED,
                    AppUtilKt.getFullClassName(this),
                    JGTrackManager.TrackAllDrugs.PAGE_ID,
                    JGTrackManager.TrackAllDrugs.TITLE,
                    "",
                    "",
                    "",
                    integer,
                    productId,
                    productName,
                    "普通商品",
                    productPrice,
                    productTag.toString(),
                    mEntrance,
                    "",
                    ""
            );
            return null;
        });
//        goodsAdapter.setPreHot(true);
//        goodsAdapter.setTraceId("2");

        rvList.getRecyclerView().setMotionEventSplittingEnabled(false);
//        rvList.setEmptyView(R.layout.layout_empty_view_all_goods, R.drawable.icon_empty, mEmptyStr);
        goodsAdapter.setEmptyView(getNotNullActivity(), R.layout.layout_empty_view_all_goods, R.drawable.icon_empty, mEmptyStr);
        goodsAdapter.setEnableLoadMore(true);
        goodsAdapter.setShowUnderlinePrice(false);
        //        rvList.setRefreshEnable(false);//分类没有加载完不能刷新 todo 该方法无效
        rvList.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                if (!isFirstRefresh) {//第一次进来的时候也会执行该刷新方法，但是我们第一次进入不需要刷新
                    getNewData(false, false);
                } else {
                    isFirstRefresh = false;
                }
            }

            @Override
            public void onLoadMore() {
                getLoadMoreResponse();
            }
        });

//        goodsAdapter.setOnListItemClickListener(rows -> {
//            dismissPop();
//            if (rows != null) {
//                clickPosition = goodsAdapter.getCurrPosition();
//                openUrl("ybmpage://productdetail?" + IntentCanst.PRODUCTID + "=" + rows.getId());
//            }
//        });
        rvList.setOnScrollListener(new CommonRecyclerView.OnScrollListener() {
            @Override
            public void onScrollChanged(int x, int y) {

            }

            @Override
            public void onScrollRollingDistance(int y, int dy) {
                mIvFastScrollBrand.showFastScroll(rvList, y, dy, appbar);
            }

            @Override
            public void onScrollState(int i) {

            }

        });

        rvList.setAdapter(goodsAdapter);
        ExposureManager.INSTANCE.registerExposureListener(rvList.getRecyclerView(), new ExposureCallback() {
            @Override
            public void callback(@NonNull List<Integer> indexList) {
                int catePosition = -1;
                String cateText = "";
                try {
                    if (selectedCategoryLevel2Position >= 0) {
                        OneRowsBean oneRowsBean = categoryList_level2.get(selectedCategoryLevel2Position);
                        if (oneRowsBean != null) {
                            catePosition = selectedCategoryLevel2Position;
                            cateText = oneRowsBean.getName();
                        }
                    }
                } catch (Exception ignore) {
                }
                StringBuilder indexStr = new StringBuilder();
                for (int index : indexList) {
                    Object itemData = goodsAdapter.getItem(index);
                    BaseFlowData flowData = goodsAdapter.getFlowData();
                    if (flowData != null && itemData instanceof RowsBean) {
                        flowDataPageListPageExposureNew(flowData, ((RowsBean) itemData).getProductId(),
                                ((RowsBean) itemData).getShowName(), index, catePosition, cateText);
                        indexStr.append(index).append(" ");
                    }
                }
                Log.e("guan", "callback " + indexStr);
            }
        });
        if (tab > 0) {
            selecTab(tab, sort, name);
        }

    }

    /**
     * 设置动态的主题之后，tab需要跟着变化
     */
    private void setTabFilterColor() {
        if (YBMAppLike.isNewTheme) {
            rbComprehensiveRanking.setBackgroundResource(R.drawable.product_rb3_category_selector);
            tvShop.setTextColor(getResources().getColorStateList(R.color.selector_text_color_category_dynamic));
        }
//        YBMAppLike.changeThemeBg(R.drawable.base_header_dynamic_bg, llSearch, brandRg01);
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @OnClick({R.id.tv_shop,
             R.id.rb_comprehensive_ranking, R.id.rb_promotion,
             R.id.iv_ad_suspension, R.id.imageButton_category_level1_show_all,
            R.id.tv_reload, R.id.rb_spec})
    public void clickTab(View view) {
        switch (view.getId()) {
//            case R.id.iv_code:
//                XyyIoUtil.track(XyyIoUtil.ACTION_ALLDRUGS_SCAN);
//                RoutersUtils.open("ybmpage://captureactivity");
//                break;
//            case R.id.iv_voice:
//
//                HashMap content = new HashMap<String, String>();
//                content.put("entry", "search_brand");
//                content.put("merchantId", SpUtil.getMerchantid());
//                XyyIoUtil.track(XyyIoUtil.ACTION_VOICE_CLICK, content);
//
//                RoutersUtils.open("ybmpage://searchvoiceactivity");
//
//                break;
//            case R.id.home_search_rl://点击搜索框
////                ((BaseActivity) getNotNullActivity()).gotoAtivity(SearchProductActivity.class, null);
//                RoutersUtils.open(RouterConstantKt.ROUTER_SEARCH_PRODUCT + "?pageSource=" + IntentCanst.PAGE_SOURCE_CATEGORY_SEARCH);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ALLDRUGS_SEARCH);
//                break;
            //综合排序
            case R.id.rb_comprehensive_ranking:
                if (goodsAdapter == null || ll_condition == null) {
                    return;
                }
                ((BaseActivity) getActivity()).hideSoftInput();
                setActivated(R.id.rb_comprehensive_ranking);
                setIconState(rbComprehensiveRanking, R.drawable.manufacturers_new_checked);
                initPopComprehensiveRanking();
                mPopWindowRanking.show(ll_condition);
                break;
            //有促销
            case R.id.rb_promotion:
                if (goodsAdapter == null || brandRg01 == null) {
                    return;
                }
                boolean promotionActivated = mRbPromotion.isActivated();
                mRbPromotion.setActivated(!promotionActivated);
                mRbPromotion.setTypeface(!promotionActivated ? Typeface.defaultFromStyle(Typeface.BOLD) : Typeface.defaultFromStyle(Typeface.NORMAL));
                isPromotion = !promotionActivated;
                if (mClassifyPop != null) {
                    mClassifyPop.setPromotion(isPromotion);
                }
                mHandler.sendMessage(mHandler.obtainMessage(20));
                getNewData(true, true);
                break;
            //选择==>筛选
            case R.id.tv_shop:
                XyyIoUtil.track(XyyIoUtil.ACTION_ALLDRUGS_SCREEN);
                if (goodsAdapter == null || brandRg01 == null) {
                    return;
                }
                ((BaseActivity) getActivity()).hideSoftInput();
                tvShop.setActivated(true);
                tvShop.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                init1Classify();
                mClassifyPop.show();
                break;
//            case R.id.title_left://打开消息中心
//                XyyIoUtil.track(XyyIoUtil.ACTION_ALLDRUGS_MESSAGE);
//                Message.openMessagePage();
//                break;
            case R.id.iv_ad_suspension:
                getCoupon();
                break;
            case R.id.imageButton_category_level1_show_all://显示所有一级分类
                initPopCategoryLevel1All(selectedCategoryLevel1Position);
                popWindowCategoryLevel1All.show(imageButton_category_level1_show_all);
                break;
            case R.id.tv_reload://重新加载
                requestGetAllCategoryData();
                break;
            case R.id.rb_spec: //规格
                isSpecSelected = !isSpecSelected;
                rbSpec.setChecked(false);
                setIconState(rbSpec, isSpecSelected ? R.drawable.manufacturers_new_checked : R.drawable.manufacturers_new_def);
                if (isSpecSelected) {
                    //选中
                    showSpecPopupWindow();
                } else {
                    if (specFilterPopupWindow != null) specFilterPopupWindow.dismiss();
                }
                break;
        }
    }

    private Map<String, String> getSpecRequestMap() {
        Map<String, String> paramsMap = getRequestParams(false).getParamsMap();
        paramsMap.remove("spec");
        if (paramsMap.containsKey("categoryId")) {
            String srCategoryId = paramsMap.get("categoryId");
            if (srCategoryId == null) {
                srCategoryId = "";
            }
            paramsMap.put("categorySecondId", srCategoryId);
        }
        return paramsMap;
    }

    /**
     * 规格过滤
     */
    private void showSpecPopupWindow() {
        if (specFilterPopupWindow == null) {
            specFilterPopupWindow = new SpecFilterPopupWindowTransparent();
            specFilterPopupWindow.setSpecFilterMargin(UiUtils.sp2px(74), 0, 0, 0);
            Map<String, String> paramsMap = getSpecRequestMap();
            specFilterPopupWindow.loadData(paramsMap);
            specFilterPopupWindow.setListener(new SpecFilterPopupWindow.SpecFilterCallback() {
                @Override
                public void onGetData(@NotNull String filterStr) {
                    specFilterStr = filterStr;
                    rbSpec.setChecked(false);
                    getNewData(false, true);
                }

                @Override
                public void onDismiss() {
                    isSpecSelected = false;
                    setIconState(rbSpec, !TextUtils.isEmpty(specFilterStr) ? R.drawable.manufacturers_new_checked : R.drawable.manufacturers_new_def);
                }
            });
        } else {
            specFilterPopupWindow.show(brandRg01);
        }
    }

    //一级分类全部展示弹框
    private void initPopCategoryLevel1All(int currentPosition) {
        if (popWindowCategoryLevel1All == null) {
            popWindowCategoryLevel1All = new CategoryLevel1AllPopWindow();
            popWindowCategoryLevel1All.setNewData(categoryList_level1);
            popWindowCategoryLevel1All.setOnCallbackListener(new CategoryLevel1AllPopWindow.OnCallbackListener() {
                @Override
                public void onSelectedCallback(int position) {
                    ctl.setCurrentTab(position);
                    setOnTabSelect(position, categoryList_level1);
                }
            });
        } else {
            popWindowCategoryLevel1All.setNewData(categoryList_level1);
        }
        popWindowCategoryLevel1All.setSelectedPosition(currentPosition);
    }

    //获取首页天降礼包
    private void getCoupon() {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.HOME_COUPON).addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
        HttpManager.getInstance().post(params, new BaseResponse<AdBagListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<AdBagListBean> obj, AdBagListBean adDataBeans) {
//                if (titleTv == null) {
//                    return;
//                }
                if (obj != null && obj.isSuccess()) {
                    if (adDataBeans != null && adDataBeans.bagList != null && adDataBeans.bagList.size() > 0) {
                        AdDialog2.showDialog(adDataBeans);
                    }
                }
            }
        });
    }

    /**
     * 综合排序
     */
    private void initPopComprehensiveRanking() {
        if (mPopWindowRanking == null) {
            mPopWindowRanking = new AllGoodsPopWindowRanking();

            mPopWindowRanking.setOnSelectListener(new BaseFilterPopWindow.OnSelectListener() {
                @Override
                public void getValue(SearchFilterBean show) {
                    rbComprehensiveRanking.setText(show.nickname);
                    property = show.id;
                    propertyName = show.realName;
                    getNewData(true, true);
                }

                @Override
                public void OnDismiss(String multiSelectStr) {
                    if (TextUtils.isEmpty(property)) {
                        rbComprehensiveRanking.setText("综合排序");
                    }
                    setIconState(rbComprehensiveRanking, R.drawable.manufacturers_new_def);
                }
            });
        }
        if (SpUtil.isKa()) {
            mPopWindowRanking.setNewData(ListFilterPopWindow.getKaRanking());
        } else {
            mPopWindowRanking.setNewData(ListFilterPopWindow.getRanking());
        }
    }


    /*
     * 筛选一级分类
     * */
    private void init1Classify() {
        if (mClassifyPop == null) {
            mClassifyPop = new FiltrateClassifyPop();

            mClassifyPop.setListener(new LeftPopWindow.Listener<SearchFilterBean>() {

                @Override
                public void onResult(SearchFilterBean bean) {
                    isAvailable = bean.isAvailable;
                    isPromotion = bean.isPromotion;
                    drugClassification = setDrugsClass(bean);
                    priceRangeFloor = !TextUtils.isEmpty(bean.priceRangeFloor) ? bean.priceRangeFloor : "";
                    priceRangeTop = !TextUtils.isEmpty(bean.priceRangeTop) ? bean.priceRangeTop : "";
                    setManufacturer(bean);
                    getNewData(true, true);
                }

                @Override
                public void onDismiss() {
                    setManufacturersState();
                }
            });
        }
        mClassifyPop.setDataType("categoryId", categoryId);
        mClassifyPop.setSpec(specFilterStr);
        // ka用户隐藏价格区间选项
        mClassifyPop.hiddenPriceRange(SpUtil.isKa());
        //ka用户隐藏药帮忙服务下的有促销
        mClassifyPop.hiddenPromotion(isKaUser);
        mClassifyPop.hiddenShopService(true);
    }

//    /**
//     * 厂家
//     */
//    private void initManufacturerPop() {
//        if (mPopManufacturer == null) {
//            mPopManufacturer = new Manufacturers2Pop();
//            mPopManufacturer.setDataType(categoryId, "", isAvailable, isPromotion, isClassA
//                    , isClassB, isClassRx, isClassElse, priceRangeFloor, priceRangeTop, lastNames);
//            mPopManufacturer.setOnSelectListener(new BaseFilterPopWindow.OnSelectListener() {
//                @Override
//                public void getValue(final SearchFilterBean bean) {
//                    setManufacturer(bean);
//                    getNewData(true, true);
//                }
//
//                @Override
//                public void OnDismiss(String multiSelectStr) {
//                    setManufacturersState();
//                }
//            });
//        }
//    }

    /**
     * 设置厂家筛选后各个按钮的状态
     */
    private void setManufacturersState() {
        if (lastNames == null || lastNames.size() <= 0) {
            mIsManufacturer = false;
        } else {
            mIsManufacturer = true;
        }
        mRbPromotion.setActivated(isPromotion);
        mRbPromotion.setTypeface(isPromotion ? Typeface.defaultFromStyle(Typeface.BOLD) : Typeface.defaultFromStyle(Typeface.NORMAL));

        mHandler.sendMessage(mHandler.obtainMessage(20));
    }

    /**
     * 设置厂家过来的数据
     *
     * @param bean
     */
    private void setManufacturer(SearchFilterBean bean) {
        if (lastNames == null) {
            lastNames = new ArrayList<>();
        }
        lastNames.clear();
        lastNames.addAll(bean.lastNames);
        if (mClassifyPop != null) {
            mClassifyPop.setLastNames(lastNames);
        }
        StringBuilder sb = new StringBuilder();
        if (lastNames != null && lastNames.size() > 0) {
            for (int i = 0; i < lastNames.size(); i++) {
                sb.append(lastNames.get(i)).append("*");
            }
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }
        }
        shop = sb.toString();

    }

    /**
     * 设置箭头指示状态 →，→
     *
     * @param tv
     * @param id
     */
    private void setIconState(TextView tv, int id) {
        if (getNotNullActivity() != null && getNotNullActivity() instanceof BaseActivity) {
            Drawable nav_up = getNotNullActivity().getResources().getDrawable(id);
            nav_up.setBounds(0, 0, nav_up.getMinimumWidth(), nav_up.getMinimumHeight());
            tv.setCompoundDrawables(null, null, nav_up, null);
        }
    }

    /**
     * 设置选中状态
     *
     * @param id
     */
    public void setActivated(int id) {
        if (rbComprehensiveRanking == null) {
            return;
        }
        rbComprehensiveRanking.setActivated(R.id.rb_comprehensive_ranking == id);
    }

    /**
     * 拼接药品经营类型
     *
     * @param bean
     * @return
     */
    private String setDrugsClass(SearchFilterBean bean) {
        isClassA = bean.isClassA;
        isClassB = bean.isClassB;
        isClassRx = bean.isClassRx;
        isClassElse = bean.isClassElse;

        StringBuilder sb = new StringBuilder();
        if (isClassA) {
            sb.append("1").append(",");
        }
        if (isClassB) {
            sb.append("2").append(",");
        }
        if (isClassRx) {
            sb.append("3").append(",");
        }
        if (isClassElse) {
            sb.append("4").append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }


    private RequestParams searchMoreParams;

    /**
     * 请求参数
     *
     * @param loadMore
     * @return manufacturer => 厂家
     * drugClassification =>经营类型
     * isControl =>控销
     * activityType =>高毛
     */
    private RequestParams getRequestParams(boolean loadMore) {
        if (loadMore) {
            return searchMoreParams;
        }

        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        String prePageSource = IntentCanst.PAGE_SOURCE_CATEGORY_LIST;
        String pageUrl = "";
        if(getActivity() != null){
            pageUrl = getActivity().getIntent().getStringExtra(Routers.KEY_RAW_URL);
        }
        params.put("pageSource", IntentCanst.PAGE_SOURCE_CATEGORY_LIST + "_e" + categoryIdForLevel2);
        params.put("nsid", "");
        params.put("listoffset", "");
        params.put("listdata", "");
        params.put("pageurl", pageUrl);
        params.put("sptype", "2");
        /**
         * 规格
         */
        if (!TextUtils.isEmpty(specFilterStr)) {
            params.put("spec", specFilterStr);
        }

        if (!TextUtils.isEmpty(property)) {

            switch (propertyName) {
                //价格从低到高
                case "价格从低到高":
                    propertyDescOrAsc = "asc";
                    break;
                //价格从高到低
                case "价格从高到低":
                default:
                    propertyDescOrAsc = "desc";
            }
            params.put("property", property);
            params.put("direction", propertyDescOrAsc);
        }
        //厂家
        if (!TextUtils.isEmpty(shop)) {
            params.put("manufacturer", shop);
        }
        //经营类型
        if (!TextUtils.isEmpty(drugClassification)) {
            params.put("drugClassificationStr", drugClassification);
        }
        //全部分类 categoryId
        if (!TextUtils.isEmpty(categoryId)) {
            params.put("categoryId", categoryId);
        }
        //仅看有货
        if (isAvailable) {
            params.put("hasStock", "1");
        }
        //有促销
        if (isPromotion) {
            params.put("isPromotion", "1");
        }
        //价格区间-最低价
        if (!TextUtils.isEmpty(priceRangeFloor)) {
            params.put("minPrice", priceRangeFloor);
        }
        //价格区间-最高价
        if (!TextUtils.isEmpty(priceRangeTop)) {
            params.put("maxPrice", priceRangeTop);
        }
        if (preCategoryId != null && preCategoryId.equals(categoryId) && !isUpdateAnalysis) {
            // 更换categoryId认为是新的请求，添加spType、spId和sId到请求参数
            FlowDataAnalysisManagerKt.addAnalysisRequestParams(params, mFlowData);
        }
        return params;
    }

    /**
     * 下拉刷新
     *
     * @param isShowRefreshing 是否显示下拉刷新
     * @param isShowWaitDialog 是否显示加载进度
     */
    private void getNewData(boolean isShowRefreshing, boolean isShowWaitDialog) {
        if (isShowWaitDialog) {
            showProgress();
        }
        RequestParams params = getRequestParams(false);
        HttpManager.getInstance().post(isKaUser ? AppNetConfig.SORTNET_KA : AppNetConfig.SORTNET, params, new BaseResponse<SearchResultBean>() {

            @Override
            public void onSuccess(String content, BaseBean<SearchResultBean> obj, SearchResultBean brandBean) {
                dismissProgress();
                completion();
                if (null != obj) {
                    if (obj.isSuccess() && brandBean != null) {
                        if (specFilterPopupWindow != null) {
                            Map<String, String> paramsMap = getSpecRequestMap();
                            specFilterPopupWindow.loadDataOnly(paramsMap);
                        }
                        updateLicenseStatus(brandBean.licenseStatus, getCurrentLicenseStatusListener());
                        if (preCategoryId == null || !preCategoryId.equals(categoryId)) {
                            FlowDataAnalysisManagerKt.updateFlowData(mFlowData, brandBean.sptype, brandBean.spid, brandBean.sid, brandBean.nsid);
                        }
                        if (!requestParamsEqualsPre(preParams, params) || isUpdateAnalysis) {
                            // 当前获取列表的id与上一次不一致认为发起新的请求
                            FlowDataEventAnalysisKt.flowDataPageCommoditySearch(mFlowData);
                            goodsAdapter.setFlowData(mFlowData);
                            isUpdateAnalysis = false;
                        }
                        preParams = params;
                        preCategoryId = categoryId;
                        updateSearchData(true, brandBean);
                        RecyclerView.LayoutManager layoutManager = rvList.getLayoutManager();
                        if (layoutManager != null)
                            rvList.getRecyclerView().scrollToPosition(0);
                        appbar.setExpanded(true);//展开折叠的的AppBarLayout
                    } else {
                        if (goodsAdapter != null) {
                            goodsAdapter.setEnableLoadMore(false);
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                completion();
                dismissProgress();
                if (goodsAdapter != null) {
                    goodsAdapter.loadMoreFail();
                }
            }
        });
    }


    /**
     * 请求数据
     */
    private void getLoadMoreResponse() {
        // guanchong 药品分类
        HttpManager.getInstance().post(isKaUser ? AppNetConfig.SORTNET_KA : AppNetConfig.SORTNET, getRequestParams(true), new BaseResponse<SearchResultBean>() {

            @Override
            public void onSuccess(String content, BaseBean<SearchResultBean> obj, SearchResultBean brandBean) {

                completion();
                if (null != obj) {
                    if (obj.isSuccess() && brandBean != null) {
                        updateLicenseStatus(brandBean.licenseStatus, null);
                        updateSearchData(false, brandBean);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                completion();
                try {
                    if (rvList != null) {
                        rvList.getRecyclerView().stopScroll();
                        goodsAdapter.loadMoreFail();
                    }
                } catch (Throwable e) {
                    e.printStackTrace();
                }
            }
        });


    }

    /**
     * 请求搜索后的数据更新
     *
     * @param rowsBeans
     */
    private void updateSearchData(boolean isRefresh, SearchResultBean rowsBeans) {
        searchMoreParams = rowsBeans.getRequestParams();
        AuditStatusSyncUtil.getInstance().updateLicenseStatus(rowsBeans.licenseStatus, null, true);
        AdapterUtils.INSTANCE.addLocalTimeForRows(rowsBeans.rows);
        AdapterUtils.INSTANCE.notifyAndControlLoadmoreStatus(rowsBeans.rows, goodsAdapter, isRefresh, rowsBeans.isEnd);
        // 请求并更新折后价
        AdapterUtils.INSTANCE.getAfterDiscountPrice(rowsBeans.rows, goodsAdapter);
        ExposureManager.INSTANCE.increaseEatResetTargetCount();
    }


    private void completion() {
        if (rvList == null) {
            return;
        }
        rvList.setRefreshing(false);
    }


    @SuppressLint("HandlerLeak")
    Handler mHandler = new Handler() {
        @Override
        public void handleMessage(android.os.Message msg) {
            super.handleMessage(msg);
            if (msg.what == 10) {
                style = (int) msg.obj;
            } else if (msg.what == 20) {

                boolean isShop = lastNames.size() <= 0 && !isAvailable && !isPromotion && TextUtils.isEmpty(priceRangeFloor) && TextUtils.isEmpty(priceRangeTop) && !isClassA && !isClassB &&
                        !isClassRx && !isClassElse;

                //boolean isAll = TextUtils.isEmpty(id) && isShop;

                tvShop.setActivated(!isShop);
                tvShop.setTypeface(!isShop ? Typeface.defaultFromStyle(Typeface.BOLD) : Typeface.defaultFromStyle(Typeface.NORMAL));

            }
        }
    };

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            this.onPause();
        } else {
            //刷新当前列表数据 情景一：当购物车删除商品后，该界面需要跟着刷新
            goodsAdapter.notifyDataSetChanged();
            setAdShow();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        //refreshList();//刷新当前列表数据
//        if (isKaUser) {//ka用户隐藏有促销 消息
////            mRbPromotion.setVisibility(View.INVISIBLE);
//            msgLayout.setVisibility(View.GONE);
//        } else {
////            mRbPromotion.setVisibility(View.VISIBLE);
//            msgLayout.setVisibility(View.VISIBLE);
//        }
        setAdShow();
    }

    private void setAdShow() {
        if (SpUtil.readInt("show_ad_collect_pop", 0) == 1) {
            adSuspension.setVisibility(View.GONE);
            return;
        }
        adSuspension.setVisibility(View.VISIBLE);
    }

    @Override
    public void onStop() {
        super.onStop();
        dismissPop();
    }

    /*
     * pop-dismiss弹窗
     * */
    private void dismissPop() {
        if (mClassifyPop != null && mClassifyPop.isShow()) {
            mClassifyPop.dismissPop();
        }
    }

    @Override
    public void handleLicenseStatusChange(int status) {
//        getNewData(false, false);
    }

    @Override
    public boolean onLicenseStatusEnable() {
        return true;
    }

    @NonNull
    @Override
    public String getCouponEntryType() {
        return CouponEntryType.COUPON_ENTRY_TYPE_CATEGORY;
    }


    public static class MyItemDecoration extends RecyclerView.ItemDecoration {
        private int space = ConvertUtils.dp2px(1);

        public MyItemDecoration() {
        }

        private int row = 1;

        public void setRow(int row) {
            this.row = row;
        }

        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            if (row <= 0) {
                outRect.bottom = 0;
                outRect.left = 0;
                outRect.top = 0;
                outRect.right = 0;
                return;
            }
            if (row == 1) {
                outRect.top = 0;
                outRect.left = space;
                outRect.right = space;
                outRect.bottom = 0;
            }
            if (row == 2) {
                outRect.bottom = space * 10;

                if (parent.getChildLayoutPosition(view) % 2 == 0) {
                    outRect.left = space * 10;
                    outRect.right = space * 5;
                } else {
                    outRect.left = space * 5;
                    outRect.right = space * 10;
                }

                if (parent.getChildLayoutPosition(view) < row) {
                    outRect.top = space * 10;
                } else {
                    outRect.top = 0;
                }

            }

        }
    }

    public void selecTab(int index, int sortIndex, final String nameStr) {
        this.tab = index;
        this.sort = sortIndex;
        this.name = nameStr;
        LogUtils.d("tab:" + tab + " sort:" + sort + " name:" + name);
    }

    private void initReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.ACTION_BRAND_SET);
        intentFilter.addAction(IntentCanst.ACTION_AD_COLLECT_POP);
        intentFilter.addAction(IntentCanst.ACTION_AD_COLLECT_HINT_POP);
        LocalBroadcastManager.getInstance(getNotNullActivity().getApplicationContext()).registerReceiver(mRefreshBroadcastReceiver, intentFilter);
    }

    /*
     * 广播
     * */
    private BroadcastReceiver mRefreshBroadcastReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (IntentCanst.ACTION_BRAND_SET.equals(action)) {
                requestGetAllCategoryData();
            } else if (IntentCanst.ACTION_AD_COLLECT_POP.equals(intent.getAction())) {
                adSuspension.setVisibility(View.VISIBLE);
            } else if (IntentCanst.ACTION_AD_COLLECT_HINT_POP.equals(intent.getAction())) {
                adSuspension.setVisibility(View.GONE);
            }
        }
    };

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        ButterKnife.unbind(this);
        if (mRefreshBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(getNotNullActivity().getApplicationContext()).unregisterReceiver(mRefreshBroadcastReceiver);
        }
//        Message.instance.releaseListener(listener);
    }

//    private Message.Listener listener = new Message.Listener() {
//        @Override
//        public void countChange(int count) {
//
//            Message.showMsgCount(count, tvSmg, tvSmgMore);
//
//        }
//    };

    /**
     * 判断当前请求参数与上次请求的keyWord和筛选条件是否一致
     *
     * @param preParams 上次请求的参数
     * @param curParams 当前请求的参数
     * @return true 一致
     */
    private boolean requestParamsEqualsPre(RequestParams preParams, RequestParams curParams) {
        if (preParams == null) return false;
        return preParams.equalsParamValues(curParams,
                "property",
                "direction",
                "manufacturer",
                "drugClassificationStr",
                "categoryId",
                "hasStock",
                "isPromotion",
                "minPrice",
                "maxPrice");
    }

    /**
     * 是否需要更新埋点参数
     *
     * @param isUpdateAnalysis true: 需要更新 false: 不需要更新
     */
    public void setUpdateAnalysisStatus(boolean isUpdateAnalysis) {
        this.isUpdateAnalysis = isUpdateAnalysis;
    }

}
