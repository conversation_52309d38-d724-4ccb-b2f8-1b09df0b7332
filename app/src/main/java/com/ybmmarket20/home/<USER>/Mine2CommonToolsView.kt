package com.ybmmarket20.home.mine

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.UseToolsBean
import com.ybmmarket20.home.mine.bean.Mine2HeaderItemContainer
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.xyyreport.page.mine.MineReport
import kotlinx.android.synthetic.main.view_mine2_common_tools.view.*

/**
 * 常用工具
 */
class Mine2CommonToolsView(context: Context, attr: AttributeSet?) :
    AbsMine2BaseView<List<UseToolsBean>>(context, attr) {

    private val commonToolsData = mutableListOf<UseToolsBean>()
    private val commonToolsAdapter by lazy {
        Mine2CommonToolsAdapter(commonToolsData)
    }

    override fun getLayoutId(): Int = R.layout.view_mine2_common_tools

    override fun initialize() {
        super.initialize()
        mine2_common_tools_progress.setScrollListener(rv_common_tools)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun setData(container: Mine2HeaderItemContainer<List<UseToolsBean>>) {
        if (checkDataChange(commonToolsData, container.entry)) return
        commonToolsData.clear()
        container.entry?.let { commonToolsData.addAll(it) }
        rv_common_tools.layoutManager =
            GridLayoutManager(context, 2, GridLayoutManager.HORIZONTAL, false)
        if (rv_common_tools.adapter == null) {
            rv_common_tools.adapter = commonToolsAdapter
        } else {
            commonToolsAdapter.notifyDataSetChanged()
        }
    }

    /**
     * 检查新老数据是否变化
     */
    private fun checkDataChange(orgData: List<UseToolsBean>?, newData: List<UseToolsBean>?):Boolean {
        if (orgData == null || newData == null) return false
        if (orgData.size != newData.size) return false
        return (orgData zip newData).all {
            it.first == it.second
        }
    }

    inner class Mine2CommonToolsAdapter(data: List<UseToolsBean>):
        YBMBaseAdapter<UseToolsBean>(R.layout.item_mine2_common_tool, data) {

        private val itemWidth = (UiUtils.getScreenWidth() - UiUtils.dp2px(20)) / 5

        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: UseToolsBean?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                ImageUtil.load(context, bean.imgUrl, holder.getView(R.id.iv_icon))
                holder.setText(R.id.tv_name, bean.name)
                holder.itemView.setOnClickListener {
                    MineReport.clickCommonToolItem(mContext, holder.bindingAdapterPosition, bean.name)
                    RoutersUtils.open(bean.jumpUrl)
                    XyyIoUtil.track(bean.mdKey, hashMapOf("index" to "${holder.bindingAdapterPosition}"))
                    MineFragment2.jgTrackBtnClick(context,"下服务区", bean.mdKey ?:"")
                }
                bean.tips?.also {
                    if (it.type == 1) {
                        //数字
                        holder.setVisible(R.id.tv_dot, true)
                        holder.setVisible(R.id.tv_dot_text, false)
                        holder.setText(R.id.tv_dot, it.content)
                    } else if (it.type == 2) {
                        //文本
                        holder.setVisible(R.id.tv_dot, false)
                        holder.setVisible(R.id.tv_dot_text, true)
                        holder.setText(R.id.tv_dot_text, it.content)
                    }
                }
                val lp = holder.itemView.layoutParams as RecyclerView.LayoutParams
                lp.width = itemWidth
                holder.itemView.layoutParams = lp

            }
        }

    }

}