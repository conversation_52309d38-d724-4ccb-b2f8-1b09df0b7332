package com.ybmmarketkotlin.views.combinedbuy

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.xyy.canary.utils.DensityUtil
import com.ybmmarket20.R
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarket20.utils.SpanUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarketkotlin.adapter.GoodsCombinedBuyItemAdapter

/**
 * <AUTHOR>
 * @desc    加价购区域
 * @date    2025/5/6
 */
class CombinedBuyMultiLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, def: Int = 0) :
    ConstraintLayout(context, attrs, def) {
    val adapter by lazy {
        GoodsCombinedBuyItemAdapter(mGroupPurchaseInfo?.subProducts)
    }
    private val rlvGoods by lazy { findViewById<RecyclerView>(R.id.rlvGoods) }
    private val tvTotalPrice by lazy { findViewById<TextView>(R.id.tvTotalPrice) }
    private val tvTotalCount by lazy { findViewById<TextView>(R.id.tvTotalCount) }
    private val tvDiscount by lazy { findViewById<TextView>(R.id.tvDiscount) }
    private val tvSettle by lazy { findViewById<TextView>(R.id.tvSettle) }
    var mListener: CombinedBuyListener? = null
        set(value) {
            field = value
            adapter.mListener = object : CombinedBuyListener {
                override fun changeNum(bean: RowsBeanCombinedExt, curSubPosition: Int, addFlag: Boolean, preNum: Int) {
                    field?.changeNum(bean, curSubPosition, addFlag, preNum)
                }

                override fun changeNumClick(bean: RowsBeanCombinedExt, curSubPosition: Int, addFlag: Boolean, preNum: Int) {
                    field?.changeNumClick(bean, curSubPosition, addFlag, preNum)
                }

                override fun jumpToGoodsDetail(bean: RowsBeanCombinedExt) {
                    field?.jumpToGoodsDetail(bean)
                }
            }
            adapter.mListener = value
        }

    private var mGroupPurchaseInfo: GroupPurchaseInfo? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_combined_buy_multi, this)
        initView()
    }

    private fun initView() {
        rlvGoods.adapter = adapter
        tvSettle.setOnClickListener {
            mListener?.preSettle()
        }
    }

    private fun upTotalData() {
        if(mGroupPurchaseInfo!!.realPay.isNullOrEmpty()){
            tvSettle.alpha = 0.5f
            tvSettle.isEnabled = false
        }else{
            tvSettle.alpha = 1f
            tvSettle.isEnabled = true
        }
        val priceSplit = UiUtils.transform(mGroupPurchaseInfo!!.realPay ?: "0.00").split(".")
        tvTotalPrice.text = SpanUtils()
            .append("￥").setFontSize(DensityUtil.dip2px(context, 12f))
            .setBold()
            .append(priceSplit[0])
            .setBold()
            .setFontSize(DensityUtil.dip2px(context, 18f))
            .apply {
                if (priceSplit.size > 1) {
                    this.append(".${priceSplit[1]}")
                    this.setBold()
                    this.setFontSize(DensityUtil.dip2px(context, 14f))
                }
            }
            .create()
        tvTotalCount.text = mGroupPurchaseInfo!!.tips ?: "已选3盒20元"
        if (mGroupPurchaseInfo!!.combinedDiscountSub == null || mGroupPurchaseInfo!!.combinedDiscountSub == 0.0) {
            tvDiscount.visibility = View.GONE
        } else {
            tvDiscount.visibility = View.VISIBLE
            tvDiscount.text = "已优惠:￥${UiUtils.transform(mGroupPurchaseInfo!!.combinedDiscountSub!!)}"
        }
    }

    /**
     * 设置商品
     */
    fun setNewData(bean: GroupPurchaseInfo) {
        mGroupPurchaseInfo = bean
        adapter.mGroupFlag = false
        adapter.setNewData(mGroupPurchaseInfo!!.subProducts)
        upTotalData()
    }
}