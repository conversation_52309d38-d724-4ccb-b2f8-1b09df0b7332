package com.ybmmarketkotlin.adapter

import com.xyy.canary.utils.LogUtil
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuyListener
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuySingleLayout
import okhttp3.internal.notifyAll

/**
 * <AUTHOR>
 * @desc    大搜组合购
 * @date    2025/5/6
 */
class GoodsCombinedBuySingleAdapter(purchaseInfos: MutableList<GroupPurchaseInfo>?) :
    YBMBaseListAdapter<GroupPurchaseInfo>(R.layout.adapter_combinedbuy_single, purchaseInfos) {
    var mListener: CombinedBuyListener? = null
    var loadMoreFlag:Boolean = false

    public override fun bindItemView(baseViewHolder: YBMBaseHolder, purchaseInfo: GroupPurchaseInfo) {
        LogUtil.e("groupPurchase","bindItemView:"+purchaseInfo.realPay)
        val layoutSingle = baseViewHolder.itemView.findViewById<CombinedBuySingleLayout>(R.id.layoutSingle)
        if(!loadMoreFlag && layoutSingle.curSubPos != 0){
            layoutSingle.curSubPos = 0
        }
        layoutSingle.setNewData(purchaseInfo)
        layoutSingle.mListener = mListener
    }

    public override fun bindItemView(baseViewHolder: YBMBaseHolder, t: GroupPurchaseInfo, payloads: List<Any?>) {
        payloads.forEach {
            it?:return@forEach
            val layoutSingle = baseViewHolder.itemView.findViewById<CombinedBuySingleLayout>(R.id.layoutSingle)
            layoutSingle.setNewData(it as GroupPurchaseInfo)
        }
    }

}