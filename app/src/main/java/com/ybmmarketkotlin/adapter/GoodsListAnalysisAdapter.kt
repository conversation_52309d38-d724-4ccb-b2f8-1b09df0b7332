package com.ybmmarketkotlin.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.bean.SearchRowsBean
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.xyyreport.page.search.SearchProductReport

/**
 * 商品列表埋点
 */
abstract class GoodsListAnalysisAdapter<T>(
    data: MutableList<SearchRowsBean>
) :
    YBMBaseMultiItemAdapter<SearchRowsBean>(data) {

    private val mCacheRecord = mutableSetOf<Int>()

    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SearchRowsBean) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            if (!mCacheRecord.contains(holder.bindingAdapterPosition)) {
                mCacheRecord.add(holder.bindingAdapterPosition)
                if (bean.productInfo == null) return@whenAllNotNull
                SearchProductReport.trackSearchGoodsExposure(
                    mContext,
                    bean.productInfo,
                    holder.bindingAdapterPosition
                )
            }
        }
    }

    fun clearCacheRecord() {
        mCacheRecord.clear()
    }

}