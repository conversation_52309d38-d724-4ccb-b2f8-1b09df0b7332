apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'
//极光分析全埋点插件 'com.analysys.android.plugin'
apply plugin: 'com.analysys.android.plugin'
//QT埋点插件
//apply plugin: 'com.quick.analytics.plugin'

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    defaultConfig {
        applicationId "com.ybmmarket20"
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        multiDexEnabled true
        resConfigs rootProject.ext.android.resConfigs
        versionCode = (project.hasProperty("versionCode") && project.versionCode != null && !("".equals(project.versionCode)) && Integer.parseInt(project.versionCode) > 0) ? Integer.parseInt(project.versionCode) : 12010
        versionName = (project.hasProperty("versionName") && project.versionName != null && !("".equals(project.versionName))) ? project.versionName : "release V12.0.10"
        ndk {
            // 设置支持的 SO 库构架
            abiFilters 'arm64-v8a','armeabi-v7a'//, 'x86_64'
        }
        manifestPlaceholders = [
                JPUSH_CHANNEL: "developer-default", //暂时填写默认值即可.
        ]
//        buildFeatures {
//            // Enables Jetpack Compose for this module
//            compose true
//        }

        composeOptions {
            kotlinCompilerExtensionVersion '1.2.0'
        }
//        javaCompileOptions {
//            annotationProcessorOptions {
//                includeCompileClasspath = true
//            }
//        }

    }


    signingConfigs {
        ybm {
            keyAlias '小药药'
            keyPassword 'ybmmarket20'
            storeFile file('ybm.jks')
            storePassword 'ybmmarket20'
        }
    }
    lintOptions {
        abortOnError Boolean.valueOf(modulesLintAbortOnError)
        ignoreWarnings Boolean.valueOf(modulesLintIgnoreWarnings)
        if (Boolean.valueOf(modulesLintBaseLineEnable)) {
            baseline file("lint-baseline.xml")
        }
        lintConfig file("$rootDir/lint.xml")
    }
    dexOptions {
        jumboMode = true
    }

    buildTypes {
        release {//线上版本
            minifyEnabled true
            zipAlignEnabled true
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.ybm
            buildConfigField "String", "bugly_app_id", '"9ebbec1f6e"'
            buildConfigField "String", "bugly_app_key", '"e65c4e53-9035-4101-839c-8f015d835cae"'
            manifestPlaceholders = [JPUSH_PKGNAME  : "com.ybmmarket20", JPUSH_APPKEY: "808b05619bc76f8b083c1f8a", package_name: "com.ybmmarket20", app_name: "@string/app_name", app_logo: "@drawable/logo"
                                    , file_provider: "com.ybmmarket20.fileprovider",
                                    XMPUSH_APPKEY  : "\\5541750838421", XMPUSH_APPID: "\\2882303761517508421",
                                    HWPUSH_APPID   : "appid=100075867",
                                    BaiduMap_AK    : "OfItGg1jpgTfKAtoKAe1dm0Pzi5ePl0a",
                                    OPPOPUSH_APPKEY: "E3kfRAOx1y8KcKsg8Cg80c4w8", OPPOPUSH_APPSECRET: "e47ded7FeF2662201c76Da7770BB227e",
                                    VIVOPUSH_APPKEY: "98a6c4ed7d1473501967dcd0487b28f5", VIVOPUSH_APPID: "105192356",
                                    HONORPUSH_APPID: "104492132"]
            //小米appkey和appid****请务必在数值前添加"\\"，否则长数字取值会null****
            debuggable false
            jniDebuggable false
        }
        debug {//开发版本
            minifyEnabled false
            zipAlignEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.ybm
            buildConfigField "String", "bugly_app_id", '"9ebbec1f6e"'
            buildConfigField "String", "bugly_app_key", '"e65c4e53-9035-4101-839c-8f015d835cae"'
            applicationIdSuffix '.debug'//修改包名
            manifestPlaceholders = [JPUSH_PKGNAME  : "com.ybmmarket20.debug", JPUSH_APPKEY: "4218114842f5a887421cf613", package_name: "com.ybmmarket20.debug", app_name: "@string/app_name_debug", app_logo: "@drawable/logo_debug"
                                    , file_provider: "com.ybmmarket20.debug.fileprovider",
                                    XMPUSH_APPKEY  : "\\5721853980400", XMPUSH_APPID: "\\2882303761518539400",
                                    HWPUSH_APPID   : "appid=102652791",
                                    //OPPOPUSH_APPKEY:"8e1e4c79273c461493fbd1b81c6e681b",OPPOPUSH_APPSECRET:"e00a994acf7347f2a7b2d8e3212b797b"]//测试
                                    BaiduMap_AK    : "4usfviaasFX0ffsNntQQFlLP7HElxxNo",
                                    OPPOPUSH_APPKEY: "E3kfRAOx1y8KcKsg8Cg80c4w8", OPPOPUSH_APPSECRET: "e47ded7FeF2662201c76Da7770BB227e",
                                    VIVOPUSH_APPKEY: "98a6c4ed7d1473501967dcd0487b28f5", VIVOPUSH_APPID: "105192356",
                                    HONORPUSH_APPID: "104492142"]
            // 小米appkey和appid****请务必在数值前添加"\\"，否则长数字取值会null****
            debuggable true
            jniDebuggable true
        }
    }

    compileOptions {
        sourceCompatibility rootProject.ext.android.javaVersion
        targetCompatibility rootProject.ext.android.javaVersion
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
        freeCompilerArgs += ['-Xskip-metadata-version-check']
    }

    dataBinding {
        enabled = true
    }

//    viewBinding{
//        enabled = true
//    }

}

// 为每个构建变体注册复制任务
android.applicationVariants.all { variant ->
    def buildType = variant.buildType.name
    def taskName = "copy${buildType.capitalize()}Json"

    // 创建复制任务
    def copyTask = tasks.register(taskName, Copy) {
        description = "Copies ${buildType} JSON file to app directory"
        from "json/${buildType}/mcs-services.json"
        into projectDir // 目标路径为app根目录
        rename { "mcs-services.json" } // 重命名为目标文件名
        doFirst {
            println "Copying ${buildType} JSON file to app directory..."
        }
    }

    // 在构建前执行复制任务
    variant.preBuildProvider.get().dependsOn(copyTask)
}


repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation files('libs/Msc.jar')
    implementation files('libs/xyy-app-rsa-1.0.jar')
//    implementation files('libs/zxing.jar')
    implementation 'com.google.zxing:core:3.0.0'
    implementation 'com.google.zxing:javase:3.0.0'
    implementation files('libs/open_sdk_3.5.5.79_ra741783e_lite.jar')
    // 百度地图jar包 + liblocSDK8a.so
//    implementation files('libs/BaiduLBS_Android.jar')
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Location:9.1.8'
    implementation 'com.alipay.sdk:alipaysdk-android:15.8.11'
    api 'com.tencent.mm.opensdk:wechat-sdk-android-without-mta:6.6.4'

    api "com.jeremyliao:live-event-bus-x:1.7.3"

//    implementation(name: 'xyyio-1.0.2', ext: 'aar')
    implementation 'com.android.xyy:push:1.1.3'

    /*baseapp通用库*/
    implementation deps.appcompatV7
    implementation deps.supportV4
    implementation deps.recyclerview
    implementation deps.constraintLayout

    //implementation deps.design
    implementation deps.okhttp
    implementation deps.gson
    implementation deps.adapterhelper
    implementation deps.logutils

    implementation deps.glide
    implementation files('libs/tbs_sdk_thirdapp_v4.3.0.326_44226_sharewithdownloadwithfile_withoutGame_obfs_20220825_155943.jar')
    kapt 'com.github.bumptech.glide:compiler:4.11.0'
    //annotationProcessor 'com.github.bumptech.glide:compiler:4.11.0'

    implementation deps.photoview
    implementation deps.butterknife
    implementation deps.activityrouter
    implementation deps.suspensionIndexBar

    /*app库*/
    implementation('com.github.niorgai:StatusBarCompat:2.1.3', {
        exclude group: 'com.android.support'
    })
    kapt 'com.github.mzule.activityrouter:compiler:1.1.7'
    implementation 'cn.jiguang.sdk:jpush:4.3.0'
    implementation 'cn.jiguang.sdk:jcore:2.9.0'

    implementation(name: 'zzplayerlibrary', ext: 'aar')
    implementation 'com.jakewharton.timber:timber:4.6.0'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"

    implementation 'com.scwang.smart:refresh-layout-kernel:2.0.1'      //核心必须依赖
    implementation 'com.scwang.smart:refresh-header-classics:2.0.1'    //经典刷新头
    implementation 'com.scwang.smart:refresh-footer-classics:2.0.1'    //经典加载

    implementation('com.androidkun:XTabLayout:1.1.4', {
        exclude group: 'com.android.support'
    })
    implementation project(path: ':common')
    implementation project(path: ':tablayout')
    implementation project(path: ':push')
    implementation project(path: ':YBMBaseLib')
    implementation project(path: ':picture_library')
    implementation project(path: ':module_report')
    implementation project(path: ':xyyReport')


    api "com.gyf.immersionbar:immersionbar:2.3.3-beta15"
    api "com.squareup.retrofit2:retrofit:2.9.0"
    api "com.squareup.retrofit2:converter-gson:2.9.0"
    // rxandroid 2.1.1 dependencies rxjava2.2.6
    api 'com.contrarywind:Android-PickerView:4.1.8'
    implementation 'com.google.android:flexbox:2.0.1'
    implementation 'com.github.xiaohaibin:XBanner:1.8.9'

    // 直播和IM相关 6.0.3
    implementation 'com.shuyu:gsyVideoPlayer-java:6.0.3'
    implementation 'com.shuyu:gsyVideoPlayer-arm64:6.0.3'
    implementation 'com.shuyu:gsyVideoPlayer-armv7a:6.0.3'
    implementation 'com.tencent.imsdk:imsdk:4.8.1'

//    implementation 'com.aliyun.ams:alicloud-android-httpdns:2.2.2'

    implementation 'com.github.barteksc:android-pdf-viewer:2.8.2'
//    implementation project(path: ':flutter')
//    implementation project(':XYYContainer')
//    implementation 'com.android.ybm.flutter:flutter_release:1.0.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.3.9'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.4.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.0'

//    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.7'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.0'
//navcontroller
//    implementation 'androidx.navigation:navigation-fragment-ktx:2.3.3'
//    implementation 'androidx.navigation:navigation-ui-ktx:2.3.3'
//lifecycle
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.4.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.0'
//material design
    implementation 'com.google.android.material:material:1.3.0'

    implementation deps.androidxActivityJava
    implementation deps.androidxActivityKotlin
    implementation deps.androidxFragmentJava
    implementation deps.androidxFragmentKotlin
    //升级
    implementation deps.ybmAppupdate
    implementation deps.mmkv
    implementation 'jsc.kit.wheel:wheel-view:0.5.0'

    // 基础依赖包，必须要依赖
    implementation 'com.geyifeng.immersionbar:immersionbar:3.2.2'
    // kotlin扩展（可选）
    implementation 'com.geyifeng.immersionbar:immersionbar-ktx:3.2.2'
    compileOnly files('runtime/sdk.jar')
    implementation files('libs/andpermission.aar')
    implementation files('libs/AndroidPermissionX-1.0.0.aar')
    implementation files('libs/kyb_sdk_AndroidX-release_20220520104848_sec.aar')
    implementation files('libs/PabrSdk-naga-3.20.0-androidx.aar')
    implementation files('libs/lib_wwapi-2.0.12.11.aar')
    implementation "androidx.biometric:biometric-ktx:1.2.0-alpha05"

    //NewBieGuide引导层
    implementation 'com.github.huburt-Hu:NewbieGuide:v2.4.0'

    //banner
    implementation 'io.github.youth5201314:banner:2.2.2'
    //极光埋点 含全埋点等多个模块
    implementation deps.jgAnalysysVersion

    implementation 'com.lxj:xpopup:2.0.8-rc5'
    api 'com.tencent.tdos-diagnose:diagnose:0.4.11'
    api 'com.tencent.tdos-diagnose:logger:0.4.11'
//    annotationProcessor 'javax.xml.bind:jaxb-api:2.3.1'
//    annotationProcessor 'com.sun.xml.bind:jaxb-core:2.3.0.1'
//    annotationProcessor 'com.sun.xml.bind:jaxb-impl:2.3.2'
//    kapt 'javax.xml.bind:jaxb-api:2.3.1'
//    kapt 'com.sun.xml.bind:jaxb-core:2.3.0.1'
//    kapt 'com.sun.xml.bind:jaxb-impl:2.3.2'

//    implementation("androidx.compose.ui:ui:1.2.0")
//    // Tooling support (Previews, etc.)
//    implementation("androidx.compose.foundation:foundation:1.2.0")
//    // Integration with activities
//    implementation 'androidx.activity:activity-compose:1.5.1'
//    // Compose Material Design
//    implementation 'androidx.compose.material:material:1.2.0'
//    // Animations
//    implementation 'androidx.compose.animation:animation:1.2.0'
//    // Tooling support (Previews, etc.)
//    implementation 'androidx.compose.ui:ui-tooling:1.2.0'
//    // Integration with ViewModels
//    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.5.1'
//    // When using a AppCompat theme
//    implementation "com.google.accompanist:accompanist-appcompat-theme:0.25.0"
}


