首页动态模块分类
=
   * 无产品数据  id 1开头                          id

    1.广告轮播,可以单个图片（没有轮播与指示点）    1000  重复出现
    2.快捷入口（多行，多列）                     1001 不支持高度
    3.头条轮播                                 1002 不支持高度
    4.图片块 支持1,2、3、4张图片 支持上下与左右排列  1003  重复出现

   * 产品数据分类 id 2开头

    5.巨便宜 （可以横向滑动，和不要活动时间）     2000 重复出现
    6.限时特价                                2001

模块通用属性 ModuleView


    1.bgRes 背景色 #777777 或者 图片url,默认白色
    2.title 标题
    3.titleRes 背景色 #777777 或者 图片url,默认白色
    4.titleColor 标题色 #777777
    5.titleSize 标题大小 16sp 单位 sp                    | int型
    6.padding[left,top,rigth,bottom] 内边距 单位dp       | int型数组
    7.action 块点击事件
    8.api 模块条目列表数据请求的地址 http://114.55.2.1/xyy-app/app/findPatchVersion？key1=2&key2=34
    9.heigth 块的高度                                    | int型
    10.style 样式 上下排列 圆角                            | int型
    11.moduleId 模块id                                  | int型
    12 items 模块条目数据                                 |List<T>
    13 titleGravity 标题对齐方式     左中右                |int型
    15 titleMargin 标题外边距[left,top,rigth,bottom]       |int型数组
    15 margin 外边距[left,top,rigth,bottom]               |int型数组

模块条目通用属性（不包含产品数据模块条目）ModuleViewItem

    1.text 文案 没有不显示
    2.imgUrl 图片url
    3.action 点击跳转 没有点击没有响应

接口

    1.首页动态模块列表集合 List<ModuleView> modules
    2.每个模块中条目数据请求接口 4个非产品 List<ModuleViewItem> 1个产品通用 List<RowsBean> list，目前只有产品数据的接口，其它四个都没有